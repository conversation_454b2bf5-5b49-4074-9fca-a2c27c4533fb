@page "/counter/{param?}"
@using BlazorApp_KzApp.Components.PageUI.NewUpload

@code {

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public string param { get; set; } = "";
}

<Main_Framework PageValue="@param" LeftMenusVisible="false">

    <h2>
        test:
        @CObj.PageValue
    </h2>

    <MudUpload />

    <title>Counter @currentCount</title>

    <h1>Counter</h1>

    <p role="status">Current count: @currentCount</p>

    <button class="btn btn-primary" @onclick="IncrementCount">Click me</button>   

</Main_Framework>

@code {
    private int currentCount = 0;

    private void IncrementCount()
    {
        currentCount++;
    }
}
