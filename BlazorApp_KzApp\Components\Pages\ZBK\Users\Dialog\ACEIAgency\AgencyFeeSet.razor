@code {  
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]  
    public eZeroCore.Users.User TargetUserObj { get; set; } = new();  

    protected override async Task OnInitializedAsync()  
    {  
        await Task.CompletedTask;  
    }  
}  

<div class="row">  
    <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum._3"  
                       Title="@nameof(eZeroCore.Users.User.ACEIAgencyFee.StudentFee_0_10)">  
        <input maxlength="10" type="number"
               @bind-value=@(TargetUserObj.Settings.ACEIAgencyFeeSet.StudentFee_0_10) class="form-control" />  
    </_UI_Col_Framework>
    <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum._3"
                       Title="@nameof(eZeroCore.Users.User.ACEIAgencyFee.StudentFee_11_30)">
        <input maxlength="10" type="number"
               @bind-value=@(TargetUserObj.Settings.ACEIAgencyFeeSet.StudentFee_11_30) class="form-control" />
    </_UI_Col_Framework>
    <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum._3"
                       Title="@nameof(eZeroCore.Users.User.ACEIAgencyFee.StudentFee_31_50)">
        <input maxlength="10" type="number"
               @bind-value=@(TargetUserObj.Settings.ACEIAgencyFeeSet.StudentFee_31_50) class="form-control" />
    </_UI_Col_Framework>
    <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum._3"
                       Title="@nameof(eZeroCore.Users.User.ACEIAgencyFee.StudentFee_51_80)">
        <input maxlength="10" type="number"
               @bind-value=@(TargetUserObj.Settings.ACEIAgencyFeeSet.StudentFee_51_80) class="form-control" />
    </_UI_Col_Framework>
    <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum._3"
                       Title="@nameof(eZeroCore.Users.User.ACEIAgencyFee.StudentFee_MoreThen80)">
        <input maxlength="10" type="number"
               @bind-value=@(TargetUserObj.Settings.ACEIAgencyFeeSet.StudentFee_MoreThen80) class="form-control" />
    </_UI_Col_Framework>

</div>
<div class="row">
    <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum._3"
                       Title="@nameof(eZeroCore.Users.User.ACEIAgencyFee.TeacherFeeYr_Lv1)">
        <input maxlength="10" type="number"
               @bind-value=@(TargetUserObj.Settings.ACEIAgencyFeeSet.TeacherFeeYr_Lv1) class="form-control" />
    </_UI_Col_Framework>

    <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum._3"
                       Title="@nameof(eZeroCore.Users.User.ACEIAgencyFee.TeacherFeeYr_Lv2)">
        <input maxlength="10" type="number"
               @bind-value=@(TargetUserObj.Settings.ACEIAgencyFeeSet.TeacherFeeYr_Lv2) class="form-control" />
    </_UI_Col_Framework>

    <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum._3"
                       Title="@nameof(eZeroCore.Users.User.ACEIAgencyFee.TeacherFeeYr_Lv3)">
        <input maxlength="10" type="number"
               @bind-value=@(TargetUserObj.Settings.ACEIAgencyFeeSet.TeacherFeeYr_Lv3) class="form-control" />
    </_UI_Col_Framework>
</div>