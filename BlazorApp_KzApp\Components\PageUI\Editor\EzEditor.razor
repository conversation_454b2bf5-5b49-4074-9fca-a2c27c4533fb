@using TinyMCE.Blazor

@code {

    [CascadingParameter]
    public BlzHelperObj CObj { get; set; }

    [Parameter]
    public string Content { get; set; } = "";
    [Parameter]
    public EventCallback<string> ContentChanged { get; set; }
    [Parameter]
    public int Height { get; set; } = 500;
    [Parameter]
    public bool Sticky { get; set; } = true;
    [Parameter]
    public bool AllowUploadImage { get; set; } = true;
    [Parameter]
    public bool AllowSourceCode { get; set; }
    [Parameter]
    public bool AutoResize { get; set; }

    eZeroCore.Web.BlazorServerUI.TinyMCE TinyMCEOpt { get; set; } = new();

    protected override Task OnParametersSetAsync()
    {
        TinyMCEOpt = new eZeroCore.Web.BlazorServerUI.TinyMCE(Height, Sticky, AutoResize, AllowSourceCode)
            {
                AllowUploadImage = AllowUploadImage,
                Chinese = CObj.UserLang.LangTypeEnum == AppLang.LangType.Zh_Cn
            };

        return base.OnInitializedAsync();
    }

    public string ContentBind
    {
        get => Content;
        set
        {
            if (Content != value)
            {
                Content = value;
                ContentChanged.InvokeAsync(value);
            }
        }
    }
}

<Editor @bind-Value="ContentBind"
        ScriptSrc=@TyEditorArgs.ScriptSrc
        JsConfSrc=@TyEditorArgs.JsConfSrc
        Conf="@(TinyMCEOpt.GetEditorConf())"
        ApiKey=@TyEditorArgs.APIKey />


@code {
    public class TyEditorArgs
    {
        public static string ScriptSrc = "/lib/tinymce/tinymce.min.js";
        public static string JsConfSrc = eZeroCore.Web.BlazorServerUI.TinyMCE.GetJsConSrc();
        public static string APIKey = eZeroCore.Web.BlazorServerUI.TinyMCE.GetTinyMCEAPIKey();
    }

}