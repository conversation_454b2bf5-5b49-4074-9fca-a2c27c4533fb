@code {

    [CascadingParameter]    
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public EventCallback<eZeroCore.Users.UserGroup> OnTreeViewSelected { get; set; }

    [Parameter]
    public HashSet<TreeItemData<eZeroCore.Users.UserGroup?>> TreeItems { get; set; } = new();

    eZeroCore.Users.UserGroup? _ActivatedValue;

    eZeroCore.Users.UserGroup? SelectedItem
    {
        get
        {
            if (_ActivatedValue is null)
            {
                _ActivatedValue = TreeItems.FirstOrDefault()?.Value;
            }
            return _ActivatedValue;
        }
        set
        {
            _ActivatedValue = value;
            OnTreeViewSelected.InvokeAsync(value);
        }
    }

    string? getIcon(eZeroCore.Users.UserGroup? org)
    {
        if (org is not null && org.IsLocked)
        {
            return Icons.Material.Filled.Lock;
        }
        return null;
    }

}

<MudTreeView @bind-SelectedValue=SelectedItem Items="TreeItems" Hover="true" ExpandOnClick="true">
    <ItemTemplate>
        <MudTreeViewItem @bind-Expanded="@context.Expanded"
                         Items="@context.Children"
                         Value="@context.Value"
                         Text="@context.Text"
                         Class="@(context.Value is not null && context.Value.IsHide?"opacity-25":"")"
                         Icon="@(getIcon(context.Value))"
                         IconColor=" Color.Inherit"
                         T="@eZeroCore.Users.UserGroup"
                         EndText="@($"{(context.Value?.UserCount>0?context.Value?.UserCount:"")}")"
                         EndTextTypo="@Typo.caption" />
    </ItemTemplate>
</MudTreeView>