{"version": 3, "file": "project-details.js", "sources": ["../../../src/js/theme/charts/echarts/echarts-utils.js", "../../../src/js/theme/charts/echarts/completed-task-chart.js", "../../../src/js/theme/charts/echarts/top-coupons-chart.js", "../../../src/js/pages/project-details.js"], "sourcesContent": ["// import * as echarts from 'echarts';\r\nconst { merge } = window._;\r\n\r\n// form config.js\r\nexport const echartSetOption = (\r\n  chart,\r\n  userOptions,\r\n  getDefaultOptions,\r\n  responsiveOptions\r\n) => {\r\n  const { breakpoints, resize } = window.phoenix.utils;\r\n  const handleResize = options => {\r\n    Object.keys(options).forEach(item => {\r\n      if (window.innerWidth > breakpoints[item]) {\r\n        chart.setOption(options[item]);\r\n      }\r\n    });\r\n  };\r\n\r\n  const themeController = document.body;\r\n  // Merge user options with lodash\r\n  chart.setOption(merge(getDefaultOptions(), userOptions));\r\n\r\n  const navbarVerticalToggle = document.querySelector(\r\n    '.navbar-vertical-toggle'\r\n  );\r\n  if (navbarVerticalToggle) {\r\n    navbarVerticalToggle.addEventListener('navbar.vertical.toggle', () => {\r\n      chart.resize();\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    });\r\n  }\r\n\r\n  resize(() => {\r\n    chart.resize();\r\n    if (responsiveOptions) {\r\n      handleResize(responsiveOptions);\r\n    }\r\n  });\r\n  if (responsiveOptions) {\r\n    handleResize(responsiveOptions);\r\n  }\r\n\r\n  themeController.addEventListener(\r\n    'clickControl',\r\n    ({ detail: { control } }) => {\r\n      if (control === 'phoenixTheme') {\r\n        chart.setOption(window._.merge(getDefaultOptions(), userOptions));\r\n      }\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    }\r\n  );\r\n};\r\n// -------------------end config.js--------------------\r\n\r\nconst echartTabs = document.querySelectorAll('[data-tab-has-echarts]');\r\nif (echartTabs) {\r\n  echartTabs.forEach(tab => {\r\n    tab.addEventListener('shown.bs.tab', e => {\r\n      const el = e.target;\r\n      const { hash } = el;\r\n      const id = hash || el.dataset.bsTarget;\r\n      const content = document.getElementById(id.substring(1));\r\n      const chart = content?.querySelector('[data-echart-tab]');\r\n      if (chart) {\r\n        window.echarts.init(chart).resize();\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport const tooltipFormatter = (params, dateFormatter = 'MMM DD') => {\r\n  let tooltipItem = ``;\r\n  params.forEach(el => {\r\n    tooltipItem += `<div class='ms-1'>\r\n        <h6 class=\"text-body-tertiary\"><span class=\"fas fa-circle me-1 fs-10\" style=\"color:${\r\n          el.borderColor ? el.borderColor : el.color\r\n        }\"></span>\r\n          ${el.seriesName} : ${\r\n      typeof el.value === 'object' ? el.value[1] : el.value\r\n    }\r\n        </h6>\r\n      </div>`;\r\n  });\r\n  return `<div>\r\n            <p class='mb-2 text-body-tertiary'>\r\n              ${\r\n                window.dayjs(params[0].axisValue).isValid()\r\n                  ? window.dayjs(params[0].axisValue).format(dateFormatter)\r\n                  : params[0].axisValue\r\n              }\r\n            </p>\r\n            ${tooltipItem}\r\n          </div>`;\r\n};\r\n\r\nexport const handleTooltipPosition = ([pos, , dom, , size]) => {\r\n  // only for mobile device\r\n  if (window.innerWidth <= 540) {\r\n    const tooltipHeight = dom.offsetHeight;\r\n    const obj = { top: pos[1] - tooltipHeight - 20 };\r\n    obj[pos[0] < size.viewSize[0] / 2 ? 'left' : 'right'] = 5;\r\n    return obj;\r\n  }\r\n  return null; // else default behaviour\r\n};\r\n", "import { echartSetOption } from './echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Total Sales                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst completedTaskChartInit = () => {\r\n  const { getColor, getData, getDates } = window.phoenix.utils;\r\n\r\n  const $totalSalesChart = document.querySelector(\r\n    '.echart-completed-task-chart'\r\n  );\r\n\r\n  const dates = getDates(\r\n    new Date('5/1/2022'),\r\n    new Date('5/30/2022'),\r\n    1000 * 60 * 60 * 24\r\n  );\r\n\r\n  const currentMonthData = [\r\n    50, 115, 180, 180, 180, 150, 120, 120, 120, 120, 120, 240, 240, 240, 240,\r\n    270, 300, 330, 360, 390, 340, 290, 310, 330, 350, 320, 290, 330, 370, 350\r\n  ];\r\n\r\n  const prevMonthData = [\r\n    130, 130, 130, 90, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 110, 170,\r\n    230, 230, 230, 270, 310, 270, 230, 260, 290, 320, 280, 280, 280\r\n  ];\r\n  const tooltipFormatter = params => {\r\n    const currentDate = window.dayjs(params[0].axisValue);\r\n    const prevDate = window.dayjs(params[0].axisValue).subtract(1, 'month');\r\n\r\n    const result = params.map((param, index) => ({\r\n      value: param.value,\r\n      date: index > 0 ? prevDate : currentDate,\r\n      color: param.color\r\n    }));\r\n\r\n    let tooltipItem = ``;\r\n    result.forEach((el, index) => {\r\n      tooltipItem += `<h6 class=\"fs-9 text-body-tertiary ${\r\n        index > 0 && 'mb-0'\r\n      }\"><span class=\"fas fa-circle me-2\" style=\"color:${el.color}\"></span>\r\n      ${el.date.format('MMM DD')} : ${el.value}\r\n    </h6>`;\r\n    });\r\n    return `<div class='ms-1'>\r\n              ${tooltipItem}\r\n            </div>`;\r\n  };\r\n\r\n  if ($totalSalesChart) {\r\n    const userOptions = getData($totalSalesChart, 'echarts');\r\n    const chart = window.echarts.init($totalSalesChart);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [getColor('primary'), getColor('info')],\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: 10,\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter,\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      xAxis: [\r\n        {\r\n          type: 'category',\r\n          data: dates,\r\n          axisLabel: {\r\n            formatter: value => window.dayjs(value).format('DD MMM'),\r\n            interval: 13,\r\n            showMinLabel: true,\r\n            showMaxLabel: false,\r\n            color: getColor('secondary-color'),\r\n            align: 'left',\r\n            fontFamily: 'Nunito Sans',\r\n            fontWeight: 600,\r\n            fontSize: 12.8\r\n          },\r\n          axisLine: {\r\n            show: true,\r\n            lineStyle: {\r\n              color: getColor('secondary-bg')\r\n            }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: true,\r\n            interval: 0,\r\n            lineStyle: {\r\n              color: getColor('secondary-bg')\r\n            }\r\n          },\r\n          boundaryGap: false\r\n        },\r\n        {\r\n          type: 'category',\r\n          position: 'bottom',\r\n          data: dates,\r\n          axisLabel: {\r\n            formatter: value => window.dayjs(value).format('DD MMM'),\r\n            interval: 130,\r\n            showMaxLabel: true,\r\n            showMinLabel: false,\r\n            color: getColor('secondary-color'),\r\n            align: 'right',\r\n            fontFamily: 'Nunito Sans',\r\n            fontWeight: 600,\r\n            fontSize: 12.8\r\n          },\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          boundaryGap: false\r\n        }\r\n      ],\r\n      yAxis: {\r\n        position: 'right',\r\n        axisPointer: { type: 'none' },\r\n        axisTick: 'none',\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        axisLine: { show: false },\r\n        axisLabel: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'd',\r\n          type: 'line',\r\n          // data: Array.from(Array(30).keys()).map(() =>\r\n          //   getRandomNumber(100, 300)\r\n          // ),\r\n          data: currentMonthData,\r\n          showSymbol: false,\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'e',\r\n          type: 'line',\r\n          // data: Array.from(Array(30).keys()).map(() =>\r\n          //   getRandomNumber(100, 300)\r\n          // ),\r\n          data: prevMonthData,\r\n          // symbol: 'none',\r\n          lineStyle: {\r\n            type: 'dashed',\r\n            width: 1,\r\n            color: getColor('info')\r\n          },\r\n          showSymbol: false,\r\n          symbol: 'circle'\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 2,\r\n        left: 5,\r\n        bottom: '20px',\r\n        top: '2%',\r\n        containLabel: false\r\n      },\r\n      animation: false\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default completedTaskChartInit;\r\n", "// import * as echarts from 'echarts';\r\nimport { echartSetOption } from './echarts-utils';\r\n\r\nconst { echarts } = window;\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                                Market Share                                */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst topCouponsChartInit = () => {\r\n  const { getData, getColor } = window.phoenix.utils;\r\n  const ECHART_TOP_COUPONS = '.echart-top-coupons';\r\n  const $echartTopCoupons = document.querySelector(ECHART_TOP_COUPONS);\r\n\r\n  if ($echartTopCoupons) {\r\n    const userOptions = getData($echartTopCoupons, 'options');\r\n    const chart = echarts.init($echartTopCoupons);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        getColor('primary'),\r\n        getColor('primary-lighter'),\r\n        getColor('info-dark')\r\n      ],\r\n\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position(pos, params, el, elRect, size) {\r\n          const obj = { top: pos[1] - 35 }; // set tooltip position over 35px from pointer\r\n          if (window.innerWidth > 540) {\r\n            if (pos[0] <= size.viewSize[0] / 2) {\r\n              obj.left = pos[0] + 20; // 'move in right';\r\n            } else {\r\n              obj.left = pos[0] - size.contentSize[0] - 20;\r\n            }\r\n          } else {\r\n            obj[pos[0] < size.viewSize[0] / 2 ? 'left' : 'right'] = 0;\r\n          }\r\n          return obj;\r\n        },\r\n        formatter: params => {\r\n          return `<strong>${params.data.name}:</strong> ${params.percent}%`;\r\n        },\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      legend: { show: false },\r\n      series: [\r\n        {\r\n          name: '72%',\r\n          type: 'pie',\r\n          radius: ['100%', '87%'],\r\n          avoidLabelOverlap: false,\r\n          emphasis: {\r\n            scale: false,\r\n            itemStyle: {\r\n              color: 'inherit'\r\n            }\r\n          },\r\n          itemStyle: {\r\n            borderWidth: 2,\r\n            borderColor: getColor('body-bg')\r\n          },\r\n          label: {\r\n            show: true,\r\n            position: 'center',\r\n            formatter: '{a}',\r\n            fontSize: 23,\r\n            color: getColor('light-text-emphasis')\r\n          },\r\n          data: [\r\n            { value: 7200000, name: 'Percentage discount' },\r\n            { value: 1800000, name: 'Fixed card discount' },\r\n            { value: 1000000, name: 'Fixed product discount' }\r\n          ]\r\n        }\r\n      ],\r\n      grid: { containLabel: true }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default topCouponsChartInit;\r\n", "import completedTaskChartInit from '../theme/charts/echarts/completed-task-chart';\r\nimport topCouponsChartInit from '../theme/charts/echarts/top-coupons-chart';\r\n\r\nconst { docReady } = window.phoenix.utils;\r\ndocReady(completedTaskChartInit);\r\ndocReady(topCouponsChartInit);\r\n"], "names": [], "mappings": ";;;;;EAAA;EACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;AAC3B;EACA;EACO,MAAM,eAAe,GAAG;EAC/B,EAAE,KAAK;EACP,EAAE,WAAW;EACb,EAAE,iBAAiB;EACnB,EAAE,iBAAiB;EACnB,KAAK;EACL,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACvD,EAAE,MAAM,YAAY,GAAG,OAAO,IAAI;EAClC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;EACzC,MAAM,IAAI,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE;EACjD,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;AAC3D;EACA,EAAE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,aAAa;EACrD,IAAI,yBAAyB;EAC7B,GAAG,CAAC;EACJ,EAAE,IAAI,oBAAoB,EAAE;EAC5B,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,MAAM;EAC1E,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;EACrB,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,MAAM,CAAC,MAAM;EACf,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;EACnB,IAAI,IAAI,iBAAiB,EAAE;EAC3B,MAAM,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACtC,KAAK;EACL,GAAG,CAAC,CAAC;EACL,EAAE,IAAI,iBAAiB,EAAE;EACzB,IAAI,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACpC,GAAG;AACH;EACA,EAAE,eAAe,CAAC,gBAAgB;EAClC,IAAI,cAAc;EAClB,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK;EACjC,MAAM,IAAI,OAAO,KAAK,cAAc,EAAE;EACtC,QAAQ,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;EAC1E,OAAO;EACP,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK;EACL,GAAG,CAAC;EACJ,CAAC,CAAC;EACF;AACA;EACA,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;EACvE,IAAI,UAAU,EAAE;EAChB,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,IAAI;EAC5B,IAAI,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI;EAC9C,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;EAC1B,MAAM,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;EAC1B,MAAM,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE,aAAa,CAAC,mBAAmB,CAAC,CAAC;EAChE,MAAM,IAAI,KAAK,EAAE;EACjB,QAAQ,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;EAC5C,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;EACL;;ECvEA;EACA;EACA;AACA;EACA,MAAM,sBAAsB,GAAG,MAAM;EACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC/D;EACA,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,aAAa;EACjD,IAAI,8BAA8B;EAClC,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG,QAAQ;EACxB,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC;EACxB,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC;EACzB,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EACvB,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,gBAAgB,GAAG;EAC3B,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC7E,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,aAAa,GAAG;EACxB,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EACnE,GAAG,CAAC;EACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;EAC1D,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC5E;EACA,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM;EACjD,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;EACxB,MAAM,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,WAAW;EAC9C,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;EACxB,KAAK,CAAC,CAAC,CAAC;AACR;EACA,IAAI,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;EACzB,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,KAAK;EAClC,MAAM,WAAW,IAAI,CAAC,mCAAmC;AACzD,QAAQ,KAAK,GAAG,CAAC,IAAI,MAAM;AAC3B,OAAO,gDAAgD,EAAE,EAAE,CAAC,KAAK,CAAC;AAClE,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,CAAC;AAC/C,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,CAAC;AACZ,cAAc,EAAE,WAAW,CAAC;AAC5B,kBAAkB,CAAC,CAAC;EACpB,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,gBAAgB,EAAE;EACxB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;EAC7D,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACxD;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;EACpD,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,EAAE;EACnB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,SAAS,EAAE,gBAAgB;EACnC,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EACpE,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,YAAY,EAAE,IAAI;EAC9B,YAAY,YAAY,EAAE,KAAK;EAC/B,YAAY,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC9C,YAAY,KAAK,EAAE,MAAM;EACzB,YAAY,UAAU,EAAE,aAAa;EACrC,YAAY,UAAU,EAAE,GAAG;EAC3B,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,QAAQ,EAAE,CAAC;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,aAAa;EACb,WAAW;EACX,UAAU,WAAW,EAAE,KAAK;EAC5B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,QAAQ,EAAE,QAAQ;EAC5B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EACpE,YAAY,QAAQ,EAAE,GAAG;EACzB,YAAY,YAAY,EAAE,IAAI;EAC9B,YAAY,YAAY,EAAE,KAAK;EAC/B,YAAY,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC9C,YAAY,KAAK,EAAE,OAAO;EAC1B,YAAY,UAAU,EAAE,aAAa;EACrC,YAAY,UAAU,EAAE,GAAG;EAC3B,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,WAAW,EAAE,KAAK;EAC5B,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,EAAE,OAAO;EACzB,QAAQ,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACrC,QAAQ,QAAQ,EAAE,MAAM;EACxB,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,GAAG;EACnB,UAAU,IAAI,EAAE,MAAM;EACtB;EACA;EACA;EACA,UAAU,IAAI,EAAE,gBAAgB;EAChC,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,GAAG;EACnB,UAAU,IAAI,EAAE,MAAM;EACtB;EACA;EACA;EACA,UAAU,IAAI,EAAE,aAAa;EAC7B;EACA,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,QAAQ;EAC1B,YAAY,KAAK,EAAE,CAAC;EACpB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,WAAW;EACX,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,MAAM;EACtB,QAAQ,GAAG,EAAE,IAAI;EACjB,QAAQ,YAAY,EAAE,KAAK;EAC3B,OAAO;EACP,MAAM,SAAS,EAAE,KAAK;EACtB,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECrLD;AAEA;EACA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;AAC3B;EACA;EACA;EACA;AACA;EACA,MAAM,mBAAmB,GAAG,MAAM;EAClC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,kBAAkB,GAAG,qBAAqB,CAAC;EACnD,EAAE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;AACvE;EACA,EAAE,IAAI,iBAAiB,EAAE;EACzB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;EAC9D,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAClD;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,iBAAiB,CAAC;EACnC,QAAQ,QAAQ,CAAC,WAAW,CAAC;EAC7B,OAAO;AACP;EACA,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;EAChD,UAAU,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC;EAC3C,UAAU,IAAI,MAAM,CAAC,UAAU,GAAG,GAAG,EAAE;EACvC,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;EAChD,cAAc,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EACrC,aAAa,MAAM;EACnB,cAAc,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EAC3D,aAAa;EACb,WAAW,MAAM;EACjB,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EACtE,WAAW;EACX,UAAU,OAAO,GAAG,CAAC;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE,MAAM,IAAI;EAC7B,UAAU,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;EAC5E,SAAS;EACT,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAC7B,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EACjC,UAAU,iBAAiB,EAAE,KAAK;EAClC,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,KAAK;EACxB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,SAAS;EAC9B,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,WAAW,EAAE,CAAC;EAC1B,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,QAAQ,EAAE,QAAQ;EAC9B,YAAY,SAAS,EAAE,KAAK;EAC5B,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC;EAClD,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,qBAAqB,EAAE;EAC3D,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,qBAAqB,EAAE;EAC3D,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,wBAAwB,EAAE;EAC9D,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;EAClC,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECpFD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAC1C,QAAQ,CAAC,sBAAsB,CAAC,CAAC;EACjC,QAAQ,CAAC,mBAAmB,CAAC;;;;;;"}