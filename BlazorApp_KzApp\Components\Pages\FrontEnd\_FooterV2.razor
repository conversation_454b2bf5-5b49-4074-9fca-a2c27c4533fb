﻿@code {    

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public bool IsContentPage { get; set; }

    [Parameter]
    public bool MobileFooterMenus { get; set; }

    eZeroCore.Settings config = new eZeroCore.Settings();

    //eZeroCore.Web.Document docFooter = new("id0c5e8d926d");
    eZeroCore.Web.Column colFooter = new("COL6C8C802C8B");
    eZeroCore.Web.Column colAbout = new("COL15EF1551BE");

    eZeroCore.Web.ColumnList columnList = new();

    List<eZeroCore.Web.Column> Columns = new();
    protected override async Task OnInitializedAsync()
    {
        columnList = new();
        columnList.ParentId = "COL6C8C802C8B";
        columnList.OrderBy = eZeroCore.Web.ColumnList.OrderByEnum.Followorder;
        columnList.SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending;
        Columns = await columnList.ListAsync();
    }

    string GetUrl(eZeroCore.Web.Column col)
    {
        #region...
        if (string.IsNullOrEmpty(col.Redirect))
        {
            eZeroCore.Web.Weicity.WeiPage page = new()
                {
                    ColId = col.ColumnId,
                    RootColIdOrUrlWrite = col.GetRootId()
                };
            //return page.GetPageUrl(eZeroCore.Web.Weicity.WeiPage.ContentType.Column);
            return col.GetColumnUrl();
        }
        return col.Redirect;
        #endregion
    }
}

<div class="@(IsContentPage?"":"container-medium") overflow-hidden">
    <div class="card shadow-none border my-2 py-1">
        @if (Columns.Count > 0)
        {
            <nav role="navigation" class="nav justify-content-center footerColumns">
                @foreach (eZeroCore.Web.Column col in Columns)
                {
                    <a href="@GetUrl(col)" target="_parent" class="nav-link w-nav-link">
                        @col.ColumnName
                    </a>
                }
            </nav>
        }
    </div>
    <div class="card shadow-none border">
        <div class="row g-3 py-5">
            <div class="col-10 col-md-6 mx-auto text-center">
                <div class="d-flex justify-content-center">
                    <img class="footerHomePageImage d-block mx-2" style="max-height:@(CObj.IsMobile?"80px":"200px")" src="@colFooter.TitlePhotoUrls">
                    <img class="footerHomePageImage d-block mx-2" style="max-height:@(CObj.IsMobile?"80px":"200px")" src="@colFooter.SEOPack_Title">
                    <img class="footerHomePageImage d-block mx-2" style="max-height:@(CObj.IsMobile?"80px":"200px")" src="@colFooter.SEOPack_description">
                    <img class="footerHomePageImage d-block mx-2" style="max-height:@(CObj.IsMobile?"80px":"200px")" src="@colFooter.SEOPack_keywords">
                </div>                
            </div>
            <b class="d-block text-center mt-5">
                @colFooter.ColumnContentSummary
            </b>
        </div>
    </div>
    <div class="text-center my-2">
        <RadzenTabs>
            <Tabs>
                <RadzenTabsItem Text="公司介绍" class="pb-0">
                    @(new MarkupString(colAbout.ColumnContent))
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </div>
    <div class="footer-copyright text-center my-3">
        ©Copyright 2008-@(DateTime.Now.Year) @(new MarkupString(colAbout.ColumnContentSummary))
    </div>
</div>

@if (MobileFooterMenus)
{
    <BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile._FooterMenus/>
}
