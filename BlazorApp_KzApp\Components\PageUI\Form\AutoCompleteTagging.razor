﻿@using System;
@using System.Collections.Generic;
@using System.Linq;
@using System.Text.RegularExpressions;

<div class="suggestion-container">

    <label for="@($"{Id}_tag")" class="form-label tagsFormLabel">
        @Title 
        (Enter to inish)
    </label>

    <div id="@($"{Id}_tagsinput")" class="tagsinput">

        <div id="@($"{Id}_addTag")">
            <div class="@(IsContainSpecialCharacter ? "tag-tooltip" : string.Empty) input-group">

                @if (Tags != null && Tags.Any())
                {
                    @foreach (var tag in Tags)
                    {
                        if (!string.IsNullOrEmpty(tag))
                        {
                            <span class="tag bg-purple-900 input-group-text ">
                                <span class="tag-text">@tag</span>
                                <span class="tag-remove" @onclick="() => DeleteTag(tag)" />
                            </span>
                        }
                    }
                }

                <input id="@($"{Id}_tag")"
                       class="form-control tag-input"
                       placeholder="Add tags"
                       autocomplete="off"
                @bind-value="Value"
                @bind-value:event="oninput"
                @onkeyup="eventArgs => AddTags(eventArgs)" />
                
            </div>
        </div>
    </div>

    @if (IsContainSpecialCharacter)
    {
        <small class="error-right d-inline-flex p-2">
            pecial characters not allowed.
        </small>
    }

</div>

@code {

    private Guid Id => Guid.NewGuid();
    protected string Value { get; set; } = "";
    protected bool MenuVisibility { get; set; }
    protected bool IsContainSpecialCharacter { get; set; }
    [Parameter]
    public List<string> Tags { get; set; } = new List<string>();
    [Parameter]
    public string Title { get; set; } = "Tags";

    protected override void OnInitialized()
    {
    }

    protected void AddTags(KeyboardEventArgs eventArgs)
    {
        IsContainSpecialCharacter = false;

        Value = Value.Trim().Replace(" ","").Replace(" ","");

        if (!String.IsNullOrEmpty(Value) && eventArgs.Key != null)
        {
            if (eventArgs.Key.Equals("Enter"))
            {
                var regex = new Regex(@"[^\w\s]");
                if (!regex.IsMatch(Value) && !string.IsNullOrEmpty(Value))
                {
                    if (!Tags.Exists(t => t.Equals(Value, StringComparison.CurrentCultureIgnoreCase)))
                    {
                        Tags.Add(Value);
                    }

                    Value = string.Empty;
                }
                else
                {
                    //禁止的字符
                    IsContainSpecialCharacter = true;
                }
            }
        }
    }

    protected void DeleteTag(string value)
    {
        if (String.IsNullOrEmpty(value)) return;

        var tag = Tags.FirstOrDefault(t => t == value);
        if (tag == null) return;

        Tags.Remove(tag);
    }
}