﻿@using AspNetCoreWeb.Blazor.CityV3.LayoutFramework
@using AspNetCoreWeb.Blazor.CityV3.ZBk
@using AspNetCoreWeb.Blazor.Stk._V3
@using System.Globalization
@using MudBlazor
@using <PERSON><PERSON><PERSON>
@using AspNetCoreWeb.Blazor.Stk.Tabs.Components
@inject IJSRuntime JS
@inject NavigationManager NavigationManagerObj


@code {
	[Parameter]
	public ComponentPassObj CObj { get; set; } = new();
	eZeroCore.Users.User User { get; set; } = new();
	eZeroCore.Lang lang = new();

	protected override async Task OnInitializedAsync()
	{
		User = new(CObj.UserId);
		lang = new(User);
		await Task.Delay(0);
	}

	async Task GetTagDate(string tags)
	{
		StateHasChanged();
		await Task.Delay(0);
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		await Task.CompletedTask;
	}

	string SearchString;
	async Task OnSearch(string value)
	{
		SearchString = value.ToUpper();
		//更新表格数据
		await UpdateTableWithSearchString(SearchString);
		await Task.Delay(0);
	}

	// 引用表格组件
	Tb_HotStock _table;

	// 添加更新表格方法
	async Task UpdateTableWithSearchString(string searchValue)
	{
		if (_table != null)
		{
			await _table.SearchByStockCode(searchValue);
		}
	}

	RzSearchInputBox inputBox;
	async Task SearchButtonClick()
	{
		await inputBox.HandleSearchKeyDown();
	}
}

<Main_Framework CObj="@CObj">

		<!-- PC端搜索框 -->
		<MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
			<div class="p-2 text-end UIFrameworkHeader position-relative z-5">
				<div style="margin-top:35px; margin-bottom:-180px;">
					<RzSearchInputBox OnSearch="@OnSearch" Placeholder="@CObj.UserLang.GetLangValue("搜索代号")" />
				</div>
			</div>
		</MudHidden>

		<!-- 移动端搜索框 -->
		<MudHidden Breakpoint="Breakpoint.MdAndUp">
			<div class="p-2 UIFrameworkHeader position-relative z-5 z-center mt-1" style="display:flex; align-items:center; gap:8px;">
				<div>
					<RzSearchInputBox @ref=inputBox OnSearch="@OnSearch" Placeholder="@CObj.UserLang.GetLangValue("搜索代号")" />
				</div>
				<_SearchButton Text="@CObj.UserLang.GetLangValue("搜索")"
							   OnClick="@SearchButtonClick"
							   MarginLeft="0"
							   MarginTop="0" />
			</div>
		</MudHidden>
		
		<!-- 表格 -->
		<Tb_HotStock @ref="_table" CObj="@CObj" />

		<!-- 移动端底部菜单 -->
		<MudHidden Breakpoint="Breakpoint.MdAndUp">
			<_FooterMenus />
		</MudHidden>

		<OffcanvasChatBox CObj="@CObj" />

</Main_Framework>