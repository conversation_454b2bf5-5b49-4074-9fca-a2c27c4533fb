﻿@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption Data { get; set; } = new();

    Settings settings = new();


    string trendSentimentClassName = "";

    protected override Task OnInitializedAsync()
    {
        trendSentimentClassName = GetStockColorFromCallPut(Data.CallPut);
        return base.OnInitializedAsync();
    }

    private string GetStockColorFromCallPut(string callPut)
    {
        // 与GetPutOrCall方法保持一致的颜色逻辑
        bool isPut = callPut.Equals("put", StringComparison.OrdinalIgnoreCase);
        return isPut ? "text-danger-lighter" : "text-success-light";
    }

    int GetStar(float score)
    {
        // 确保分数在0到100之间
        score = Math.Max(0, Math.Min(100, score));
        return (int)Math.Round(score / 20);
    }

    string getWidth(int pct = 20)
    {
        return $"flex: 0 0 {20}%;max-width: {20}%;";
    }

    public static string GetPutOrCall(string callPut, bool borderBadges = true, string addClass = "")
    {
        bool put = callPut.Equals("put", StringComparison.OrdinalIgnoreCase);
        string className = put ? $"badge {(borderBadges ? "badge-phoenix badge-phoenix-danger text-danger-lighter" : "bg-danger-lighter text-black")}" : $"badge {(borderBadges ? "badge-phoenix badge-phoenix-success" : "bg-success-light text-black")}";
        return $"<lable class=\"{className} {addClass}\" style=\"{(put ? "padding-left:11px;padding-right:11px;" : "")}\">{(put ? "PUT" : "CALL")}</lable>";
    }

    public static string GetInOutMoneyHtml(string InOutMoney)
    {
        var className = "";
        if (InOutMoney.Contains('外'))
        {
            className = "badge-phoenix-warning";
        }
        else if (InOutMoney.Contains('内'))
        {
            className = "badge-phoenix-info";
        }
        else
        {
            className = "badge-phoenix-secondary";
        }
        return $"<lable class=\"badge badge-phoenix {className}\">{InOutMoney}</lable>";
    }

    string sTitleClassName = "fs-10 opacity-75";

}

<div class="mobileDatatable">
    <div class="d-flex justify-content-between align-items-center">
        <div class="fs-9">
            <span class="badge badge-phoenix badge-phoenix-info">
                @Data.CreateDate.ToString(settings.SettingsEntity.LongDateFormat)
            </span>
        </div>
        <div class="Scale80-right">
            <MudRating ReadOnly="true" SelectedValue=GetStar(Data.Score) Size="Size.Small" />
        </div>
    </div>
    <div class="row px-3">
        <div class="px-0 text-center" style="@getWidth(20)">
            <div class="fs-7 fw-bold mx-auto @trendSentimentClassName">
                @Data.Stock.ToUpper()
            </div>
            @(new MarkupString(GetPutOrCall(Data.CallPut, false, "mx-auto fs-9")))
            <div class="mt-1 fs-10 @trendSentimentClassName">
                @Data.TrendSentiment
            </div>
        </div>
        <div class="px-0 ps-2" style="@getWidth(25)">
            <div class="@sTitleClassName">
                权利金
            </div>
            <div class="fw-bold">
                @eZeroCore.ENumeric.ConvertNumberToMorKString(Data.Premium)
            </div>
            <div class="@sTitleClassName">
                范围
            </div>
            <div class="fw-bold">
                @(new MarkupString(GetInOutMoneyHtml(Data.InOutMoney)))
            </div>
        </div>
        <div class="px-0" style="@getWidth(18)">
            <div class="@sTitleClassName">
                行权价
            </div>
            <div class="fw-bold">
                @eZeroCore.ENumeric.OutMonetaryStr(Data.Strike, true, N: 1)
            </div>
            <div class="@sTitleClassName">
                未平仓
            </div>
            <div class="fw-bold">
                @eZeroCore.ENumeric.ConvertNumberToMorKString(Data.OpenInterest)
            </div>
        </div>
        <div class="px-0" style="@getWidth(17)">
            <div class="@sTitleClassName">
                合约价
            </div>
            <div class="fw-bold">
                @eZeroCore.ENumeric.OutMonetaryStr(Data.ContractPrice, true)
            </div>
            <div class="@sTitleClassName">
                合约数
            </div>
            <div class="fw-bold">
                @Data.Size
            </div>
        </div>
        <div class="px-0" style="@getWidth(20)">
            <div class="@sTitleClassName">
                到期(月日年)
            </div>
            <div class="fw-bold">
                @Data.ExpireDate.ToString("MM/dd/yy")
            </div>
            <div class="@sTitleClassName">
                隐含波动率
            </div>
            <div class="fw-bold">
                @Data.Ivol.ToString("N2")
            </div>
        </div>
    </div>
</div>



