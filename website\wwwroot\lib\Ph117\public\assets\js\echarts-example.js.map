{"version": 3, "file": "echarts-example.js", "sources": ["../../../src/js/theme/charts/echarts/echarts-utils.js", "../../../src/js/theme/charts/echarts/examples/basic-line-chart.js", "../../../src/js/theme/charts/echarts/examples/area-line-chart.js", "../../../src/js/theme/charts/echarts/examples/stacked-line-chart.js", "../../../src/js/theme/charts/echarts/examples/stacked-area-chart.js", "../../../src/js/theme/charts/echarts/examples/line-marker-chart.js", "../../../src/js/theme/charts/echarts/examples/area-pieces-chart.js", "../../../src/js/theme/charts/echarts/examples/step-line-chart.js", "../../../src/js/theme/charts/echarts/examples/line-gradient-chart.js", "../../../src/js/theme/charts/echarts/examples/dynamic-line-chart.js", "../../../src/js/theme/charts/echarts/examples/line-log-chart.js", "../../../src/js/theme/charts/echarts/examples/share-dataset-chart.js", "../../../src/js/theme/charts/echarts/examples/basic-bar-chart.js", "../../../src/js/theme/charts/echarts/examples/horizontal-bar-chart.js", "../../../src/js/theme/charts/echarts/examples/bar-negative-chart.js", "../../../src/js/theme/charts/echarts/examples/series-bar-chart.js", "../../../src/js/theme/charts/echarts/examples/stacked-bar-chart.js", "../../../src/js/theme/charts/echarts/examples/stacked-horizontal-bar-chart.js", "../../../src/js/theme/charts/echarts/examples/bar-race-chart.js", "../../../src/js/theme/charts/echarts/examples/bar-gradient-chart.js", "../../../src/js/theme/charts/echarts/examples/bar-line-mixed-chart.js", "../../../src/js/theme/charts/echarts/examples/bar-waterfall-chart.js", "../../../src/js/theme/charts/echarts/examples/bar-timeline-chart.js", "../../../src/js/theme/charts/echarts/examples/basic-candlestick-chart.js", "../../../src/js/theme/charts/echarts/examples/candlestick-mixed-chart.js", "../../../src/js/theme/charts/echarts/examples/session-by-country-map.js", "../../../src/js/theme/charts/echarts/examples/map-usa.js", "../../../src/js/theme/charts/echarts/examples/basic-scatter-chart.js", "../../../src/js/theme/charts/echarts/examples/bubble-chart.js", "../../../src/js/theme/charts/echarts/examples/quartet-scatter-chart.js", "../../../src/js/theme/charts/echarts/examples/single-axis-scatter-chart.js", "../../../src/js/theme/charts/echarts/examples/pie-chart.js", "../../../src/js/theme/charts/echarts/examples/doughnut-chart.js", "../../../src/js/theme/charts/echarts/examples/doughnut-rounded-chart.js", "../../../src/js/theme/charts/echarts/examples/pie-multiple-chart.js", "../../../src/js/theme/charts/echarts/examples/pie-label-align-chart.js", "../../../src/js/theme/charts/echarts/examples/pie-edge-align-chart.js", "../../../src/js/theme/charts/echarts/examples/basic-gauge-chart.js", "../../../src/js/theme/charts/echarts/examples/gauge-progress-chart.js", "../../../src/js/theme/charts/echarts/examples/gauge-ring-chart.js", "../../../src/js/theme/charts/echarts/examples/gauge-multi-ring-chart.js", "../../../src/js/theme/charts/echarts/examples/gauge-multi-title-chart.js", "../../../src/js/theme/charts/echarts/examples/gauge-grade-chart.js", "../../../src/js/theme/charts/echarts/examples/radar-chart.js", "../../../src/js/theme/charts/echarts/examples/radar-customized-chart.js", "../../../src/js/theme/charts/echarts/examples/radar-multiple-chart.js", "../../../src/js/theme/charts/echarts/examples/heatmap-chart.js", "../../../src/js/theme/charts/echarts/examples/heatmap-single-series-chart.js", "../../../src/js/theme/charts/echarts/echarts-example.js"], "sourcesContent": ["// import * as echarts from 'echarts';\r\nconst { merge } = window._;\r\n\r\n// form config.js\r\nexport const echartSetOption = (\r\n  chart,\r\n  userOptions,\r\n  getDefaultOptions,\r\n  responsiveOptions\r\n) => {\r\n  const { breakpoints, resize } = window.phoenix.utils;\r\n  const handleResize = options => {\r\n    Object.keys(options).forEach(item => {\r\n      if (window.innerWidth > breakpoints[item]) {\r\n        chart.setOption(options[item]);\r\n      }\r\n    });\r\n  };\r\n\r\n  const themeController = document.body;\r\n  // Merge user options with lodash\r\n  chart.setOption(merge(getDefaultOptions(), userOptions));\r\n\r\n  const navbarVerticalToggle = document.querySelector(\r\n    '.navbar-vertical-toggle'\r\n  );\r\n  if (navbarVerticalToggle) {\r\n    navbarVerticalToggle.addEventListener('navbar.vertical.toggle', () => {\r\n      chart.resize();\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    });\r\n  }\r\n\r\n  resize(() => {\r\n    chart.resize();\r\n    if (responsiveOptions) {\r\n      handleResize(responsiveOptions);\r\n    }\r\n  });\r\n  if (responsiveOptions) {\r\n    handleResize(responsiveOptions);\r\n  }\r\n\r\n  themeController.addEventListener(\r\n    'clickControl',\r\n    ({ detail: { control } }) => {\r\n      if (control === 'phoenixTheme') {\r\n        chart.setOption(window._.merge(getDefaultOptions(), userOptions));\r\n      }\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    }\r\n  );\r\n};\r\n// -------------------end config.js--------------------\r\n\r\nconst echartTabs = document.querySelectorAll('[data-tab-has-echarts]');\r\nif (echartTabs) {\r\n  echartTabs.forEach(tab => {\r\n    tab.addEventListener('shown.bs.tab', e => {\r\n      const el = e.target;\r\n      const { hash } = el;\r\n      const id = hash || el.dataset.bsTarget;\r\n      const content = document.getElementById(id.substring(1));\r\n      const chart = content?.querySelector('[data-echart-tab]');\r\n      if (chart) {\r\n        window.echarts.init(chart).resize();\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport const tooltipFormatter = (params, dateFormatter = 'MMM DD') => {\r\n  let tooltipItem = ``;\r\n  params.forEach(el => {\r\n    tooltipItem += `<div class='ms-1'>\r\n        <h6 class=\"text-body-tertiary\"><span class=\"fas fa-circle me-1 fs-10\" style=\"color:${\r\n          el.borderColor ? el.borderColor : el.color\r\n        }\"></span>\r\n          ${el.seriesName} : ${\r\n      typeof el.value === 'object' ? el.value[1] : el.value\r\n    }\r\n        </h6>\r\n      </div>`;\r\n  });\r\n  return `<div>\r\n            <p class='mb-2 text-body-tertiary'>\r\n              ${\r\n                window.dayjs(params[0].axisValue).isValid()\r\n                  ? window.dayjs(params[0].axisValue).format(dateFormatter)\r\n                  : params[0].axisValue\r\n              }\r\n            </p>\r\n            ${tooltipItem}\r\n          </div>`;\r\n};\r\n\r\nexport const handleTooltipPosition = ([pos, , dom, , size]) => {\r\n  // only for mobile device\r\n  if (window.innerWidth <= 540) {\r\n    const tooltipHeight = dom.offsetHeight;\r\n    const obj = { top: pos[1] - tooltipHeight - 20 };\r\n    obj[pos[0] < size.viewSize[0] / 2 ? 'left' : 'right'] = 5;\r\n    return obj;\r\n  }\r\n  return null; // else default behaviour\r\n};\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst basicLineChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-line-chart-example');\r\n  const months = [\r\n    'January',\r\n    'February',\r\n    'March',\r\n    'April',\r\n    'May',\r\n    'June',\r\n    'July',\r\n    'August',\r\n    'September',\r\n    'October',\r\n    'November',\r\n    'December'\r\n  ];\r\n\r\n  const data = [\r\n    1000, 1500, 1250, 1010, 1045, 2000, 1200, 1330, 1000, 1200, 1410, 1200\r\n  ];\r\n\r\n  const tooltipFormatter = params => {\r\n    return `\r\n    <div>\r\n        <h6 class=\"fs-9 text-body-tertiary mb-0\">\r\n          <span class=\"fas fa-circle me-1\" style='color:${params[0].borderColor}'></span>\r\n          ${params[0].name} : ${params[0].value}\r\n        </h6>\r\n    </div>\r\n    `;\r\n  };\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: tooltipFormatter,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: months,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          formatter: value => value.substring(0, 3),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            type: 'dashed',\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false },\r\n        min: 600\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          data,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          symbolSize: 10,\r\n          smooth: false,\r\n          hoverAnimation: true\r\n        }\r\n      ],\r\n      grid: { right: '3%', left: '10%', bottom: '10%', top: '5%' }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default basicLineChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst basicAreaLineChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-area-line-chart-example');\r\n  const months = [\r\n    'January',\r\n    'February',\r\n    'March',\r\n    'April',\r\n    'May',\r\n    'June',\r\n    'July',\r\n    'August',\r\n    'September',\r\n    'October',\r\n    'November',\r\n    'December'\r\n  ];\r\n  const data = [\r\n    1020, 1160, 1300, 958, 1240, 1020, 1409, 1200, 1051, 1120, 1240, 1054\r\n  ];\r\n\r\n  const tooltipFormatter = params => {\r\n    return `\r\n    <div>\r\n        <h6 class=\"fs-9 text-body-tertiary mb-0\">\r\n          <span class=\"fas fa-circle me-1\" style='color:${params[0].borderColor}'></span>\r\n          ${params[0].name} : ${params[0].value}\r\n        </h6>\r\n    </div>\r\n    `;\r\n  };\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        formatter: params => tooltipFormatter(params),\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: months,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          formatter: value => value.substring(0, 3),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false },\r\n        min: 600\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          data,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          showSymbol: false,\r\n          symbolSize: 10,\r\n          symbol: 'circle',\r\n          smooth: false,\r\n          hoverAnimation: true,\r\n          areaStyle: {\r\n            color: {\r\n              type: 'linear',\r\n              x: 0,\r\n              y: 0,\r\n              x2: 0,\r\n              y2: 1,\r\n              colorStops: [\r\n                {\r\n                  offset: 0,\r\n                  color: rgbaColor(getColor('primary'), 0.5)\r\n                },\r\n                {\r\n                  offset: 1,\r\n                  color: rgbaColor(getColor('primary'), 0)\r\n                }\r\n              ]\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      grid: { right: '3%', left: '10%', bottom: '10%', top: '5%' }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default basicAreaLineChartInit;\r\n", "import {\r\n  echartSetOption,\r\n  handleTooltipPosition,\r\n  tooltipFormatter\r\n} from '../echarts-utils';\r\n\r\nconst stackedLineChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-stacked-line-chart-example');\r\n  const days = [\r\n    'Monday',\r\n    'Tuesday',\r\n    'Wednesday',\r\n    'Thursday',\r\n    'Friday',\r\n    'Saturday',\r\n    'Sunday'\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        position: (...params) => handleTooltipPosition(params),\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          margin: 15,\r\n          formatter: value => value.substring(0, 3)\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg'),\r\n            type: 'dashed'\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Matcha Latte',\r\n          type: 'line',\r\n          symbolSize: 6,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('info'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('info')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [120, 132, 101, 134, 90, 230, 210]\r\n        },\r\n        {\r\n          name: 'Milk Tea',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('success'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('success')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [220, 182, 191, 234, 290, 330, 310]\r\n        },\r\n        {\r\n          name: 'Cheese Cocoa',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('danger')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [150, 232, 201, 154, 190, 330, 410]\r\n        },\r\n        {\r\n          name: 'Cheese Brownie',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('warning')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [320, 332, 301, 334, 390, 330, 320]\r\n        },\r\n        {\r\n          name: 'Matcha Cocoa',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          symbol: 'circle',\r\n          stack: 'product',\r\n          data: [820, 932, 901, 934, 1290, 1330, 1320]\r\n        }\r\n      ],\r\n      grid: { right: 10, left: 5, bottom: 5, top: 8, containLabel: true }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default stackedLineChartInit;\r\n", "import {\r\n  echartSetOption,\r\n  handleTooltipPosition,\r\n  tooltipFormatter\r\n} from '../echarts-utils';\r\n\r\nconst stackedAreaChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-stacked-area-chart-example');\r\n  const days = [\r\n    'Monday',\r\n    'Tuesday',\r\n    'Wednesday',\r\n    'Thursday',\r\n    'Friday',\r\n    'Saturday',\r\n    'Sunday'\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        position: (...params) => handleTooltipPosition(params),\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          margin: 15,\r\n          formatter: value => value.substring(0, 3)\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Matcha Latte',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [120, 132, 101, 134, 90, 230, 210],\r\n          areaStyle: {\r\n            color: rgbaColor(getColor('info'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('info'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('info')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Milk Tea',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [220, 182, 191, 234, 290, 330, 310],\r\n          areaStyle: {\r\n            color: rgbaColor(getColor('success'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('success'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('success')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Cheese Cocoa',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [150, 232, 201, 154, 190, 330, 410],\r\n          areaStyle: {\r\n            color: rgbaColor(getColor('danger'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('danger')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Cheese Brownie',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [320, 332, 301, 334, 390, 330, 320],\r\n          areaStyle: {\r\n            color: rgbaColor(getColor('warning'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('warning')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Matcha Cocoa',\r\n          type: 'line',\r\n          symbolSize: 10,\r\n          stack: 'product',\r\n          data: [820, 932, 901, 934, 1290, 1330, 1320],\r\n          areaStyle: {\r\n            color: rgbaColor(getColor('primary'), 0.3)\r\n          },\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          symbol: 'circle'\r\n        }\r\n      ],\r\n      grid: { right: 10, left: 5, bottom: 5, top: 8, containLabel: true }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default stackedAreaChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst lineMarkerChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-line-marker-chart-example');\r\n  const days = [\r\n    'Monday',\r\n    'Tuesday',\r\n    'Wednesday',\r\n    'Thursday',\r\n    'Friday',\r\n    'Saturday',\r\n    'Sunday'\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        getColor('primary'),\r\n        getColor('warning')\r\n        // getColor('danger')\r\n      ],\r\n      legend: {\r\n        data: [\r\n          {\r\n            name: 'Max',\r\n            textStyle: {\r\n              color: getColor('quaternary-color')\r\n            }\r\n          },\r\n          {\r\n            name: 'Min',\r\n            textStyle: {\r\n              color: getColor('quaternary-color')\r\n            }\r\n          }\r\n        ]\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Max',\r\n          type: 'line',\r\n          data: [10, 11, 13, 11, 12, 9, 12],\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          symbol: 'circle',\r\n          markPoint: {\r\n            itemStyle: {\r\n              color: getColor('primary')\r\n            },\r\n            data: [\r\n              { type: 'max', name: 'Max' },\r\n              { type: 'min', name: 'Min' }\r\n            ]\r\n          },\r\n          markLine: {\r\n            lineStyle: {\r\n              color: getColor('primary')\r\n            },\r\n            label: {\r\n              color: getColor('quaternary-color')\r\n            },\r\n            data: [{ type: 'average', name: 'average' }]\r\n          }\r\n        },\r\n        {\r\n          name: 'Min',\r\n          type: 'line',\r\n          data: [1, -2, 2, 5, 3, 2, 0],\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('danger')\r\n          },\r\n          symbol: 'circle',\r\n          markPoint: {\r\n            itemStyle: {\r\n              color: getColor('danger')\r\n            },\r\n            label: {\r\n              color: '#fff'\r\n            },\r\n            data: [{ name: 'Weekly lowest', value: -2, xAxis: 1, yAxis: -1.5 }]\r\n          },\r\n          markLine: {\r\n            lineStyle: {\r\n              color: getColor('danger')\r\n            },\r\n            label: {\r\n              color: getColor('quaternary-color')\r\n            },\r\n            data: [\r\n              { type: 'average', name: 'average' },\r\n              [\r\n                {\r\n                  symbol: 'none',\r\n                  x: '90%',\r\n                  yAxis: 'max'\r\n                },\r\n                {\r\n                  symbol: 'circle',\r\n                  label: {\r\n                    position: 'start',\r\n                    formatter: 'Max'\r\n                  },\r\n                  type: 'max',\r\n                  name: 'Highest point'\r\n                }\r\n              ]\r\n            ]\r\n          }\r\n        }\r\n      ],\r\n      grid: { right: '5%', left: '5%', bottom: '10%', top: '15%' }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default lineMarkerChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst areaPiecesChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-area-pieces-chart-example');\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          margin: 15,\r\n          formatter: value => window.dayjs(value).format('MMM DD')\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      visualMap: {\r\n        type: 'piecewise',\r\n        show: false,\r\n        dimension: 0,\r\n        seriesIndex: 0,\r\n        pieces: [\r\n          {\r\n            gt: 1,\r\n            lt: 3,\r\n            color: rgbaColor(getColor('primary'), 0.4)\r\n          },\r\n          {\r\n            gt: 5,\r\n            lt: 7,\r\n            color: rgbaColor(getColor('primary'), 0.4)\r\n          }\r\n        ]\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          name: 'Total',\r\n          smooth: 0.6,\r\n          symbol: 'none',\r\n          lineStyle: {\r\n            color: getColor('primary'),\r\n            width: 5\r\n          },\r\n          markLine: {\r\n            symbol: ['none', 'none'],\r\n            label: { show: false },\r\n            data: [{ xAxis: 1 }, { xAxis: 3 }, { xAxis: 5 }, { xAxis: 7 }]\r\n          },\r\n          areaStyle: {},\r\n          data: [\r\n            ['2019-10-10', 100],\r\n            ['2019-10-11', 300],\r\n            ['2019-10-12', 450],\r\n            ['2019-10-13', 300],\r\n            ['2019-10-14', 250],\r\n            ['2019-10-15', 750],\r\n            ['2019-10-16', 650],\r\n            ['2019-10-17', 550],\r\n            ['2019-10-18', 200]\r\n          ]\r\n        }\r\n      ],\r\n      grid: { right: 20, left: 5, bottom: 5, top: 8, containLabel: true }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default areaPiecesChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst stepLineChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-step-line-chart-example');\r\n\r\n  const days = [\r\n    'Monday',\r\n    'Tuesday',\r\n    'Wednesday',\r\n    'Thursday',\r\n    'Friday',\r\n    'Saturday',\r\n    'Sunday'\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      color: [getColor('danger'), getColor('warning'), getColor('primary')],\r\n\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Step Start',\r\n          type: 'line',\r\n          step: 'start',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          symbol: 'circle',\r\n          data: [120, 132, 101, 134, 90, 230, 210]\r\n        },\r\n        {\r\n          name: 'Step Middle',\r\n          type: 'line',\r\n          step: 'middle',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('warning')\r\n          },\r\n          symbol: 'circle',\r\n          data: [220, 282, 201, 234, 290, 430, 410]\r\n        },\r\n        {\r\n          name: 'Step End',\r\n          type: 'line',\r\n          step: 'end',\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('danger')\r\n          },\r\n          symbol: 'circle',\r\n          data: [450, 432, 401, 454, 590, 530, 510]\r\n        }\r\n      ],\r\n      grid: { right: '3%', left: '8%', bottom: '10%', top: '5%' }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default stepLineChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst lineGradientChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-line-gradient-chart-example'\r\n  );\r\n\r\n  const data = [\r\n    ['2021-06-05', 116],\r\n    ['2021-06-06', 129],\r\n    ['2021-06-07', 135],\r\n    ['2021-06-08', 86],\r\n    ['2021-06-09', 73],\r\n    ['2021-06-10', 85],\r\n    ['2021-06-11', 73],\r\n    ['2021-06-12', 68],\r\n    ['2021-06-13', 92],\r\n    ['2021-06-14', 130],\r\n    ['2021-06-15', 245],\r\n    ['2021-06-16', 139],\r\n    ['2021-06-17', 115],\r\n    ['2021-06-18', 111],\r\n    ['2021-06-19', 309],\r\n    ['2021-06-20', 206],\r\n    ['2021-06-21', 137],\r\n    ['2021-06-22', 128],\r\n    ['2021-06-23', 85],\r\n    ['2021-06-24', 94],\r\n    ['2021-06-25', 71],\r\n    ['2021-06-26', 106],\r\n    ['2021-06-27', 84],\r\n    ['2021-06-28', 93],\r\n    ['2021-06-29', 85],\r\n    ['2021-06-30', 73],\r\n    ['2021-07-01', 83],\r\n    ['2021-07-02', 125],\r\n    ['2021-07-03', 107],\r\n    ['2021-07-04', 82],\r\n    ['2021-07-05', 44],\r\n    ['2021-07-06', 72],\r\n    ['2021-07-07', 106],\r\n    ['2021-07-08', 107],\r\n    ['2021-07-09', 66],\r\n    ['2021-07-10', 91],\r\n    ['2021-07-11', 92],\r\n    ['2021-07-12', 113],\r\n    ['2021-07-13', 107],\r\n    ['2021-07-14', 250],\r\n    ['2021-07-15', 111],\r\n    ['2021-07-16', 350],\r\n    ['2021-07-17', 150],\r\n    ['2021-07-18', 420],\r\n    ['2021-07-19', 77],\r\n    ['2021-07-20', 83],\r\n    ['2021-07-21', 111],\r\n    ['2021-07-22', 57],\r\n    ['2021-07-23', 55],\r\n    ['2021-07-24', 60]\r\n  ];\r\n\r\n  const dateList = data.map(item => {\r\n    return item[0];\r\n  });\r\n  const valueList = data.map(item => {\r\n    return item[1];\r\n  });\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      visualMap: {\r\n        show: false,\r\n        type: 'continuous',\r\n        dimension: 0,\r\n        min: 0,\r\n        max: dateList.length - 1,\r\n        color: [getColor('danger'), getColor('warning')]\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: dateList,\r\n        axisLabel: {\r\n          formatter: value => window.dayjs(value).format('MMM DD'),\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg'),\r\n            type: 'dashed'\r\n          }\r\n        }\r\n      },\r\n      grid: { right: '3%', left: '8%', bottom: '10%', top: '5%' },\r\n      series: {\r\n        name: 'Total',\r\n        type: 'line',\r\n        showSymbol: false,\r\n        symbolSize: 10,\r\n        symbol: 'circle',\r\n        data: valueList,\r\n        itemStyle: {\r\n          borderWidth: 2\r\n        }\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default lineGradientChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst dynamicLineChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-dynamic-line-chart-example');\r\n\r\n  const data = [];\r\n  let now = +new Date(1997, 9, 3);\r\n  const oneDay = 24 * 3600 * 1000;\r\n  let value = Math.random() * 1000;\r\n\r\n  const randomData = () => {\r\n    now = new Date(+now + oneDay);\r\n    value = value + Math.random() * 21 - 10;\r\n    return {\r\n      name: now.toString(),\r\n      value: [\r\n        [now.getFullYear(), now.getMonth() + 1, now.getDate()].join('/'),\r\n        Math.round(value)\r\n      ]\r\n    };\r\n  };\r\n\r\n  // eslint-disable-next-line no-plusplus\r\n  for (let i = 0; i < 1000; i++) {\r\n    data.push(randomData());\r\n  }\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          animation: false\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      xAxis: {\r\n        type: 'time',\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        boundaryGap: [0, '100%'],\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Total',\r\n          type: 'line',\r\n          showSymbol: false,\r\n          hoverAnimation: false,\r\n          data,\r\n          lineStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          symbol: 'circle',\r\n          symbolSize: 10\r\n        }\r\n      ],\r\n      grid: { right: 5, left: '7%', bottom: '10%', top: '5%' }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    setInterval(() => {\r\n      // eslint-disable-next-line no-plusplus\r\n      for (let i = 0; i < 5; i++) {\r\n        data.shift();\r\n        data.push(randomData());\r\n      }\r\n\r\n      chart.setOption({\r\n        series: [\r\n          {\r\n            data\r\n          }\r\n        ]\r\n      });\r\n    }, 1000);\r\n  }\r\n};\r\n\r\nexport default dynamicLineChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst lineLogChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-line-log-chart-example');\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        splitLine: { show: false },\r\n        data: Array.from(Array(10).keys()).map(item => item + 1)\r\n      },\r\n      yAxis: {\r\n        type: 'log',\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Index Of 3',\r\n          type: 'line',\r\n          data: [1, 3, 9, 27, 81, 247, 741, 2223, 6669],\r\n          symbolSize: 7,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('danger')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Index of 2',\r\n          type: 'line',\r\n          data: [1, 2, 4, 8, 16, 32, 64, 128, 256],\r\n          symbolSize: 7,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('success'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('success')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          name: 'Index of 1/2',\r\n          type: 'line',\r\n          data: [\r\n            1 / 2,\r\n            1 / 4,\r\n            1 / 8,\r\n            1 / 16,\r\n            1 / 32,\r\n            1 / 64,\r\n            1 / 128,\r\n            1 / 256,\r\n            1 / 512\r\n          ],\r\n          symbolSize: 7,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('info'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('info')\r\n          },\r\n          symbol: 'circle'\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 10,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: 10,\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default lineLogChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst shareDatasetChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-share-dataset-chart-example'\r\n  );\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        getColor('danger'),\r\n        getColor('warning'),\r\n        getColor('info'),\r\n        getColor('primary')\r\n      ],\r\n      legend: {\r\n        top: 0,\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        showContent: false\r\n      },\r\n      dataset: {\r\n        source: [\r\n          ['product', '2012', '2013', '2014', '2015', '2016', '2017'],\r\n          ['Milk Tea', 56.5, 82.1, 88.7, 70.1, 53.4, 85.1],\r\n          ['Matcha Latte', 51.1, 51.4, 55.1, 53.3, 73.8, 68.7],\r\n          ['Cheese Cocoa', 40.1, 62.2, 69.5, 36.4, 45.2, 32.5],\r\n          ['Walnut Brownie', 25.2, 37.1, 41.2, 18, 33.9, 49.1]\r\n        ]\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        gridIndex: 0,\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          smooth: true,\r\n          seriesLayoutBy: 'row',\r\n          emphasis: { focus: 'series' },\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('danger'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('danger')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          type: 'line',\r\n          smooth: true,\r\n          seriesLayoutBy: 'row',\r\n          emphasis: { focus: 'series' },\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('info'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('info')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          type: 'line',\r\n          smooth: true,\r\n          seriesLayoutBy: 'row',\r\n          emphasis: { focus: 'series' },\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('warning')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          type: 'line',\r\n          smooth: true,\r\n          seriesLayoutBy: 'row',\r\n          emphasis: { focus: 'series' },\r\n          symbolSize: 10,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('primary'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          symbol: 'circle'\r\n        },\r\n        {\r\n          type: 'pie',\r\n          id: 'pie',\r\n          radius: '30%',\r\n          center: ['50%', '28%'],\r\n          emphasis: { focus: 'data' },\r\n          label: {\r\n            formatter: '{b}: {@2012} ({d}%)',\r\n            color: getColor('tertiary-color')\r\n          },\r\n          encode: {\r\n            itemName: 'product',\r\n            value: '2012',\r\n            tooltip: '2012'\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 10,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '55%',\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default shareDatasetChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst basicBarChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-basic-bar-chart-example');\r\n  const months = [\r\n    'January',\r\n    'February',\r\n    'March',\r\n    'April',\r\n    'May',\r\n    'June',\r\n    'July',\r\n    'August',\r\n    'September',\r\n    'October',\r\n    'November',\r\n    'December'\r\n  ];\r\n  const data = [\r\n    1020, 1160, 1300, 958, 1240, 1020, 1409, 1200, 1051, 1120, 1240, 1054\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        formatter: params => tooltipFormatter(params),\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: months,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          formatter: value => value.substring(0, 3),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        boundaryGap: true,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false },\r\n        min: 600\r\n      },\r\n      series: [\r\n        {\r\n          type: 'bar',\r\n          name: 'Total',\r\n          data,\r\n          lineStyle: { color: getColor('primary') },\r\n          itemStyle: {\r\n            color: getColor('primary'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          },\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          smooth: false,\r\n          hoverAnimation: true\r\n        }\r\n      ],\r\n      grid: { right: '3%', left: '10%', bottom: '10%', top: '5%' }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default basicBarChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst horizontalBarChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-horizontal-bar-chart-example'\r\n  );\r\n  const months = [\r\n    'January',\r\n    'February',\r\n    'March',\r\n    'April',\r\n    'May',\r\n    'June',\r\n    'July',\r\n    'August',\r\n    'September',\r\n    'October',\r\n    'November',\r\n    'December'\r\n  ];\r\n  const data = [\r\n    1020, 1160, 1300, 958, 1240, 1020, 1409, 1200, 1051, 1120, 1240, 1054\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        formatter: tooltipFormatter,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'value',\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n        axisTick: { show: true },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        min: 600\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: months,\r\n        boundaryGap: true,\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'bar',\r\n          name: 'Total',\r\n          data,\r\n          lineStyle: { color: getColor('primary') },\r\n          itemStyle: {\r\n            color: getColor('primary'),\r\n            barBorderRadius: [0, 3, 3, 0]\r\n          },\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          smooth: false,\r\n          hoverAnimation: true\r\n        }\r\n      ],\r\n      grid: { right: '3%', left: '10%', bottom: '10%', top: '0%' }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default horizontalBarChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst barNegativeChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-bar-negative-chart-example');\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'shadow'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      grid: {\r\n        top: 5,\r\n        bottom: 5,\r\n        left: 5,\r\n        right: 5\r\n      },\r\n      xAxis: {\r\n        type: 'value',\r\n        position: 'top',\r\n        splitLine: {\r\n          lineStyle: {\r\n            type: 'dashed',\r\n            color: getColor('secondary-bg')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        axisLine: { show: false },\r\n        axisLabel: { show: false },\r\n        axisTick: { show: false },\r\n        splitLine: { show: false },\r\n        data: [\r\n          'Ten',\r\n          'Nine',\r\n          'Eight',\r\n          'Seven',\r\n          'Six',\r\n          'Five',\r\n          'Four',\r\n          'Three',\r\n          'Two',\r\n          'One'\r\n        ]\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Cost',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true,\r\n            formatter: '{b}',\r\n            color: '#fff'\r\n          },\r\n          itemStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          data: [-0.15, -0.45, 0.3, 0.55, -0.23, 0.09, -0.56, 0.47, -0.36, 0.32]\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default barNegativeChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst seriesBarChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-series-bar-chart-example');\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      color: [getColor('primary'), getColor('info')],\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'shadow'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      xAxis: {\r\n        type: 'value',\r\n        axisLabel: {\r\n          formatter: value => `${value / 1000}k`,\r\n          color: getColor('quaternary-color')\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            type: 'dashed',\r\n            color: getColor('secondary-bg')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        axisTick: { show: false },\r\n        splitLine: { show: false },\r\n        data: ['Brazil', 'Indonesia', 'USA', 'India', 'China']\r\n      },\r\n      series: [\r\n        {\r\n          name: '2011',\r\n          type: 'bar',\r\n          data: [131744, 104970, 29034, 235481, 132541],\r\n          itemStyle: {\r\n            barBorderRadius: [0, 3, 3, 0]\r\n          }\r\n        },\r\n        {\r\n          name: '2012',\r\n          type: 'bar',\r\n          data: [134141, 121594, 31000, 141201, 124115],\r\n          itemStyle: {\r\n            barBorderRadius: [0, 3, 3, 0]\r\n          }\r\n        }\r\n      ],\r\n      grid: { right: 15, left: '12%', bottom: '10%', top: 5 }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default seriesBarChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst stackedBarChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-stacked-bar-chart-example');\r\n\r\n  const xAxisData = [];\r\n  const data1 = [];\r\n  const data2 = [];\r\n  const data3 = [];\r\n  const data4 = [];\r\n\r\n  for (let i = 0; i < 10; i += 1) {\r\n    xAxisData.push('Class'.concat(i + 1));\r\n    data1.push((Math.random() * 2).toFixed(2));\r\n    data2.push((Math.random() * 5).toFixed(2));\r\n    data3.push((Math.random() + 0.3).toFixed(2));\r\n    data4.push(-Math.random().toFixed(2));\r\n  }\r\n\r\n  const emphasisStyle = {\r\n    itemStyle: {\r\n      shadowBlur: 10,\r\n      shadowColor: rgbaColor(getColor('light-text-emphasis'), 0.3)\r\n    }\r\n  };\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        getColor('primary'),\r\n        getColor('info'),\r\n        getColor('warning'),\r\n        getColor('danger')\r\n      ],\r\n      legend: {\r\n        data: ['Bar1', 'Bar2', 'Bar3', 'Bar4'],\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        },\r\n        left: 0\r\n      },\r\n      toolbox: {\r\n        feature: {\r\n          magicType: {\r\n            type: ['stack', 'tiled']\r\n          }\r\n        },\r\n        iconStyle: {\r\n          borderColor: getColor('tertiary-color'),\r\n          borderWidth: 1\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      xAxis: {\r\n        data: xAxisData,\r\n        splitLine: { show: false },\r\n        splitArea: { show: false },\r\n\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('quaternary-color')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Bar1',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          emphasis: emphasisStyle,\r\n          data: data1\r\n        },\r\n        {\r\n          name: 'Bar2',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          emphasis: emphasisStyle,\r\n          data: data2\r\n        },\r\n        {\r\n          name: 'Bar3',\r\n          type: 'bar',\r\n          stack: 'two',\r\n          emphasis: emphasisStyle,\r\n          data: data3\r\n        },\r\n        {\r\n          name: 'Bar4',\r\n          type: 'bar',\r\n          stack: 'two',\r\n          emphasis: emphasisStyle,\r\n          data: data4\r\n        }\r\n      ],\r\n      grid: {\r\n        top: '10%',\r\n        bottom: 10,\r\n        left: 5,\r\n        right: 7,\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default stackedBarChartInit;\r\n", "import {\r\n  echartSetOption,\r\n  handleTooltipPosition,\r\n  tooltipFormatter\r\n} from '../echarts-utils';\r\n\r\nconst stackedHorizontalBarChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-stacked-horizontal-bar-chart-example'\r\n  );\r\n\r\n  const days = [\r\n    'Monday',\r\n    'Tuesday',\r\n    'Wednesday',\r\n    'Thursday',\r\n    'Friday',\r\n    'Saturday',\r\n    'Sunday'\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        getColor('info'),\r\n        getColor('danger'),\r\n        getColor('warning'),\r\n        getColor('success'),\r\n        getColor('primary')\r\n      ],\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'shadow'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position: (...params) => handleTooltipPosition(params),\r\n        formatter: tooltipFormatter\r\n      },\r\n      toolbox: {\r\n        feature: {\r\n          magicType: {\r\n            type: ['stack', 'tiled']\r\n          }\r\n        },\r\n        right: 0\r\n      },\r\n      legend: {\r\n        data: [\r\n          'Direct',\r\n          'Mail Ad',\r\n          'Affiliate Ad',\r\n          'Video Ad',\r\n          'Search Engine'\r\n        ],\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        },\r\n        left: 0\r\n      },\r\n      xAxis: {\r\n        type: 'value',\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            show: true,\r\n            color: getColor('secondary-bg')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        axisLine: {\r\n          lineStyle: {\r\n            show: true,\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          formatter: value => value.substring(0, 3)\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Direct',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true,\r\n            textStyle: {\r\n              color: '#fff'\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [400, 241, 451, 150, 321, 330, 142]\r\n        },\r\n        {\r\n          name: 'Mail Ad',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [250, 200, 222, 240, 230, 230, 211]\r\n        },\r\n        {\r\n          name: 'Affiliate Ad',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true,\r\n            textStyle: {\r\n              color: '#fff'\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [190, 182, 170, 195, 260, 333, 124]\r\n        },\r\n        {\r\n          name: 'Video Ad',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true,\r\n            textStyle: {\r\n              color: '#fff'\r\n            }\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [150, 212, 201, 154, 190, 330, 410]\r\n        },\r\n        {\r\n          name: 'Search Engine',\r\n          type: 'bar',\r\n          stack: 'total',\r\n          label: {\r\n            show: true\r\n          },\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          data: [1000, 900, 700, 1100, 1200, 1300, 1350]\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 15,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '15%',\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default stackedHorizontalBarChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst barRaceChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-bar-race-chart-example');\r\n\r\n  let data = Array.from(Array(7).keys()).map(() =>\r\n    Math.round(Math.random() * 200)\r\n  );\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      xAxis: {\r\n        max: 'dataMax',\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: ['A', 'B', 'C', 'D', 'E', 'F', 'G'],\r\n        inverse: true,\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        animationDuration: 300,\r\n        animationDurationUpdate: 300,\r\n        max: 4 // only the largest 5 bars will be displayed\r\n      },\r\n      series: [\r\n        {\r\n          realtimeSort: true,\r\n          name: 'X',\r\n          type: 'bar',\r\n          data,\r\n          label: {\r\n            show: true,\r\n            position: 'right',\r\n            color: getColor('tertiary-color'),\r\n            fontWeight: 500,\r\n            valueAnimation: true\r\n          },\r\n          itemStyle: {\r\n            color: getColor('primary'),\r\n            barBorderRadius: [0, 3, 3, 0]\r\n          }\r\n        }\r\n      ],\r\n      animationDuration: 0,\r\n      animationDurationUpdate: 3000,\r\n      animationEasing: 'linear',\r\n      animationEasingUpdate: 'linear',\r\n      grid: {\r\n        right: '10%',\r\n        left: 5,\r\n        bottom: 5,\r\n        top: 5,\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    const run = () => {\r\n      data = data.map(item =>\r\n        Math.random() > 0.9\r\n          ? item + Math.round(Math.random() * 2000)\r\n          : item + Math.round(Math.random() * 200)\r\n      );\r\n\r\n      chart.setOption({\r\n        series: [\r\n          {\r\n            data\r\n          }\r\n        ]\r\n      });\r\n    };\r\n\r\n    setTimeout(() => {\r\n      run();\r\n    }, 0);\r\n    setInterval(() => {\r\n      run();\r\n    }, 3000);\r\n  }\r\n};\r\n\r\nexport default barRaceChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst barGradientChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-bar-gradient-chart-example');\r\n\r\n  const tooltipFormatter = params => {\r\n    return `<div> \r\n        <h6 class=\"fs-9 text-body-tertiary mb-0\">\r\n        <span class=\"fas fa-circle me-1 text-primary\"></span> ${params[0].name} : ${params[0].value} \r\n         </h6>\r\n      </div> `;\r\n  };\r\n\r\n  const dataAxis = [\r\n    'A',\r\n    'B',\r\n    'C',\r\n    'D',\r\n    'E',\r\n    'F',\r\n    'G',\r\n    'H',\r\n    'I',\r\n    'J',\r\n    'K',\r\n    'L',\r\n    'M',\r\n    'N',\r\n    'O',\r\n    'P',\r\n    'Q',\r\n    'R',\r\n    'S',\r\n    'T'\r\n  ];\r\n  const data = [\r\n    320, 190, 200, 210, 256, 451, 111, 150, 442, 321, 100, 451, 201, 232, 140,\r\n    124, 444, 333, 222, 111\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      title: {\r\n        text: 'Gradient and Clickable bar chart',\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        },\r\n        left: 'center'\r\n      },\r\n      xAxis: {\r\n        data: dataAxis,\r\n        axisLabel: {\r\n          inside: true,\r\n          textStyle: {\r\n            color: '#fff'\r\n          }\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        axisLine: {\r\n          show: false\r\n        },\r\n        z: 10\r\n      },\r\n      yAxis: {\r\n        axisLine: {\r\n          show: false\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        axisLabel: {\r\n          textStyle: {\r\n            color: getColor('quaternary-color')\r\n          }\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        }\r\n      },\r\n      dataZoom: [\r\n        {\r\n          type: 'inside'\r\n        }\r\n      ],\r\n      series: [\r\n        {\r\n          type: 'bar',\r\n          name: 'Total',\r\n          showBackground: true,\r\n          itemStyle: {\r\n            color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n              { offset: 0, color: getColor('info') },\r\n              { offset: 0.5, color: getColor('primary') },\r\n              { offset: 1, color: getColor('primary') }\r\n            ]),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [\r\n                { offset: 0, color: getColor('primary') },\r\n                { offset: 0.7, color: getColor('primary') },\r\n                { offset: 1, color: getColor('info') }\r\n              ])\r\n            }\r\n          },\r\n          data\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '10%',\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    const zoomSize = 6;\r\n    chart.on('click', params => {\r\n      chart.dispatchAction({\r\n        type: 'dataZoom',\r\n        startValue: dataAxis[Math.max(params.dataIndex - zoomSize / 2, 0)],\r\n        endValue:\r\n          dataAxis[Math.min(params.dataIndex + zoomSize / 2, data.length - 1)]\r\n      });\r\n    });\r\n  }\r\n};\r\n\r\nexport default barGradientChartInit;\r\n", "import {\r\n  echartSetOption,\r\n  handleTooltipPosition,\r\n  tooltipFormatter\r\n} from '../echarts-utils';\r\n\r\nconst barLineMixedChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-bar-line-mixed-chart-example'\r\n  );\r\n  const months = [\r\n    'January',\r\n    'February',\r\n    'March',\r\n    'April',\r\n    'May',\r\n    'June',\r\n    'July',\r\n    'August',\r\n    'September',\r\n    'October',\r\n    'November',\r\n    'December'\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'cross',\r\n          crossStyle: {\r\n            color: getColor('quaternary-color')\r\n          },\r\n          label: {\r\n            show: true,\r\n            backgroundColor: getColor('tertiary-color'),\r\n            color: getColor('body-highlight-bg')\r\n          }\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position: (...params) => handleTooltipPosition(params),\r\n        formatter: params => tooltipFormatter(params)\r\n      },\r\n      toolbox: {\r\n        top: 0,\r\n        feature: {\r\n          dataView: { show: false },\r\n          magicType: {\r\n            show: true,\r\n            type: ['line', 'bar']\r\n          },\r\n          restore: { show: true },\r\n          saveAsImage: { show: true }\r\n        },\r\n        iconStyle: {\r\n          borderColor: getColor('tertiary-color'),\r\n          borderWidth: 1\r\n        },\r\n\r\n        emphasis: {\r\n          iconStyle: {\r\n            textFill: getColor('tertiary-color')\r\n          }\r\n        }\r\n      },\r\n      legend: {\r\n        top: 40,\r\n        data: ['Evaporation', 'Precipitation', 'Average temperature'],\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      xAxis: [\r\n        {\r\n          type: 'category',\r\n          data: months,\r\n          axisLabel: {\r\n            color: getColor('quaternary-color'),\r\n            formatter: value => value.slice(0, 3)\r\n          },\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          axisLine: {\r\n            show: true,\r\n            lineStyle: {\r\n              color: getColor('tertiary-bg')\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      yAxis: [\r\n        {\r\n          type: 'value',\r\n          min: 0,\r\n          max: 250,\r\n          interval: 50,\r\n          axisLabel: {\r\n            color: getColor('quaternary-color'),\r\n            formatter: '{value} ml'\r\n          },\r\n          splitLine: {\r\n            show: true,\r\n            lineStyle: {\r\n              color: getColor('secondary-bg')\r\n            }\r\n          }\r\n        },\r\n        {\r\n          type: 'value',\r\n          min: 0,\r\n          max: 25,\r\n          interval: 5,\r\n          axisLabel: {\r\n            color: getColor('quaternary-color'),\r\n            formatter: '{value} °C'\r\n          },\r\n\r\n          splitLine: {\r\n            show: true,\r\n            lineStyle: {\r\n              color: getColor('secondary-bg')\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      series: [\r\n        {\r\n          name: 'Evaporation',\r\n          type: 'bar',\r\n          data: [\r\n            2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3\r\n          ],\r\n          itemStyle: {\r\n            color: getColor('primary'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          }\r\n        },\r\n        {\r\n          name: 'Precipitation',\r\n          type: 'bar',\r\n          data: [\r\n            2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3\r\n          ],\r\n          itemStyle: {\r\n            color: getColor('info'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          }\r\n        },\r\n        {\r\n          name: 'Average temperature',\r\n          type: 'line',\r\n          yAxisIndex: 1,\r\n          data: [\r\n            2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2\r\n          ],\r\n          lineStyle: {\r\n            color: getColor('warning')\r\n          },\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          symbol: 'circle',\r\n          symbolSize: 10\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '23%',\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default barLineMixedChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst barWaterFallChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-bar-waterfall-chart-example'\r\n  );\r\n  const days = [\r\n    '2021-06-05',\r\n    '2021-06-06',\r\n    '2021-06-07',\r\n    '2021-06-08',\r\n    '2021-06-09',\r\n    '2021-06-10',\r\n    '2021-06-11',\r\n    '2021-06-12',\r\n    '2021-06-13',\r\n    '2021-06-14',\r\n    '2021-06-15'\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        data: ['Expenditure', 'Income'],\r\n        textStyle: {\r\n          color: getColor('quaternary-color')\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        /* eslint-disable prefer-destructuring */\r\n        formatter: params => {\r\n          let tar;\r\n          if (params[1].value !== '-') {\r\n            tar = params[1];\r\n          } else {\r\n            tar = params[2];\r\n          }\r\n          return `${window.dayjs(tar.name).format('MMM DD')}<br/>${\r\n            tar.seriesName\r\n          } : ${tar.value}`;\r\n        },\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'shadow'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          formatter: value => window.dayjs(value).format('MMM DD'),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        boundaryGap: true,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false },\r\n        min: 600\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Assist',\r\n          type: 'bar',\r\n          stack: 'Total',\r\n          itemStyle: {\r\n            barBorderColor: 'transparent',\r\n            color: 'transparent'\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              barBorderColor: 'transparent',\r\n              color: 'transparent'\r\n            }\r\n          },\r\n          data: [0, 900, 1245, 1530, 1376, 1376, 1511, 1689, 1856, 1495, 1292]\r\n        },\r\n        {\r\n          name: 'Income',\r\n          type: 'bar',\r\n          stack: 'Total',\r\n          label: {\r\n            show: true,\r\n            position: 'top',\r\n            color: getColor('quaternary-color')\r\n          },\r\n          data: [1000, 400, 350, '-', '-', 320, 180, 190, '-', '-', '-'],\r\n          itemStyle: {\r\n            color: getColor('primary'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          }\r\n        },\r\n        {\r\n          name: 'Expenditure',\r\n          type: 'bar',\r\n          stack: 'Total',\r\n          label: {\r\n            show: true,\r\n            position: 'bottom',\r\n            color: getColor('quaternary-color')\r\n          },\r\n          data: ['-', '-', '-', 100, 140, '-', '-', '-', 120, 345, 190],\r\n          itemStyle: {\r\n            color: getColor('success'),\r\n            barBorderRadius: [3, 3, 0, 0]\r\n          }\r\n        }\r\n      ],\r\n      grid: { right: '3%', left: '10%', bottom: '10%', top: '10%' }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default barWaterFallChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from '../echarts-utils';\r\n\r\nconst barTimelineChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-bar-timeline-chart-example');\r\n  const months = [\r\n    'January',\r\n    'February',\r\n    'March',\r\n    'April',\r\n    'May',\r\n    'June',\r\n    'July',\r\n    'August',\r\n    'September',\r\n    'October',\r\n    'November',\r\n    'December'\r\n  ];\r\n  const dataMap = {};\r\n\r\n  const dataFormatter = obj => {\r\n    return Object.keys(obj).reduce((acc, val) => {\r\n      return {\r\n        ...acc,\r\n        [val]: obj[val].map((value, index) => ({\r\n          name: months[index],\r\n          value\r\n        }))\r\n      };\r\n    }, {});\r\n  };\r\n\r\n  dataMap.dataTI = dataFormatter({\r\n    2016: [\r\n      88.68, 112.38, 1400, 262.42, 589.56, 882.41, 625.61, 684.6, 90.26,\r\n      1461.51, 892.83, 966.5\r\n    ],\r\n    2017: [\r\n      88.8, 103.35, 1461.81, 276.77, 634.94, 939.43, 672.76, 750.14, 93.81,\r\n      1545.05, 925.1, 1011.03\r\n    ],\r\n    2018: [\r\n      101.26, 110.19, 1804.72, 311.97, 762.1, 1133.42, 783.8, 915.38, 101.84,\r\n      1816.31, 986.02, 1200.18\r\n    ],\r\n    2019: [\r\n      112.83, 122.58, 2034.59, 313.58, 907.95, 1302.02, 916.72, 1088.94, 111.8,\r\n      2100.11, 1095.96, 1418.09\r\n    ],\r\n    2020: [\r\n      118.29, 128.85, 2207.34, 477.59, 929.6, 1414.9, 980.57, 1154.33, 113.82,\r\n      2261.86, 1163.08, 1495.45\r\n    ],\r\n    2021: [\r\n      124.36, 145.58, 2562.81, 554.48, 1095.28, 1631.08, 1050.15, 1302.9,\r\n      114.15, 2540.1, 1360.56, 1729.02\r\n    ],\r\n    2022: [\r\n      136.27, 159.72, 2905.73, 641.42, 1306.3, 1915.57, 1277.44, 1701.5, 124.94,\r\n      3064.78, 1583.04, 2015.31\r\n    ]\r\n  });\r\n\r\n  dataMap.dataSI = dataFormatter({\r\n    2016: [\r\n      2026.51, 2135.07, 5271.57, 2357.04, 1773.21, 3869.4, 1580.83, 2971.68,\r\n      4381.2, 10524.96, 7164.75, 2245.9\r\n    ],\r\n    2017: [\r\n      2191.43, 2457.08, 6110.43, 2755.66, 2374.96, 4566.83, 1915.29, 3365.31,\r\n      4969.95, 12282.89, 8511.51, 2711.18\r\n    ],\r\n    2018: [\r\n      2509.4, 2892.53, 7201.88, 3454.49, 3193.67, 5544.14, 2475.45, 3695.58,\r\n      5571.06, 14471.26, 10154.25, 3370.96\r\n    ],\r\n    2019: [\r\n      2626.41, 3709.78, 8701.34, 4242.36, 4376.19, 7158.84, 3097.12, 4319.75,\r\n      6085.84, 16993.34, 11567.42, 4198.93\r\n    ],\r\n    2020: [\r\n      2855.55, 3987.84, 8959.83, 3993.8, 5114, 7906.34, 3541.92, 4060.72,\r\n      6001.78, 18566.37, 11908.49, 4905.22\r\n    ],\r\n    2021: [\r\n      3388.38, 4840.23, 10707.68, 5234, 6367.69, 9976.82, 4506.31, 5025.15,\r\n      7218.32, 21753.93, 14297.93, 6436.62\r\n    ],\r\n    2022: [\r\n      3752.48, 5928.32, 13126.86, 6635.26, 8037.69, 12152.15, 5611.48, 5962.41,\r\n      7927.89, 25203.28, 16555.58, 8309.38\r\n    ]\r\n  });\r\n\r\n  dataMap.dataPI = dataFormatter({\r\n    2016: [\r\n      4854.33, 1658.19, 3340.54, 1611.07, 1542.26, 3295.45, 1413.83, 1857.42,\r\n      4776.2, 6612.22, 5360.1, 2137.77\r\n    ],\r\n    2017: [\r\n      5837.55, 1902.31, 3895.36, 1846.18, 1934.35, 3798.26, 1687.07, 2096.35,\r\n      5508.48, 7914.11, 6281.86, 2390.29\r\n    ],\r\n    2018: [\r\n      7236.15, 2250.04, 4600.72, 2257.99, 2467.41, 4486.74, 2025.44, 2493.04,\r\n      6821.11, 9730.91, 7613.46, 2789.78\r\n    ],\r\n    2019: [\r\n      8375.76, 2886.65, 5276.04, 2759.46, 3212.06, 5207.72, 2412.26, 2905.68,\r\n      7872.23, 11888.53, 8799.31, 3234.64\r\n    ],\r\n    2020: [\r\n      9179.19, 3405.16, 6068.31, 2886.92, 3696.65, 5891.25, 2756.26, 3371.95,\r\n      8930.85, 13629.07, 9918.78, 3662.15\r\n    ],\r\n    2021: [\r\n      10600.84, 4238.65, 7123.77, 3412.38, 4209.03, 6849.37, 3111.12, 4040.55,\r\n      9833.51, 17131.45, 12063.82, 4193.69\r\n    ],\r\n    2022: [\r\n      12363.18, 5219.24, 8483.17, 3960.87, 5015.89, 8158.98, 3679.91, 4918.09,\r\n      11142.86, 20842.21, 14180.23, 4975.96\r\n    ]\r\n  });\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      baseOption: {\r\n        timeline: {\r\n          axisType: 'category',\r\n          autoPlay: false,\r\n          playInterval: 1000,\r\n          data: [\r\n            '2016-01-01',\r\n            '2017-01-01',\r\n            '2018-01-01',\r\n            '2019-01-01',\r\n            '2020-01-01',\r\n            '2021-01-01',\r\n            '2022-01-01'\r\n          ],\r\n          label: {\r\n            formatter: s => {\r\n              return new Date(s).getFullYear();\r\n            }\r\n          },\r\n          lineStyle: {\r\n            color: getColor('info')\r\n          },\r\n          itemStyle: {\r\n            color: getColor('secondary')\r\n          },\r\n          checkpointStyle: {\r\n            color: getColor('primary'),\r\n            shadowBlur: 0,\r\n            shadowOffsetX: 0,\r\n            shadowOffsetY: 0\r\n          },\r\n          controlStyle: {\r\n            color: getColor('info')\r\n          }\r\n        },\r\n        title: {\r\n          textStyle: {\r\n            color: getColor('tertiary-color')\r\n          }\r\n        },\r\n        tooltip: {\r\n          trigger: 'axis',\r\n          axisPointer: {\r\n            type: 'shadow'\r\n          },\r\n          padding: [7, 10],\r\n          backgroundColor: getColor('body-highlight-bg'),\r\n          borderColor: getColor('border-color'),\r\n          textStyle: { color: getColor('light-text-emphasis') },\r\n          borderWidth: 1,\r\n          transitionDuration: 0,\r\n          formatter: tooltipFormatter\r\n        },\r\n        legend: {\r\n          left: 'right',\r\n          data: ['Primary industry', 'Secondary industry', 'Tertiary Industry'],\r\n          textStyle: {\r\n            color: getColor('tertiary-color')\r\n          }\r\n        },\r\n        calculable: true,\r\n        xAxis: [\r\n          {\r\n            type: 'category',\r\n            data: months,\r\n            splitLine: { show: false },\r\n            axisLabel: {\r\n              color: getColor('quaternary-color')\r\n            },\r\n            axisLine: {\r\n              lineStyle: {\r\n                color: getColor('quaternary-color')\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        yAxis: [\r\n          {\r\n            type: 'value',\r\n            axisLabel: {\r\n              formatter: value => `${value / 1000}k`,\r\n              color: getColor('quaternary-color')\r\n            },\r\n            splitLine: {\r\n              lineStyle: {\r\n                color: getColor('secondary-bg')\r\n              }\r\n            }\r\n          }\r\n        ],\r\n        series: [\r\n          {\r\n            name: 'Primary industry',\r\n            type: 'bar',\r\n            itemStyle: {\r\n              color: getColor('primary'),\r\n              barBorderRadius: [3, 3, 0, 0]\r\n            }\r\n          },\r\n          {\r\n            name: 'Secondary industry',\r\n            type: 'bar',\r\n            itemStyle: {\r\n              color: getColor('info'),\r\n              barBorderRadius: [3, 3, 0, 0]\r\n            }\r\n          },\r\n          {\r\n            name: 'Tertiary Industry',\r\n            type: 'bar',\r\n            itemStyle: {\r\n              color: getColor('warning'),\r\n              barBorderRadius: [3, 3, 0, 0]\r\n            }\r\n          }\r\n        ],\r\n        grid: {\r\n          top: '10%',\r\n          bottom: '15%',\r\n          left: 5,\r\n          right: 10,\r\n          containLabel: true\r\n        }\r\n      },\r\n      options: [\r\n        {\r\n          title: { text: '2016' },\r\n          series: [\r\n            { data: dataMap.dataPI['2016'] },\r\n            { data: dataMap.dataSI['2016'] },\r\n            { data: dataMap.dataTI['2016'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2017' },\r\n          series: [\r\n            { data: dataMap.dataPI['2017'] },\r\n            { data: dataMap.dataSI['2017'] },\r\n            { data: dataMap.dataTI['2017'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2018' },\r\n          series: [\r\n            { data: dataMap.dataPI['2018'] },\r\n            { data: dataMap.dataSI['2018'] },\r\n            { data: dataMap.dataTI['2018'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2019' },\r\n          series: [\r\n            { data: dataMap.dataPI['2019'] },\r\n            { data: dataMap.dataSI['2019'] },\r\n            { data: dataMap.dataTI['2019'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2020' },\r\n          series: [\r\n            { data: dataMap.dataPI['2020'] },\r\n            { data: dataMap.dataSI['2020'] },\r\n            { data: dataMap.dataTI['2020'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2021' },\r\n          series: [\r\n            { data: dataMap.dataPI['2021'] },\r\n            { data: dataMap.dataSI['2021'] },\r\n            { data: dataMap.dataTI['2021'] }\r\n          ]\r\n        },\r\n        {\r\n          title: { text: '2022' },\r\n          series: [\r\n            { data: dataMap.dataPI['2022'] },\r\n            { data: dataMap.dataSI['2022'] },\r\n            { data: dataMap.dataTI['2022'] }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default barTimelineChartInit;\r\n", "import { echartSetOption, handleTooltipPosition } from '../echarts-utils';\r\n\r\nconst basicCandlestickChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-basic-candlestick-chart-example'\r\n  );\r\n\r\n  const data = [\r\n    ['2013/1/24', 2320.26, 2320.26, 2287.3, 2362.94],\r\n    ['2013/1/25', 2300, 2291.3, 2288.26, 2308.38],\r\n    ['2013/1/28', 2295.35, 2346.5, 2295.35, 2346.92],\r\n    ['2013/1/29', 2347.22, 2358.98, 2337.35, 2363.8],\r\n    ['2013/1/30', 2360.75, 2382.48, 2347.89, 2383.76],\r\n    ['2013/1/31', 2383.43, 2385.42, 2371.23, 2391.82],\r\n    ['2013/2/1', 2377.41, 2419.02, 2369.57, 2421.15],\r\n    ['2013/2/4', 2425.92, 2428.15, 2417.58, 2440.38],\r\n    ['2013/2/5', 2411, 2433.13, 2403.3, 2437.42],\r\n    ['2013/2/6', 2432.68, 2434.48, 2427.7, 2441.73],\r\n    ['2013/2/7', 2430.69, 2418.53, 2394.22, 2433.89],\r\n    ['2013/2/8', 2416.62, 2432.4, 2414.4, 2443.03],\r\n    ['2013/2/18', 2441.91, 2421.56, 2415.43, 2444.8],\r\n    ['2013/2/19', 2420.26, 2382.91, 2373.53, 2427.07],\r\n    ['2013/2/20', 2383.49, 2397.18, 2370.61, 2397.94],\r\n    ['2013/2/21', 2378.82, 2325.95, 2309.17, 2378.82],\r\n    ['2013/2/22', 2322.94, 2314.16, 2308.76, 2330.88],\r\n    ['2013/2/25', 2320.62, 2325.82, 2315.01, 2338.78],\r\n    ['2013/2/26', 2313.74, 2293.34, 2289.89, 2340.71],\r\n    ['2013/2/27', 2297.77, 2313.22, 2292.03, 2324.63],\r\n    ['2013/2/28', 2322.32, 2365.59, 2308.92, 2366.16],\r\n    ['2013/3/1', 2364.54, 2359.51, 2330.86, 2369.65],\r\n    ['2013/3/4', 2332.08, 2273.4, 2259.25, 2333.54],\r\n    ['2013/3/5', 2274.81, 2326.31, 2270.1, 2328.14],\r\n    ['2013/3/6', 2333.61, 2347.18, 2321.6, 2351.44],\r\n    ['2013/3/7', 2340.44, 2324.29, 2304.27, 2352.02],\r\n    ['2013/3/8', 2326.42, 2318.61, 2314.59, 2333.67],\r\n    ['2013/3/11', 2314.68, 2310.59, 2296.58, 2320.96],\r\n    ['2013/3/12', 2309.16, 2286.6, 2264.83, 2333.29],\r\n    ['2013/3/13', 2282.17, 2263.97, 2253.25, 2286.33],\r\n    ['2013/3/14', 2255.77, 2270.28, 2253.31, 2276.22],\r\n    ['2013/3/15', 2269.31, 2278.4, 2250, 2312.08],\r\n    ['2013/3/18', 2267.29, 2240.02, 2239.21, 2276.05],\r\n    ['2013/3/19', 2244.26, 2257.43, 2232.02, 2261.31],\r\n    ['2013/3/20', 2257.74, 2317.37, 2257.42, 2317.86],\r\n    ['2013/3/21', 2318.21, 2324.24, 2311.6, 2330.81],\r\n    ['2013/3/22', 2321.4, 2328.28, 2314.97, 2332],\r\n    ['2013/3/25', 2334.74, 2326.72, 2319.91, 2344.89],\r\n    ['2013/3/26', 2318.58, 2297.67, 2281.12, 2319.99],\r\n    ['2013/3/27', 2299.38, 2301.26, 2289, 2323.48],\r\n    ['2013/3/28', 2273.55, 2236.3, 2232.91, 2273.55],\r\n    ['2013/3/29', 2238.49, 2236.62, 2228.81, 2246.87],\r\n    ['2013/4/1', 2229.46, 2234.4, 2227.31, 2243.95],\r\n    ['2013/4/2', 2234.9, 2227.74, 2220.44, 2253.42],\r\n    ['2013/4/3', 2232.69, 2225.29, 2217.25, 2241.34],\r\n    ['2013/4/8', 2196.24, 2211.59, 2180.67, 2212.59],\r\n    ['2013/4/9', 2215.47, 2225.77, 2215.47, 2234.73],\r\n    ['2013/4/10', 2224.93, 2226.13, 2212.56, 2233.04],\r\n    ['2013/4/11', 2236.98, 2219.55, 2217.26, 2242.48],\r\n    ['2013/4/12', 2218.09, 2206.78, 2204.44, 2226.26]\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        position: (...params) => handleTooltipPosition(params)\r\n      },\r\n      toolbox: {\r\n        top: 0,\r\n        feature: {\r\n          dataZoom: {\r\n            yAxisIndex: false\r\n          },\r\n          restore: { show: true }\r\n        },\r\n        iconStyle: {\r\n          borderColor: getColor('tertiary-color'),\r\n          borderWidth: 1\r\n        },\r\n\r\n        emphasis: {\r\n          iconStyle: {\r\n            textFill: getColor('tertiary-color')\r\n          }\r\n        }\r\n      },\r\n      dataZoom: [\r\n        {\r\n          type: 'inside',\r\n          start: 0,\r\n          end: 100,\r\n          minValueSpan: 10\r\n        }\r\n      ],\r\n      xAxis: {\r\n        type: 'category',\r\n        data: data.map(item => item[0]),\r\n        scale: true,\r\n        splitLine: { show: false },\r\n        splitNumber: 10,\r\n        min: 'dataMin',\r\n        max: 'dataMax',\r\n        boundaryGap: true,\r\n        axisPointer: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'dashed'\r\n          }\r\n        },\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: { show: false },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          formatter: value =>\r\n            window.dayjs(value, 'YYYY-MM-DD').format('MMM DD'),\r\n          margin: 15,\r\n          fontWeight: 500\r\n        }\r\n      },\r\n      yAxis: {\r\n        scale: true,\r\n        axisPointer: { show: false },\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('secondary-bg'),\r\n            type: 'dashed'\r\n          }\r\n        },\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: true,\r\n          color: getColor('quaternary-color'),\r\n          margin: 15,\r\n          fontWeight: 500\r\n        },\r\n        axisTick: { show: false },\r\n        axisLine: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'candlestick',\r\n          name: 'Volume',\r\n          data: data.map(item => item.slice(1)),\r\n          itemStyle: {\r\n            color: getColor('warning'),\r\n            color0: getColor('primary'),\r\n            borderColor: getColor('warning'),\r\n            borderColor0: getColor('primary')\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: '15%',\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default basicCandlestickChartInit;\r\n", "import { echartSetOption, handleTooltipPosition } from '../echarts-utils';\r\n\r\nconst candlestickMixedChartInit = () => {\r\n  const { getColor, getData, getPastDates } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-candlestick-mixed-chart-example'\r\n  );\r\n  const colorList = [\r\n    getColor('primary'),\r\n    getColor('info'),\r\n    getColor('light-text-emphasis'),\r\n    getColor('warning')\r\n  ];\r\n\r\n  /* eslint-disable no-continue */\r\n  const calculateMA = (dayCount, data) => {\r\n    const result = [];\r\n    for (let i = 0, len = data.length; i < len; i += 1) {\r\n      if (i < dayCount) {\r\n        result.push('-');\r\n        continue;\r\n      }\r\n      let sum = 0;\r\n      for (let j = 0; j < dayCount; j += 1) {\r\n        sum += data[i - j][1];\r\n      }\r\n      result.push((sum / dayCount).toFixed(2));\r\n    }\r\n    return result;\r\n  };\r\n\r\n  const dates = getPastDates(61).map(date =>\r\n    window.dayjs(date).format('MMM DD, YYYY')\r\n  );\r\n\r\n  const data = [\r\n    [17512.58, 17633.11, 17434.27, 17642.81, 86160000],\r\n    [17652.36, 17716.66, 17652.36, 17790.11, 79330000],\r\n    [17716.05, 17685.09, 17669.72, 17755.7, 102600000],\r\n    [17661.74, 17792.75, 17568.02, 17811.48, 104890000],\r\n    [17799.39, 17737, 17710.67, 17806.38, 85230000],\r\n    [17718.03, 17603.32, 17579.56, 17718.03, 115230000],\r\n    [17605.45, 17716.05, 17542.54, 17723.55, 99410000],\r\n    [17687.28, 17541.96, 17484.23, 17687.28, 90120000],\r\n    [17555.39, 17576.96, 17528.16, 17694.51, 79990000],\r\n    [17586.48, 17556.41, 17555.9, 17731.63, 107100000],\r\n    [17571.34, 17721.25, 17553.57, 17744.43, 81020000],\r\n    [17741.66, 17908.28, 17741.66, 17918.35, 91710000],\r\n    [17912.25, 17926.43, 17885.44, 17962.14, 84510000],\r\n    [17925.95, 17897.46, 17867.41, 17937.65, 118160000],\r\n    [17890.2, 18004.16, 17848.22, 18009.53, 89390000],\r\n    [18012.1, 18053.6, 17984.43, 18103.46, 89820000],\r\n    [18059.49, 18096.27, 18031.21, 18167.63, 100210000],\r\n    [18092.84, 17982.52, 17963.89, 18107.29, 102720000],\r\n    [17985.05, 18003.75, 17909.89, 18026.85, 134120000],\r\n    [17990.94, 17977.24, 17855.55, 17990.94, 83770000],\r\n    [17987.38, 17990.32, 17934.17, 18043.77, 92570000],\r\n    [17996.14, 18041.55, 17920.26, 18084.66, 109090000],\r\n    [18023.88, 17830.76, 17796.55, 18035.73, 100920000],\r\n    [17813.09, 17773.64, 17651.98, 17814.83, 136670000],\r\n    [17783.78, 17891.16, 17773.71, 17912.35, 80100000],\r\n    [17870.75, 17750.91, 17670.88, 17870.75, 97060000],\r\n    [17735.02, 17651.26, 17609.01, 17738.06, 95020000],\r\n    [17664.48, 17660.71, 17615.82, 17736.11, 81530000],\r\n    [17650.3, 17740.63, 17580.38, 17744.54, 80020000],\r\n    [17743.85, 17705.91, 17668.38, 17783.16, 85590000],\r\n    [17726.66, 17928.35, 17726.66, 17934.61, 75790000],\r\n    [17919.03, 17711.12, 17711.05, 17919.03, 87390000],\r\n    [17711.12, 17720.5, 17625.38, 17798.19, 88560000],\r\n    [17711.12, 17535.32, 17512.48, 17734.74, 86640000],\r\n    [17531.76, 17710.71, 17531.76, 17755.8, 88440000],\r\n    [17701.46, 17529.98, 17469.92, 17701.46, 103260000],\r\n    [17501.28, 17526.62, 17418.21, 17636.22, 79120000],\r\n    [17514.16, 17435.4, 17331.07, 17514.16, 95530000],\r\n    [17437.32, 17500.94, 17437.32, 17571.75, 111990000],\r\n    [17507.04, 17492.93, 17480.05, 17550.7, 87790000],\r\n    [17525.19, 17706.05, 17525.19, 17742.59, 86480000],\r\n    [17735.09, 17851.51, 17735.09, 17891.71, 79180000],\r\n    [17859.52, 17828.29, 17803.82, 17888.66, 68940000],\r\n    [17826.85, 17873.22, 17824.73, 17873.22, 73190000],\r\n    [17891.5, 17787.2, 17724.03, 17899.24, 147390000],\r\n    [17754.55, 17789.67, 17664.79, 17809.18, 78530000],\r\n    [17789.05, 17838.56, 17703.55, 17838.56, 75560000],\r\n    [17799.8, 17807.06, 17689.68, 17833.17, 82270000],\r\n    [17825.69, 17920.33, 17822.81, 17949.68, 71870000],\r\n    [17936.22, 17938.28, 17936.22, 18003.23, 78750000],\r\n    [17931.91, 18005.05, 17931.91, 18016, 71260000],\r\n    [17969.98, 17985.19, 17915.88, 18005.22, 69690000],\r\n    [17938.82, 17865.34, 17812.34, 17938.82, 90540000],\r\n    [17830.5, 17732.48, 17731.35, 17893.28, 101690000],\r\n    [17710.77, 17674.82, 17595.79, 17733.92, 93740000],\r\n    [17703.65, 17640.17, 17629.01, 17762.96, 94130000],\r\n    [17602.23, 17733.1, 17471.29, 17754.91, 91950000],\r\n    [17733.44, 17675.16, 17602.78, 17733.44, 248680000],\r\n    [17736.87, 17804.87, 17736.87, 17946.36, 99380000],\r\n    [17827.33, 17829.73, 17799.8, 17877.84, 85130000],\r\n    [17832.67, 17780.83, 17770.36, 17920.16, 89440000]\r\n  ];\r\n\r\n  const dataMA5 = calculateMA(5, data);\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      animation: false,\r\n      color: colorList,\r\n      legend: {\r\n        top: 0,\r\n        data: ['MA1', 'MA5', 'Volume'],\r\n        textStyle: {\r\n          color: getColor('quaternary-color')\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position: (...params) => handleTooltipPosition(params)\r\n      },\r\n      axisPointer: {\r\n        link: [\r\n          {\r\n            xAxisIndex: [0, 1]\r\n          }\r\n        ]\r\n      },\r\n      dataZoom: [\r\n        {\r\n          type: 'slider',\r\n          xAxisIndex: [0, 1],\r\n          realtime: false,\r\n          start: 20,\r\n          end: 70,\r\n          top: 35,\r\n          height: 15,\r\n          handleIcon:\r\n            'path://M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',\r\n          handleSize: '120%'\r\n        },\r\n        {\r\n          type: 'inside',\r\n          xAxisIndex: [0, 1],\r\n          start: 40,\r\n          end: 70,\r\n          top: 30,\r\n          height: 20\r\n        }\r\n      ],\r\n      xAxis: [\r\n        {\r\n          type: 'category',\r\n          data: dates,\r\n          boundaryGap: false,\r\n          axisLine: {\r\n            lineStyle: { color: getColor('tertiary-bg') }\r\n          },\r\n          axisLabel: {\r\n            color: getColor('quaternary-color'),\r\n            formatter: value => window.dayjs(value).format('MMM DD')\r\n          },\r\n          min: 'dataMin',\r\n          max: 'dataMax',\r\n          axisPointer: {\r\n            show: true\r\n          }\r\n        },\r\n        {\r\n          type: 'category',\r\n          gridIndex: 1,\r\n          data: dates,\r\n          scale: true,\r\n          boundaryGap: false,\r\n          splitLine: { show: false },\r\n          axisLabel: { show: false },\r\n          axisTick: { show: false },\r\n          axisLine: { lineStyle: { color: 'blue' } },\r\n          splitNumber: 20,\r\n          min: 'dataMin',\r\n          max: 'dataMax',\r\n          axisPointer: {\r\n            type: 'shadow',\r\n            label: { show: false },\r\n            triggerTooltip: true\r\n          }\r\n        }\r\n      ],\r\n      yAxis: [\r\n        {\r\n          scale: true,\r\n          splitNumber: 2,\r\n          axisLine: { show: false },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: getColor('secondary-bg')\r\n            }\r\n          },\r\n          axisTick: { show: false },\r\n          axisLabel: {\r\n            color: getColor('quaternary-color')\r\n          }\r\n        },\r\n        {\r\n          scale: true,\r\n          gridIndex: 1,\r\n          splitNumber: 2,\r\n          axisLabel: { show: false },\r\n          axisLine: { show: false },\r\n          axisTick: { show: false },\r\n          splitLine: { show: false }\r\n        }\r\n      ],\r\n      grid: [\r\n        {\r\n          left: 5,\r\n          right: 12,\r\n          // top: 110,\r\n          bottom: 60,\r\n          height: 160,\r\n          containLabel: true\r\n        },\r\n        {\r\n          left: 50,\r\n          right: 12,\r\n          height: 40,\r\n          top: 260,\r\n          containLabel: true\r\n        }\r\n      ],\r\n      series: [\r\n        {\r\n          name: 'Volume',\r\n          type: 'bar',\r\n          xAxisIndex: 1,\r\n          yAxisIndex: 1,\r\n          itemStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              color: getColor('primary')\r\n            }\r\n          },\r\n          data: data.map(item => item[4])\r\n        },\r\n        {\r\n          type: 'candlestick',\r\n          name: 'MA1',\r\n          data,\r\n          itemStyle: {\r\n            color: getColor('success'),\r\n            color0: getColor('info'),\r\n            borderColor: getColor('success'),\r\n            borderColor0: getColor('info')\r\n          }\r\n        },\r\n        {\r\n          name: 'MA5',\r\n          type: 'line',\r\n          data: dataMA5,\r\n          smooth: true,\r\n          showSymbol: false,\r\n          lineStyle: {\r\n            width: 1,\r\n            color: getColor('primary')\r\n          }\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default candlestickMixedChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst sessionByCountryMapInit = () => {\r\n  const { getColor, rgbaColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-session-by-country-map');\r\n  const data = [\r\n    { name: 'Afghanistan', value: 28397.812 },\r\n    { name: 'Angola', value: 19549.124 },\r\n    { name: 'Albania', value: 3150.143 },\r\n    { name: 'United Arab Emirates', value: 8441.537 },\r\n    { name: 'Argentina', value: 40374.224 },\r\n    { name: 'Armenia', value: 2963.496 },\r\n    { name: 'French Southern and Antarctic Lands', value: 268.065 },\r\n    { name: 'Australia', value: 22404.488 },\r\n    { name: 'Austria', value: 8401.924 },\r\n    { name: 'Azerbaijan', value: 9094.718 },\r\n    { name: 'Burundi', value: 9232.753 },\r\n    { name: 'Belgium', value: 10941.288 },\r\n    { name: 'Benin', value: 9509.798 },\r\n    { name: 'Burkina Faso', value: 15540.284 },\r\n    { name: 'Bangladesh', value: 151125.475 },\r\n    { name: 'Bulgaria', value: 7389.175 },\r\n    { name: 'The Bahamas', value: 66402.316 },\r\n    { name: 'Bosnia and Herzegovina', value: 3845.929 },\r\n    { name: 'Belarus', value: 9491.07 },\r\n    { name: 'Belize', value: 308.595 },\r\n    { name: 'Bermuda', value: 64.951 },\r\n    { name: 'Bolivia', value: 716.939 },\r\n    { name: 'Brazil', value: 195210.154 },\r\n    { name: 'Brunei', value: 27.223 },\r\n    { name: 'Bhutan', value: 716.939 },\r\n    { name: 'Botswana', value: 1969.341 },\r\n    { name: 'Central African Rep.', value: 4349.921 },\r\n    { name: 'Canada', value: 34126.24 },\r\n    { name: 'Switzerland', value: 7830.534 },\r\n    { name: 'Chile', value: 17150.76 },\r\n    { name: 'China', value: 1359821.465 },\r\n    { name: \"Côte d'Ivoire\", value: 60508.978 },\r\n    { name: 'Cameroon', value: 20624.343 },\r\n    { name: 'Dem. Rep. Congo', value: 62191.161 },\r\n    { name: 'Congo', value: 3573.024 },\r\n    { name: 'Colombia', value: 46444.798 },\r\n    { name: 'Costa Rica', value: 4669.685 },\r\n    { name: 'Cuba', value: 11281.768 },\r\n    { name: 'Northern Cyprus', value: 1.468 },\r\n    { name: 'Cyprus', value: 1103.685 },\r\n    { name: 'Czech Republic', value: 10553.701 },\r\n    { name: 'Germany', value: 83017.404 },\r\n    { name: 'Djibouti', value: 834.036 },\r\n    { name: 'Denmark', value: 5550.959 },\r\n    { name: 'Dominican Republic', value: 10016.797 },\r\n    { name: 'Algeria', value: 37062.82 },\r\n    { name: 'Ecuador', value: 15001.072 },\r\n    { name: 'Egypt', value: 78075.705 },\r\n    { name: 'Eritrea', value: 5741.159 },\r\n    { name: 'Spain', value: 46182.038 },\r\n    { name: 'Estonia', value: 1298.533 },\r\n    { name: 'Ethiopia', value: 87095.281 },\r\n    { name: 'Finland', value: 5367.693 },\r\n    { name: 'Fiji', value: 860.559 },\r\n    { name: 'Falkland Islands', value: 49.581 },\r\n    { name: 'France', value: 63230.866 },\r\n    { name: 'Gabon', value: 1556.222 },\r\n    { name: 'United Kingdom', value: 62066.35 },\r\n    { name: 'Georgia', value: 4388.674 },\r\n    { name: 'Ghana', value: 24262.901 },\r\n    { name: 'Eq. Guinea', value: 10876.033 },\r\n    { name: 'Guinea', value: 10876.033 },\r\n    { name: 'Gambia', value: 1680.64 },\r\n    { name: 'Guinea Bissau', value: 10876.033 },\r\n    { name: 'Equatorial Guinea', value: 696.167 },\r\n    { name: 'Greece', value: 11109.999 },\r\n    { name: 'Greenland', value: 56.546 },\r\n    { name: 'Guatemala', value: 14341.576 },\r\n    { name: 'French Guiana', value: 231.169 },\r\n    { name: 'Guyana', value: 786.126 },\r\n    { name: 'Honduras', value: 7621.204 },\r\n    { name: 'Croatia', value: 4338.027 },\r\n    { name: 'Haiti', value: 9896.4 },\r\n    { name: 'Hungary', value: 10014.633 },\r\n    { name: 'Indonesia', value: 240676.485 },\r\n    { name: 'India', value: 1205624.648 },\r\n    { name: 'Ireland', value: 4467.561 },\r\n    { name: 'Iran', value: 240676.485 },\r\n    { name: 'Iraq', value: 30962.38 },\r\n    { name: 'Iceland', value: 318.042 },\r\n    { name: 'Israel', value: 7420.368 },\r\n    { name: 'Italy', value: 60508.978 },\r\n    { name: 'Jamaica', value: 2741.485 },\r\n    { name: 'Jordan', value: 6454.554 },\r\n    { name: 'Japan', value: 127352.833 },\r\n    { name: 'Kazakhstan', value: 15921.127 },\r\n    { name: 'Kenya', value: 40909.194 },\r\n    { name: 'Kyrgyzstan', value: 5334.223 },\r\n    { name: 'Cambodia', value: 14364.931 },\r\n    { name: 'South Korea', value: 51452.352 },\r\n    { name: 'Kosovo', value: 97.743 },\r\n    { name: 'Kuwait', value: 2991.58 },\r\n    { name: 'Laos', value: 6395.713 },\r\n    { name: 'Lebanon', value: 4341.092 },\r\n    { name: 'Liberia', value: 3957.99 },\r\n    { name: 'Libya', value: 6040.612 },\r\n    { name: 'Sri Lanka', value: 20758.779 },\r\n    { name: 'Lesotho', value: 2008.921 },\r\n    { name: 'Lithuania', value: 3068.457 },\r\n    { name: 'Luxembourg', value: 507.885 },\r\n    { name: 'Latvia', value: 2090.519 },\r\n    { name: 'Morocco', value: 31642.36 },\r\n    { name: 'Moldova', value: 103.619 },\r\n    { name: 'Madagascar', value: 21079.532 },\r\n    { name: 'Mexico', value: 117886.404 },\r\n    { name: 'Macedonia', value: 507.885 },\r\n    { name: 'Mali', value: 13985.961 },\r\n    { name: 'Myanmar', value: 51931.231 },\r\n    { name: 'Montenegro', value: 620.078 },\r\n    { name: 'Mongolia', value: 2712.738 },\r\n    { name: 'Mozambique', value: 23967.265 },\r\n    { name: 'Mauritania', value: 3609.42 },\r\n    { name: 'Malawi', value: 15013.694 },\r\n    { name: 'Malaysia', value: 28275.835 },\r\n    { name: 'Namibia', value: 2178.967 },\r\n    { name: 'New Caledonia', value: 246.379 },\r\n    { name: 'Niger', value: 15893.746 },\r\n    { name: 'Nigeria', value: 159707.78 },\r\n    { name: 'Nicaragua', value: 5822.209 },\r\n    { name: 'Netherlands', value: 16615.243 },\r\n    { name: 'Norway', value: 4891.251 },\r\n    { name: 'Nepal', value: 26846.016 },\r\n    { name: 'New Zealand', value: 4368.136 },\r\n    { name: 'Oman', value: 2802.768 },\r\n    { name: 'Pakistan', value: 173149.306 },\r\n    { name: 'Panama', value: 3678.128 },\r\n    { name: 'Peru', value: 29262.83 },\r\n    { name: 'Philippines', value: 93444.322 },\r\n    { name: 'Papua New Guinea', value: 6858.945 },\r\n    { name: 'Poland', value: 38198.754 },\r\n    { name: 'Puerto Rico', value: 3709.671 },\r\n    { name: 'North Korea', value: 1.468 },\r\n    { name: 'Portugal', value: 10589.792 },\r\n    { name: 'Paraguay', value: 6459.721 },\r\n    { name: 'Qatar', value: 1749.713 },\r\n    { name: 'Romania', value: 21861.476 },\r\n    { name: 'Russia', value: 21861.476 },\r\n    { name: 'Rwanda', value: 10836.732 },\r\n    { name: 'Western Sahara', value: 514.648 },\r\n    { name: 'Saudi Arabia', value: 27258.387 },\r\n    { name: 'Sudan', value: 35652.002 },\r\n    { name: 'S. Sudan', value: 9940.929 },\r\n    { name: 'Senegal', value: 12950.564 },\r\n    { name: 'Solomon Islands', value: 526.447 },\r\n    { name: 'Sierra Leone', value: 5751.976 },\r\n    { name: 'El Salvador', value: 6218.195 },\r\n    { name: 'Somaliland', value: 9636.173 },\r\n    { name: 'Somalia', value: 9636.173 },\r\n    { name: 'Republic of Serbia', value: 3573.024 },\r\n    { name: 'Suriname', value: 524.96 },\r\n    { name: 'Slovakia', value: 5433.437 },\r\n    { name: 'Slovenia', value: 2054.232 },\r\n    { name: 'Sweden', value: 9382.297 },\r\n    { name: 'Swaziland', value: 1193.148 },\r\n    { name: 'Syria', value: 7830.534 },\r\n    { name: 'Chad', value: 11720.781 },\r\n    { name: 'Togo', value: 6306.014 },\r\n    { name: 'Thailand', value: 66402.316 },\r\n    { name: 'Tajikistan', value: 7627.326 },\r\n    { name: 'Turkmenistan', value: 5041.995 },\r\n    { name: 'East Timor', value: 10016.797 },\r\n    { name: 'Trinidad and Tobago', value: 1328.095 },\r\n    { name: 'Tunisia', value: 10631.83 },\r\n    { name: 'Turkey', value: 72137.546 },\r\n    { name: 'Tanzania', value: 44973.33 },\r\n    { name: 'Uganda', value: 33987.213 },\r\n    { name: 'Ukraine', value: 46050.22 },\r\n    { name: 'Uruguay', value: 3371.982 },\r\n    { name: 'United States', value: 312247.116 },\r\n    { name: 'Uzbekistan', value: 27769.27 },\r\n    { name: 'Venezuela', value: 236.299 },\r\n    { name: 'Vietnam', value: 89047.397 },\r\n    { name: 'Vanuatu', value: 236.299 },\r\n    { name: 'West Bank', value: 13.565 },\r\n    { name: 'Yemen', value: 22763.008 },\r\n    { name: 'South Africa', value: 51452.352 },\r\n    { name: 'Zambia', value: 13216.985 },\r\n    { name: 'Zimbabwe', value: 13076.978 }\r\n  ];\r\n\r\n  const total = 6961500;\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: params =>\r\n          `<strong>${params.data?.name} :</strong> ${(\r\n            (params.data?.value / total) *\r\n            100\r\n          ).toFixed(2)}%`\r\n      },\r\n      toolbox: {\r\n        show: false,\r\n        feature: {\r\n          restore: {}\r\n        }\r\n      },\r\n      visualMap: {\r\n        show: false,\r\n        min: 800,\r\n        max: 50000,\r\n        inRange: {\r\n          color: [\r\n            getColor('primary'),\r\n            rgbaColor(getColor('primary'), 0.8),\r\n            rgbaColor(getColor('primary'), 0.6),\r\n            rgbaColor(getColor('primary'), 0.4),\r\n            rgbaColor(getColor('primary'), 0.2)\r\n          ].reverse()\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'map',\r\n          map: 'world',\r\n          data,\r\n          roam: true,\r\n          scaleLimit: {\r\n            min: 1,\r\n            max: 5\r\n          },\r\n          left: 0,\r\n          right: 0,\r\n          label: {\r\n            show: false\r\n          },\r\n          itemStyle: {\r\n            borderColor: getColor('border-color')\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: false\r\n            },\r\n            itemStyle: {\r\n              areaColor: getColor('warning')\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    document\r\n      .querySelector('.session-by-country-map-reset')\r\n      ?.addEventListener('click', () => {\r\n        chart.dispatchAction({\r\n          type: 'restore'\r\n        });\r\n      });\r\n  }\r\n};\r\n\r\nexport default sessionByCountryMapInit;\r\n", "import { echartSetOption, handleTooltipPosition } from '../echarts-utils';\r\n\r\nconst mapUSAInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-map-usa-example');\r\n\r\n  const data = [\r\n    { name: 'Alabama', value: 4822023 },\r\n    { name: 'Alaska', value: 731449 },\r\n    { name: 'Arizona', value: 6553255 },\r\n    { name: 'Arkansas', value: 2949131 },\r\n    { name: 'California', value: 38041430 },\r\n    { name: 'Colorado', value: 5187582 },\r\n    { name: 'Connecticut', value: 3590347 },\r\n    { name: 'Delaware', value: 917092 },\r\n    { name: 'District of Columbia', value: 632323 },\r\n    { name: 'Florida', value: 19317568 },\r\n    { name: 'Georgia', value: 9919945 },\r\n    { name: 'Hawaii', value: 1392313 },\r\n    { name: 'Idaho', value: 1595728 },\r\n    { name: 'Illinois', value: 12875255 },\r\n    { name: 'Indiana', value: 6537334 },\r\n    { name: 'Iowa', value: 3074186 },\r\n    { name: 'Kansas', value: 2885905 },\r\n    { name: 'Kentucky', value: 4380415 },\r\n    { name: 'Louisiana', value: 4601893 },\r\n    { name: 'Maine', value: 1329192 },\r\n    { name: 'Maryland', value: 5884563 },\r\n    { name: 'Massachusetts', value: 6646144 },\r\n    { name: 'Michigan', value: 9883360 },\r\n    { name: 'Minnesota', value: 5379139 },\r\n    { name: 'Mississippi', value: 2984926 },\r\n    { name: 'Missouri', value: 6021988 },\r\n    { name: 'Montana', value: 1005141 },\r\n    { name: 'Nebraska', value: 1855525 },\r\n    { name: 'Nevada', value: 2758931 },\r\n    { name: 'New Hampshire', value: 1320718 },\r\n    { name: 'New Jersey', value: 8864590 },\r\n    { name: 'New Mexico', value: 2085538 },\r\n    { name: 'New York', value: 19570261 },\r\n    { name: 'North Carolina', value: 9752073 },\r\n    { name: 'North Dakota', value: 699628 },\r\n    { name: 'Ohio', value: 11544225 },\r\n    { name: 'Oklahoma', value: 3814820 },\r\n    { name: 'Oregon', value: 3899353 },\r\n    { name: 'Pennsylvania', value: 12763536 },\r\n    { name: 'Rhode Island', value: 1050292 },\r\n    { name: 'South Carolina', value: 4723723 },\r\n    { name: 'South Dakota', value: 833354 },\r\n    { name: 'Tennessee', value: 6456243 },\r\n    { name: 'Texas', value: 26059203 },\r\n    { name: 'Utah', value: 2855287 },\r\n    { name: 'Vermont', value: 626011 },\r\n    { name: 'Virginia', value: 8185867 },\r\n    { name: 'Washington', value: 6897012 },\r\n    { name: 'West Virginia', value: 1855413 },\r\n    { name: 'Wisconsin', value: 5726398 },\r\n    { name: 'Wyoming', value: 576412 },\r\n    { name: 'Puerto Rico', value: 3667084 }\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position: (...params) => handleTooltipPosition(params),\r\n        formatter: params => {\r\n          return `<strong>${params.data.name} :</strong> ${params.data.value}`;\r\n        }\r\n      },\r\n      toolbox: {\r\n        show: false,\r\n        feature: {\r\n          restore: {}\r\n        }\r\n      },\r\n      visualMap: {\r\n        left: 'right',\r\n        min: 500000,\r\n        max: 38000000,\r\n        inRange: {\r\n          color: [getColor('primary'), getColor('info')]\r\n        },\r\n        text: ['High', 'Low'],\r\n        calculable: true,\r\n        textStyle: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        formatter: value => `${value / 1000}k`\r\n      },\r\n      series: [\r\n        {\r\n          left: 10,\r\n          name: 'USA PopEstimates',\r\n          type: 'map',\r\n          zoom: 1.2,\r\n          roam: true,\r\n          scaleLimit: {\r\n            min: 1,\r\n            max: 5\r\n          },\r\n          itemStyle: {\r\n            borderColor: getColor('border-color')\r\n          },\r\n          label: {\r\n            color: '#fff'\r\n          },\r\n          map: 'USA',\r\n          emphasis: {\r\n            label: {\r\n              show: true,\r\n              color: '#fff'\r\n            },\r\n\r\n            itemStyle: {\r\n              areaColor: getColor('warning')\r\n            }\r\n          },\r\n          data\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n    document.querySelector('.usa-map-reset').addEventListener('click', () => {\r\n      chart.dispatchAction({\r\n        type: 'restore'\r\n      });\r\n    });\r\n  }\r\n};\r\n\r\nexport default mapUSAInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst basicScatterChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-basic-scatter-chart-example'\r\n  );\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0\r\n      },\r\n      xAxis: {\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          // symbolSize: val => val[2] * 2,\r\n          data: [\r\n            [10.0, 8.04],\r\n            [8.07, 6.95],\r\n            [13.0, 7.58],\r\n            [9.05, 8.81],\r\n            [11.0, 8.33],\r\n            [14.0, 7.66],\r\n            [13.4, 6.81],\r\n            [10.0, 6.33],\r\n            [14.0, 8.96],\r\n            [12.5, 6.82],\r\n            [9.15, 7.2],\r\n            [11.5, 7.2],\r\n            [3.03, 4.23],\r\n            [12.2, 7.83],\r\n            [2.02, 4.47],\r\n            [1.05, 3.33],\r\n            [4.05, 4.96],\r\n            [6.03, 7.24],\r\n            [12.0, 6.26],\r\n            [12.0, 8.84],\r\n            [7.08, 5.82],\r\n            [5.02, 5.68]\r\n          ],\r\n          type: 'scatter',\r\n          itemStyle: {\r\n            color: getColor('danger')\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 8,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: 8,\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default basicScatterChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst bubbleChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-bubble-chart-example');\r\n\r\n  const data = [\r\n    [\r\n      [28604, 77, 17096869, 'Australia', 1990],\r\n      [31163, 77.4, 27662440, 'Canada', 1990],\r\n      [1516, 68, 1154605773, 'China', 1990],\r\n      [28599, 75, 4986705, 'Finland', 1990],\r\n      [29476, 77.1, 56943299, 'France', 1990],\r\n      [31476, 75.4, 78958237, 'Germany', 1990],\r\n      [1777, 57.7, *********, 'India', 1990],\r\n      [29550, 79.1, *********, 'Japan', 1990],\r\n      [12087, 72, 42972254, 'South Korea', 1990],\r\n      [24021, 75.4, 3397534, 'New Zealand', 1990],\r\n      [43296, 76.8, 4240375, 'Norway', 1990],\r\n      [10088, 70.8, 38195258, 'Poland', 1990],\r\n      [19349, 69.6, *********, 'Russia', 1990],\r\n      [26424, 75.7, 57110117, 'United Kingdom', 1990],\r\n      [37062, 75.4, *********, 'United States', 1990]\r\n    ],\r\n    [\r\n      [44056, 81.8, 23968973, 'Australia', 2015],\r\n      [43294, 81.7, 35939927, 'Canada', 2015],\r\n      [13334, 76.9, 1376048943, 'China', 2015],\r\n      [38923, 80.8, 5503457, 'Finland', 2015],\r\n      [37599, 81.9, 64395345, 'France', 2015],\r\n      [44053, 81.1, 80688545, 'Germany', 2015],\r\n      [5903, 66.8, 1311050527, 'India', 2015],\r\n      [36162, 83.5, *********, 'Japan', 2015],\r\n      [34644, 80.7, 50293439, 'South Korea', 2015],\r\n      [34186, 80.6, 4528526, 'New Zealand', 2015],\r\n      [64304, 81.6, 5210967, 'Norway', 2015],\r\n      [24787, 77.3, 38611794, 'Poland', 2015],\r\n      [23038, 73.13, *********, 'Russia', 2015],\r\n      [38225, 81.4, 64715810, 'United Kingdom', 2015],\r\n      [53354, 79.1, *********, 'United States', 2015]\r\n    ]\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      title: {\r\n        text: '1990 and 2015 have per capita and GDP',\r\n        left: 0,\r\n        top: 0,\r\n        textStyle: {\r\n          color: getColor('tertiary-color'),\r\n          fontWeight: 600\r\n        }\r\n      },\r\n      legend: {\r\n        right: 0,\r\n        top: '10%',\r\n        data: ['1990', '2015'],\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      xAxis: {\r\n        axisLabel: {\r\n          color: getColor('quaternary-color'),\r\n          formatter: value => `${value / 1000}k`\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        scale: true,\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: '1990',\r\n          data: data[0],\r\n          type: 'scatter',\r\n          symbolSize: value => {\r\n            return Math.sqrt(value[2]) / 5e2;\r\n          },\r\n          emphasis: {\r\n            focus: 'series',\r\n            label: {\r\n              color: getColor('tertiary-color'),\r\n              show: true,\r\n              formatter: param => {\r\n                return param.data[3];\r\n              },\r\n              position: 'top'\r\n            }\r\n          },\r\n          itemStyle: {\r\n            color: rgbaColor(getColor('primary'), 0.7)\r\n          }\r\n        },\r\n        {\r\n          name: '2015',\r\n          data: data[1],\r\n          type: 'scatter',\r\n          symbolSize: value => {\r\n            return Math.sqrt(value[2]) / 7e2;\r\n          },\r\n          emphasis: {\r\n            focus: 'series',\r\n            label: {\r\n              color: getColor('quaternary-color'),\r\n              show: true,\r\n              formatter: param => {\r\n                return param.data[3];\r\n              },\r\n              position: 'top'\r\n            }\r\n          },\r\n          itemStyle: {\r\n            color: rgbaColor(getColor('warning'), 0.7)\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        left: 5,\r\n        right: 10,\r\n        bottom: 5,\r\n        top: '20%',\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default bubbleChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst quartetScatterChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-quartet-scatter-chart-example'\r\n  );\r\n\r\n  const dataAll = [\r\n    [\r\n      [10.0, 8.04],\r\n      [8.0, 6.95],\r\n      [13.0, 7.58],\r\n      [9.0, 8.81],\r\n      [11.0, 8.33],\r\n      [14.0, 9.96],\r\n      [6.0, 7.24],\r\n      [4.0, 4.26],\r\n      [12.0, 10.84],\r\n      [7.0, 4.82],\r\n      [5.0, 5.68]\r\n    ],\r\n    [\r\n      [10.0, 9.14],\r\n      [8.0, 8.14],\r\n      [13.0, 8.74],\r\n      [9.0, 8.77],\r\n      [11.0, 9.26],\r\n      [14.0, 8.1],\r\n      [6.0, 6.13],\r\n      [4.0, 3.1],\r\n      [12.0, 9.13],\r\n      [7.0, 7.26],\r\n      [5.0, 4.74]\r\n    ],\r\n    [\r\n      [10.0, 7.46],\r\n      [8.0, 6.77],\r\n      [13.0, 12.74],\r\n      [9.0, 7.11],\r\n      [11.0, 7.81],\r\n      [14.0, 8.84],\r\n      [6.0, 6.08],\r\n      [4.0, 5.39],\r\n      [12.0, 8.15],\r\n      [7.0, 6.42],\r\n      [5.0, 5.73]\r\n    ],\r\n    [\r\n      [8.0, 6.58],\r\n      [8.0, 5.76],\r\n      [8.0, 7.71],\r\n      [8.0, 8.84],\r\n      [8.0, 8.47],\r\n      [8.0, 7.04],\r\n      [8.0, 5.25],\r\n      [19.0, 12.5],\r\n      [8.0, 5.56],\r\n      [8.0, 7.91],\r\n      [8.0, 6.89]\r\n    ]\r\n  ];\r\n\r\n  const xAxis = () => ({\r\n    axisLabel: {\r\n      color: getColor('quaternary-color')\r\n    },\r\n    axisLine: {\r\n      show: true,\r\n      lineStyle: {\r\n        color: getColor('tertiary-bg')\r\n      }\r\n    },\r\n\r\n    splitLine: {\r\n      show: true,\r\n      lineStyle: {\r\n        color: getColor('secondary-bg')\r\n      }\r\n    }\r\n  });\r\n\r\n  const yAxis = () => ({\r\n    axisLabel: {\r\n      color: getColor('quaternary-color')\r\n    },\r\n    splitLine: {\r\n      show: true,\r\n      lineStyle: {\r\n        color: getColor('secondary-bg')\r\n      }\r\n    },\r\n\r\n    axisLine: {\r\n      show: true,\r\n      lineStyle: {\r\n        color: getColor('tertiary-bg')\r\n      }\r\n    }\r\n  });\r\n\r\n  const markLineOpt = {\r\n    animation: false,\r\n    label: {\r\n      formatter: 'y = 0.5 * x + 3',\r\n      align: 'right',\r\n      color: getColor('tertiary-color'),\r\n      fontWeight: 600\r\n    },\r\n    lineStyle: {\r\n      type: 'solid'\r\n    },\r\n    tooltip: {\r\n      formatter: 'y = 0.5 * x + 3'\r\n    },\r\n    data: [\r\n      [\r\n        {\r\n          coord: [0, 3],\r\n          symbol: 'none'\r\n        },\r\n        {\r\n          coord: [20, 13],\r\n          symbol: 'none'\r\n        }\r\n      ]\r\n    ]\r\n  };\r\n\r\n  const gridMdUp = [\r\n    { left: '7%', top: '10%', width: '38%', height: '38%' },\r\n    { right: '7%', top: '10%', width: '38%', height: '38%' },\r\n    { left: '7%', bottom: '7%', width: '38%', height: '38%' },\r\n    { right: '7%', bottom: '7%', width: '38%', height: '38%' }\r\n  ];\r\n\r\n  const gridMdDown = [\r\n    { left: 6, right: 7, top: '4%', height: '20%' },\r\n    { left: 6, right: 7, top: '29%', height: '20%' },\r\n    { left: 6, right: 7, bottom: '26%', height: '20%' },\r\n    { left: 6, right: 7, bottom: 25, height: '20%' }\r\n  ];\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        getColor('primary'),\r\n        getColor('success'),\r\n        getColor('warning'),\r\n        getColor('danger')\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: 'Group {a}: ({c})'\r\n      },\r\n      title: {\r\n        text: \"Anscombe's quartet\",\r\n        left: 'center',\r\n        top: 0,\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      grid: window.innerWidth < 768 ? gridMdDown : gridMdUp,\r\n      xAxis: [\r\n        { gridIndex: 0, min: 0, max: 20, ...xAxis() },\r\n        { gridIndex: 1, min: 0, max: 20, ...xAxis() },\r\n        { gridIndex: 2, min: 0, max: 20, ...xAxis() },\r\n        { gridIndex: 3, min: 0, max: 20, ...xAxis() }\r\n      ],\r\n      yAxis: [\r\n        { gridIndex: 0, min: 0, max: 15, ...yAxis() },\r\n        { gridIndex: 1, min: 0, max: 15, ...yAxis() },\r\n        { gridIndex: 2, min: 0, max: 15, ...yAxis() },\r\n        { gridIndex: 3, min: 0, max: 15, ...yAxis() }\r\n      ],\r\n      series: [\r\n        {\r\n          name: 'I',\r\n          type: 'scatter',\r\n          xAxisIndex: 0,\r\n          yAxisIndex: 0,\r\n          data: dataAll[0],\r\n          markLine: markLineOpt\r\n        },\r\n        {\r\n          name: 'II',\r\n          type: 'scatter',\r\n          xAxisIndex: 1,\r\n          yAxisIndex: 1,\r\n          data: dataAll[1],\r\n          markLine: markLineOpt\r\n        },\r\n        {\r\n          name: 'III',\r\n          type: 'scatter',\r\n          xAxisIndex: 2,\r\n          yAxisIndex: 2,\r\n          data: dataAll[2],\r\n          markLine: markLineOpt\r\n        },\r\n        {\r\n          name: 'IV',\r\n          type: 'scatter',\r\n          xAxisIndex: 3,\r\n          yAxisIndex: 3,\r\n          data: dataAll[3],\r\n          markLine: markLineOpt\r\n        }\r\n      ]\r\n    });\r\n    const responsiveOptions = {\r\n      xs: {\r\n        grid: gridMdDown\r\n      },\r\n      md: {\r\n        grid: gridMdUp\r\n      }\r\n    };\r\n    echartSetOption(chart, userOptions, getDefaultOptions, responsiveOptions);\r\n  }\r\n};\r\n\r\nexport default quartetScatterChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst singleAxisScatterChartInit = () => {\r\n  const { getColor, getData, getRandomNumber } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-single-axis-scatter-chart-example'\r\n  );\r\n\r\n  const hours = [\r\n    '12am',\r\n    '1am',\r\n    '2am',\r\n    '3am',\r\n    '4am',\r\n    '5am',\r\n    '6am',\r\n    '7am',\r\n    '8am',\r\n    '9am',\r\n    '10am',\r\n    '11am',\r\n    '12pm',\r\n    '1pm',\r\n    '2pm',\r\n    '3pm',\r\n    '4pm',\r\n    '5pm',\r\n    '6pm',\r\n    '7pm',\r\n    '8pm',\r\n    '9pm',\r\n    '10pm',\r\n    '11pm'\r\n  ];\r\n\r\n  const days = [\r\n    'Saturday',\r\n    'Friday',\r\n    'Thursday',\r\n    'Wednesday',\r\n    'Tuesday',\r\n    'Monday',\r\n    'Sunday'\r\n  ];\r\n\r\n  const data = [];\r\n  for (let i = 0; i < 7; i += 1) {\r\n    for (let j = 0; j < 24; j += 1) {\r\n      data.push([j, i, getRandomNumber(0, 10)]);\r\n    }\r\n  }\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position: 'top',\r\n        formatter: params => {\r\n          return `\r\n            ${days[params.value[1]]} <br/>\r\n            ${hours[params.value[0]]} : ${params.value[2]}\r\n          `;\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: hours,\r\n        boundaryGap: false,\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        axisLine: {\r\n          show: false\r\n        },\r\n        axisTick: {\r\n          lineStyle: {\r\n            color: getColor('quaternary-color')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        axisLine: {\r\n          show: false\r\n        },\r\n        axisTick: {\r\n          lineStyle: {\r\n            color: getColor('quaternary-color')\r\n          }\r\n        },\r\n        axisLabel: {\r\n          margin: 15\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Punch Card',\r\n          type: 'scatter',\r\n          symbolSize: val => val[2] * 2,\r\n          data,\r\n          animationDelay: idx => {\r\n            return idx * 5;\r\n          },\r\n          itemStyle: {\r\n            color: getColor('primary')\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 12,\r\n        left: 5,\r\n        bottom: 5,\r\n        top: 5,\r\n        containLabel: true\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default singleAxisScatterChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst pieChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-pie-chart-example');\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        left: 'left',\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 530 ? '45%' : '60%',\r\n          label: {\r\n            color: getColor('tertiary-color')\r\n          },\r\n          center: ['50%', '55%'],\r\n          data: [\r\n            {\r\n              value: 1200,\r\n              name: 'Facebook',\r\n              itemStyle: {\r\n                color: getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: 1000,\r\n              name: 'Youtube',\r\n              itemStyle: {\r\n                color: getColor('danger')\r\n              }\r\n            },\r\n            {\r\n              value: 800,\r\n              name: 'Twitter',\r\n              itemStyle: {\r\n                color: getColor('info')\r\n              }\r\n            },\r\n            {\r\n              value: 600,\r\n              name: 'Linkedin',\r\n              itemStyle: {\r\n                color: getColor('success')\r\n              }\r\n            },\r\n            {\r\n              value: 400,\r\n              name: 'Github',\r\n              itemStyle: {\r\n                color: getColor('warning')\r\n              }\r\n            }\r\n          ],\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowOffsetX: 0,\r\n              shadowColor: rgbaColor(getColor('tertiary-color'), 0.5)\r\n            }\r\n          }\r\n        }\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      }\r\n    });\r\n\r\n    const responsiveOptions = {\r\n      xs: {\r\n        series: [\r\n          {\r\n            radius: '45%'\r\n          }\r\n        ]\r\n      },\r\n      sm: {\r\n        series: [\r\n          {\r\n            radius: '60%'\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions, responsiveOptions);\r\n  }\r\n};\r\n\r\nexport default pieChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst doughnutChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-doughnut-chart-example');\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        left: 'left',\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          center: ['50%', '55%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: false,\r\n            position: 'center'\r\n          },\r\n          labelLine: {\r\n            show: false\r\n          },\r\n          data: [\r\n            {\r\n              value: 1200,\r\n              name: 'Facebook',\r\n              itemStyle: {\r\n                color: getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: 1000,\r\n              name: 'Youtube',\r\n              itemStyle: {\r\n                color: getColor('danger')\r\n              }\r\n            },\r\n            {\r\n              value: 800,\r\n              name: 'Twitter',\r\n              itemStyle: {\r\n                color: getColor('info')\r\n              }\r\n            },\r\n            {\r\n              value: 600,\r\n              name: 'Linkedin',\r\n              itemStyle: {\r\n                color: getColor('success')\r\n              }\r\n            },\r\n            {\r\n              value: 400,\r\n              name: 'Github',\r\n              itemStyle: {\r\n                color: getColor('warning')\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default doughnutChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst doughnutRoundedChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-doughnut-rounded-chart-example'\r\n  );\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        orient: 'vertical',\r\n        left: 'left',\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: ['40%', '70%'],\r\n          center: window.innerWidth < 530 ? ['65%', '55%'] : ['50%', '55%'],\r\n          avoidLabelOverlap: false,\r\n          itemStyle: {\r\n            borderRadius: 10,\r\n            borderColor: getColor('body-highlight-bg'),\r\n            borderWidth: 2\r\n          },\r\n          label: {\r\n            show: false,\r\n            position: 'center'\r\n          },\r\n          labelLine: {\r\n            show: false\r\n          },\r\n          data: [\r\n            {\r\n              value: 1200,\r\n              name: 'Starter',\r\n              itemStyle: {\r\n                color: getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: 1000,\r\n              name: 'Basic',\r\n              itemStyle: {\r\n                color: getColor('danger')\r\n              }\r\n            },\r\n            {\r\n              value: 800,\r\n              name: 'Optimal',\r\n              itemStyle: {\r\n                color: getColor('info')\r\n              }\r\n            },\r\n            {\r\n              value: 600,\r\n              name: 'Business',\r\n              itemStyle: {\r\n                color: getColor('success')\r\n              }\r\n            },\r\n            {\r\n              value: 400,\r\n              name: 'Premium',\r\n              itemStyle: {\r\n                color: getColor('warning')\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      }\r\n    });\r\n\r\n    const responsiveOptions = {\r\n      xs: {\r\n        series: [\r\n          {\r\n            center: ['65%', '55%']\r\n          }\r\n        ]\r\n      },\r\n      sm: {\r\n        series: [\r\n          {\r\n            center: ['50%', '55%']\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions, responsiveOptions);\r\n  }\r\n};\r\n\r\nexport default doughnutRoundedChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst pieMultipleChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-pie-multiple-chart-example');\r\n  const data1 = [\r\n    {\r\n      value: 1048,\r\n      name: 'Starter',\r\n      itemStyle: {\r\n        color: getColor('danger')\r\n      }\r\n    },\r\n    {\r\n      value: 735,\r\n      name: 'Basic',\r\n      itemStyle: {\r\n        color: getColor('primary')\r\n      }\r\n    },\r\n    {\r\n      value: 580,\r\n      name: 'Optimal',\r\n      itemStyle: {\r\n        color: getColor('secondary')\r\n      }\r\n    },\r\n    {\r\n      value: 484,\r\n      name: 'Business',\r\n      itemStyle: {\r\n        color: getColor('warning')\r\n      }\r\n    },\r\n    {\r\n      value: 300,\r\n      name: 'Premium',\r\n      itemStyle: {\r\n        color: getColor('success')\r\n      }\r\n    },\r\n    {\r\n      value: 300,\r\n      name: 'Platinum',\r\n      itemStyle: {\r\n        color: getColor('info')\r\n      }\r\n    }\r\n  ];\r\n\r\n  const data2 = [\r\n    {\r\n      value: 1048,\r\n      name: 'Facebook',\r\n      itemStyle: {\r\n        color: getColor('primary')\r\n      }\r\n    },\r\n    {\r\n      value: 735,\r\n      name: 'Youtube',\r\n      itemStyle: {\r\n        color: getColor('danger')\r\n      }\r\n    },\r\n    {\r\n      value: 580,\r\n      name: 'Twitter',\r\n      itemStyle: {\r\n        color: getColor('info')\r\n      }\r\n    },\r\n    {\r\n      value: 484,\r\n      name: 'Linkedin',\r\n      itemStyle: {\r\n        color: getColor('success')\r\n      }\r\n    },\r\n    {\r\n      value: 300,\r\n      name: 'Github',\r\n      itemStyle: {\r\n        color: getColor('warning')\r\n      }\r\n    }\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      title: [\r\n        {\r\n          text: 'Pie Multiple Chart',\r\n          left: 'center',\r\n          textStyle: {\r\n            color: getColor('tertiary-color')\r\n          }\r\n        }\r\n      ],\r\n\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 450 ? '48%' : '55%',\r\n          center: ['25%', '50%'],\r\n          data: data1,\r\n          label: {\r\n            show: false\r\n          }\r\n        },\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 450 ? '48%' : '55%',\r\n          center: ['75%', '50%'],\r\n          avoidLabelOverlap: false,\r\n          label: {\r\n            show: false\r\n          },\r\n          data: data2\r\n        }\r\n      ]\r\n    });\r\n\r\n    const responsiveOptions = {\r\n      xs: {\r\n        series: [{ radius: '48%' }, { radius: '48%' }]\r\n      },\r\n      sm: {\r\n        series: [{ radius: '55%' }, { radius: '55%' }]\r\n      }\r\n    };\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions, responsiveOptions);\r\n  }\r\n};\r\n\r\nexport default pieMultipleChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst pieLabelAlignChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-pie-label-align-chart-example'\r\n  );\r\n\r\n  const data = [\r\n    {\r\n      value: 850,\r\n      name: 'Starter',\r\n      itemStyle: {\r\n        color: rgbaColor(getColor('primary'), 0.5)\r\n      }\r\n    },\r\n    {\r\n      value: 750,\r\n      name: 'Starter Pro',\r\n      itemStyle: {\r\n        color: getColor('danger')\r\n      }\r\n    },\r\n    {\r\n      value: 457,\r\n      name: 'Basic',\r\n      itemStyle: {\r\n        color: getColor('primary')\r\n      }\r\n    },\r\n    {\r\n      value: 654,\r\n      name: 'Optimal',\r\n      itemStyle: {\r\n        color: getColor('secondary')\r\n      }\r\n    },\r\n    {\r\n      value: 447,\r\n      name: 'Business',\r\n      itemStyle: {\r\n        color: getColor('warning')\r\n      }\r\n    },\r\n    {\r\n      value: 682,\r\n      name: 'Classic addition',\r\n      itemStyle: {\r\n        color: rgbaColor(getColor('warning'), 0.8)\r\n      }\r\n    },\r\n    {\r\n      value: 471,\r\n      name: 'Premium',\r\n      itemStyle: {\r\n        color: getColor('success')\r\n      }\r\n    },\r\n    {\r\n      value: 524,\r\n      name: 'Platinum',\r\n      itemStyle: {\r\n        color: getColor('info')\r\n      }\r\n    },\r\n    {\r\n      value: 200,\r\n      name: 'Platinum Pro',\r\n      itemStyle: {\r\n        color: rgbaColor(getColor('primary'), 0.5)\r\n      }\r\n    }\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      title: [\r\n        {\r\n          text: 'Pie Label Align Chart',\r\n          left: 'center',\r\n          textStyle: {\r\n            color: getColor('tertiary-color')\r\n          }\r\n        },\r\n        {\r\n          subtext: 'alignTo: \"labelLine\"',\r\n          left: '50%',\r\n          top: '85%',\r\n          textAlign: 'center',\r\n          subtextStyle: {\r\n            color: getColor('tertiary-color')\r\n          }\r\n        }\r\n      ],\r\n\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        position(pos, ...size) {\r\n          if (window.innerWidth <= 540) {\r\n            const tooltipHeight = size[1].offsetHeight;\r\n            const obj = { top: pos[1] - tooltipHeight - 20 };\r\n            obj[pos[0] < size[3].viewSize[0] / 2 ? 'left' : 'right'] = 5;\r\n            return obj;\r\n          }\r\n          return null;\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 530 ? '45%' : '60%',\r\n          center: ['50%', '50%'],\r\n          data,\r\n          label: {\r\n            position: 'outer',\r\n            alignTo: 'labelLine',\r\n            bleedMargin: 5,\r\n            color: getColor('tertiary-color')\r\n          },\r\n          left: '5%',\r\n          right: '5%',\r\n          top: 0,\r\n          bottom: 0\r\n        }\r\n      ]\r\n    });\r\n\r\n    const responsiveOptions = {\r\n      xs: {\r\n        series: [\r\n          {\r\n            radius: '45%'\r\n          }\r\n        ]\r\n      },\r\n      sm: {\r\n        series: [\r\n          {\r\n            radius: '60%'\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions, responsiveOptions);\r\n  }\r\n};\r\n\r\nexport default pieLabelAlignChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst pieEdgeAlignChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-pie-edge-align-chart-example'\r\n  );\r\n\r\n  const data = [\r\n    {\r\n      value: 850,\r\n      name: 'Starter',\r\n      itemStyle: {\r\n        color: rgbaColor(getColor('primary'), 0.5)\r\n      }\r\n    },\r\n    {\r\n      value: 750,\r\n      name: 'Starter Pro',\r\n      itemStyle: {\r\n        color: getColor('danger')\r\n      }\r\n    },\r\n    {\r\n      value: 457,\r\n      name: 'Basic',\r\n      itemStyle: {\r\n        color: getColor('primary')\r\n      }\r\n    },\r\n    {\r\n      value: 654,\r\n      name: 'Optimal',\r\n      itemStyle: {\r\n        color: getColor('secondary')\r\n      }\r\n    },\r\n    {\r\n      value: 447,\r\n      name: 'Business',\r\n      itemStyle: {\r\n        color: getColor('warning')\r\n      }\r\n    },\r\n    {\r\n      value: 682,\r\n      name: 'Classic addition',\r\n      itemStyle: {\r\n        color: rgbaColor(getColor('warning'), 0.8)\r\n      }\r\n    },\r\n    {\r\n      value: 471,\r\n      name: 'Premium',\r\n      itemStyle: {\r\n        color: getColor('success')\r\n      }\r\n    },\r\n    {\r\n      value: 524,\r\n      name: 'Platinum',\r\n      itemStyle: {\r\n        color: getColor('info')\r\n      }\r\n    },\r\n    {\r\n      value: 200,\r\n      name: 'Platinum Pro',\r\n      itemStyle: {\r\n        color: rgbaColor(getColor('primary'), 0.5)\r\n      }\r\n    }\r\n  ];\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      title: [\r\n        {\r\n          text: 'Pie Edge Align Chart',\r\n          left: 'center',\r\n          textStyle: {\r\n            color: getColor('tertiary-color')\r\n          }\r\n        },\r\n        {\r\n          subtext: 'alignTo: \"edge\"',\r\n          left: '50%',\r\n          top: '85%',\r\n          textAlign: 'center',\r\n          subtextStyle: {\r\n            color: getColor('tertiary-color')\r\n          }\r\n        }\r\n      ],\r\n\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        position(pos, ...size) {\r\n          if (window.innerWidth <= 540) {\r\n            const tooltipHeight = size[1].offsetHeight;\r\n            const obj = { top: pos[1] - tooltipHeight - 20 };\r\n            obj[pos[0] < size[3].viewSize[0] / 2 ? 'left' : 'right'] = 5;\r\n            return obj;\r\n          }\r\n          return null;\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'pie',\r\n          radius: window.innerWidth < 530 ? '45%' : '60%',\r\n          center: ['50%', '50%'],\r\n          data,\r\n          label: {\r\n            position: 'outer',\r\n            alignTo: 'edge',\r\n            margin: 20,\r\n            color: getColor('tertiary-color')\r\n          },\r\n          left: '5%',\r\n          right: '5%',\r\n          top: 0,\r\n          bottom: 0\r\n        }\r\n      ]\r\n    });\r\n\r\n    const responsiveOptions = {\r\n      xs: {\r\n        series: [\r\n          {\r\n            radius: '45%'\r\n          }\r\n        ]\r\n      },\r\n      sm: {\r\n        series: [\r\n          {\r\n            radius: '60%'\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions, responsiveOptions);\r\n  }\r\n};\r\n\r\nexport default pieEdgeAlignChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst basicGaugeChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-basic-gauge-chart-example');\r\n\r\n  const tooltipFormatter = params => {\r\n    return `\r\n    <div>\r\n        <h6 class=\"fs-9 text-body-tertiary mb-0\">\r\n          <span class=\"fas fa-circle me-1\" style='color:${params[0].color}'></span>\r\n          ${params[0].name} : ${params[0].value}\r\n        </h6>\r\n    </div>\r\n    `;\r\n  };\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        formatter: params => tooltipFormatter(params),\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      radius: '100%',\r\n      series: [\r\n        {\r\n          name: 'Pressure',\r\n          type: 'gauge',\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: getColor('tertiary-color')\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: getColor('tertiary-color')\r\n          },\r\n          detail: {\r\n            formatter: '{value}'\r\n          },\r\n          title: {\r\n            color: getColor('tertiary-color')\r\n          },\r\n          data: [\r\n            {\r\n              value: 50,\r\n              name: 'SCORE',\r\n              detail: {\r\n                color: getColor('tertiary-color')\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default basicGaugeChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst gaugeProgressChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-gauge-progress-chart-example'\r\n  );\r\n\r\n  const tooltipFormatter = params => {\r\n    return `\r\n    <div>\r\n        <h6 class=\"fs-9 text-body-tertiary mb-0\">\r\n          <span class=\"fas fa-circle me-1\" style='color:${params[0].color}'></span>\r\n          ${params[0].name} : ${params[0].value}\r\n        </h6>\r\n    </div>\r\n    `;\r\n  };\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        formatter: params => tooltipFormatter(params),\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          center: ['50%', '60%'],\r\n          radius: '100%',\r\n          startAngle: 180,\r\n          endAngle: 0,\r\n          progress: {\r\n            show: true,\r\n            width: 18,\r\n            itemStyle: {\r\n              color: getColor('info')\r\n            }\r\n          },\r\n          itemStyle: {\r\n            color: getColor('info'),\r\n            shadowColor: rgbaColor(getColor('primary'), 0.5),\r\n            shadowBlur: 10,\r\n            shadowOffsetX: 2,\r\n            shadowOffsetY: 2\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 18,\r\n              color: [[1, getColor('secondary-bg')]]\r\n            }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              width: 2,\r\n              color: getColor('quaternary-color')\r\n            }\r\n          },\r\n          axisLabel: {\r\n            distance: 25,\r\n            color: getColor('quaternary-color')\r\n          },\r\n          anchor: {\r\n            show: true,\r\n            showAbove: true,\r\n            size: 25,\r\n            itemStyle: {\r\n              color: getColor('info')\r\n            }\r\n          },\r\n          title: {\r\n            show: false\r\n          },\r\n          detail: {\r\n            valueAnimation: true,\r\n            fontSize: 80,\r\n            offsetCenter: [0, '70%']\r\n          },\r\n          data: [\r\n            {\r\n              value: 70,\r\n              detail: {\r\n                fontSize: 30,\r\n                color: getColor('quaternary-color'),\r\n                offsetCenter: [0, '40%']\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default gaugeProgressChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst gaugeRingChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-gauge-ring-chart-example');\r\n\r\n  const tooltipFormatter = params => {\r\n    return `\r\n    <div>\r\n        <h6 class=\"fs-9 text-body-tertiary mb-0\">\r\n          <span class=\"fas fa-circle me-1\" style='color:${params[0].color}'></span>\r\n          ${params[0].name} : ${params[0].value}\r\n        </h6>\r\n    </div>\r\n    `;\r\n  };\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        formatter: params => tooltipFormatter(params),\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          radius: '100%',\r\n          startAngle: 90,\r\n          endAngle: -270,\r\n          pointer: {\r\n            show: false\r\n          },\r\n          progress: {\r\n            show: true,\r\n            overlap: false,\r\n            roundCap: true,\r\n            clip: false,\r\n            itemStyle: {\r\n              borderWidth: 1,\r\n              borderColor: getColor('quaternary-color')\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 18,\r\n              color: [[1, getColor('secondary-bg')]]\r\n            }\r\n          },\r\n          splitLine: {\r\n            show: false,\r\n            distance: 0,\r\n            length: 10\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            show: false,\r\n            distance: 50\r\n          },\r\n          data: [\r\n            {\r\n              value: 80,\r\n              title: {\r\n                offsetCenter: ['0%', '0%']\r\n              },\r\n              detail: {\r\n                offsetCenter: ['0%', '0%']\r\n              },\r\n              itemStyle: {\r\n                color: getColor('primary')\r\n              }\r\n            }\r\n          ],\r\n          title: {\r\n            fontSize: 14\r\n          },\r\n          detail: {\r\n            width: 50,\r\n            height: 14,\r\n            fontSize: 20,\r\n            color: 'auto',\r\n            formatter: '{value}%'\r\n          }\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default gaugeRingChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst gaugeMultiRingChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-gauge-multiring-chart-example'\r\n  );\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          startAngle: 90,\r\n          endAngle: -270,\r\n          radius: '85%',\r\n          pointer: {\r\n            show: false\r\n          },\r\n          center: ['50%', '50%'],\r\n          progress: {\r\n            show: true,\r\n            overlap: false,\r\n            roundCap: true,\r\n            clip: false,\r\n            itemStyle: {\r\n              color: getColor('info')\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 8,\r\n              color: [[1, getColor('secondary-bg')]]\r\n            }\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          data: [79],\r\n          detail: {\r\n            show: false\r\n          },\r\n          animationDuration: 2000\r\n        },\r\n        {\r\n          type: 'gauge',\r\n          startAngle: 90,\r\n          endAngle: -270,\r\n          radius: '70%',\r\n          pointer: {\r\n            show: false\r\n          },\r\n          center: ['50%', '50%'],\r\n          progress: {\r\n            show: true,\r\n            overlap: false,\r\n            roundCap: true,\r\n            clip: false,\r\n            itemStyle: {\r\n              color: getColor('primary')\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 8,\r\n              color: [[1, getColor('secondary-bg')]]\r\n            }\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          data: [85],\r\n          detail: {\r\n            show: false\r\n          },\r\n          animationDuration: 2000\r\n        },\r\n        {\r\n          type: 'gauge',\r\n          startAngle: 90,\r\n          endAngle: -270,\r\n          radius: '55%',\r\n          pointer: {\r\n            show: false\r\n          },\r\n          center: ['50%', '50%'],\r\n          progress: {\r\n            show: true,\r\n            overlap: false,\r\n            roundCap: true,\r\n            clip: false,\r\n            itemStyle: {\r\n              color: getColor('success')\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 8,\r\n              color: [[1, getColor('secondary-bg')]]\r\n            }\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          data: [70],\r\n          detail: {\r\n            show: false\r\n          },\r\n          animationDuration: 2000\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default gaugeMultiRingChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst gaugeMultiTitleChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-gauge-multi-title-chart-example'\r\n  );\r\n\r\n  const tooltipFormatter = params => {\r\n    return `\r\n    <div>\r\n        <h6 class=\"fs-9 text-body-tertiary mb-0\">\r\n          <span class=\"fas fa-circle me-1\" style='color:${params[0].color}'></span>\r\n          ${params[0].name} : ${params[0].value}\r\n        </h6>\r\n    </div>\r\n    `;\r\n  };\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        formatter: params => tooltipFormatter(params),\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          radius: '100%',\r\n          anchor: {\r\n            show: true,\r\n            showAbove: true,\r\n            size: 18,\r\n            itemStyle: {\r\n              color: getColor('warning')\r\n            }\r\n          },\r\n\r\n          progress: {\r\n            show: true,\r\n            overlap: true,\r\n            roundCap: true\r\n          },\r\n          axisLine: {\r\n            roundCap: true,\r\n            lineStyle: {\r\n              color: [[1, getColor('secondary-bg')]]\r\n            }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              width: 2,\r\n              color: getColor('quaternary-color')\r\n            }\r\n          },\r\n          axisLabel: {\r\n            distance: 25,\r\n            color: getColor('quaternary-color')\r\n          },\r\n          data: [\r\n            {\r\n              value: 20,\r\n              name: 'Perfect',\r\n              title: {\r\n                offsetCenter: ['-40%', '80%']\r\n              },\r\n              detail: {\r\n                offsetCenter: ['-40%', '95%']\r\n              },\r\n              itemStyle: {\r\n                color: getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: 40,\r\n              name: 'Good',\r\n              title: {\r\n                offsetCenter: ['0%', '80%']\r\n              },\r\n              detail: {\r\n                offsetCenter: ['0%', '95%']\r\n              },\r\n\r\n              itemStyle: {\r\n                color: getColor('success')\r\n              }\r\n            },\r\n            {\r\n              value: 60,\r\n              name: 'Commonly',\r\n              title: {\r\n                offsetCenter: ['40%', '80%']\r\n              },\r\n              detail: {\r\n                offsetCenter: ['40%', '95%']\r\n              },\r\n\r\n              itemStyle: {\r\n                color: getColor('warning')\r\n              }\r\n            }\r\n          ],\r\n          title: {\r\n            fontSize: 14,\r\n            color: getColor('tertiary-color')\r\n          },\r\n          detail: {\r\n            width: 40,\r\n            height: 14,\r\n            fontSize: 14,\r\n            color: '#fff',\r\n            backgroundColor: 'auto',\r\n            borderRadius: 3,\r\n            formatter: '{value}%'\r\n          }\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default gaugeMultiTitleChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst gaugeGradeChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-gauge-grade-chart-example');\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      series: [\r\n        {\r\n          radius: '100%',\r\n          type: 'gauge',\r\n          center: ['50%', '70%'],\r\n          startAngle: 180,\r\n          endAngle: 0,\r\n          min: 0,\r\n          max: 1,\r\n          splitNumber: 8,\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 6,\r\n              color: [\r\n                [0.25, getColor('danger')],\r\n                [0.5, getColor('warning')],\r\n                [0.75, getColor('info')],\r\n                [1, getColor('success')]\r\n              ]\r\n            }\r\n          },\r\n          pointer: {\r\n            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',\r\n            length: '12%',\r\n            width: 20,\r\n            offsetCenter: [0, '-60%'],\r\n            itemStyle: {\r\n              color: 'auto'\r\n            }\r\n          },\r\n          axisTick: {\r\n            length: 12,\r\n            lineStyle: {\r\n              color: 'auto',\r\n              width: 2\r\n            }\r\n          },\r\n          splitLine: {\r\n            length: 20,\r\n            lineStyle: {\r\n              color: 'auto',\r\n              width: 5\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: getColor('quaternary-color'),\r\n            distance: -60,\r\n            formatter: value => {\r\n              if (value === 0.875) {\r\n                return 'Excellent';\r\n              }\r\n              if (value === 0.625) {\r\n                return 'Good';\r\n              }\r\n              if (value === 0.375) {\r\n                return 'Well';\r\n              }\r\n              if (value === 0.125) {\r\n                return 'Bad';\r\n              }\r\n              return '';\r\n            }\r\n          },\r\n          title: {\r\n            offsetCenter: [0, '-20%'],\r\n            color: getColor('tertiary-color')\r\n          },\r\n          detail: {\r\n            offsetCenter: [0, '0%'],\r\n            valueAnimation: true,\r\n            formatter: value => {\r\n              return Math.round(value * 100);\r\n            },\r\n            color: 'auto'\r\n          },\r\n          data: [\r\n            {\r\n              value: 0.7,\r\n              name: 'Grade'\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default gaugeGradeChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst radarChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-radar-chart-example');\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        orient: 'vertical',\r\n        left: 'left',\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        position(pos, ...size) {\r\n          if (window.innerWidth <= 540) {\r\n            const tooltipHeight = size[1].offsetHeight;\r\n            const obj = { top: pos[1] - tooltipHeight - 20 };\r\n            obj[pos[0] < size[3].viewSize[0] / 2 ? 'left' : 'right'] = 5;\r\n            return obj;\r\n          }\r\n          return null;\r\n        }\r\n      },\r\n\r\n      radar: {\r\n        indicator: [\r\n          { name: 'Marketing', max: 6500 },\r\n          { name: 'Admin', max: 16000 },\r\n          { name: 'Tech', max: 30000 },\r\n          { name: 'Support', max: 38000 },\r\n          { name: 'Dev ', max: 52000 },\r\n          { name: 'Sales ', max: 25000 }\r\n        ],\r\n        radius: 120,\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: rgbaColor(getColor('tertiary-color'))\r\n          }\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'radar',\r\n          data: [\r\n            {\r\n              value: [4200, 3000, 20000, 35000, 50000, 18000],\r\n              name: 'Data A',\r\n              itemStyle: {\r\n                color: getColor('primary')\r\n              }\r\n            },\r\n            {\r\n              value: [5000, 14000, 28000, 26000, 42000, 21000],\r\n              name: 'Data B',\r\n              itemStyle: {\r\n                color: getColor('warning')\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default radarChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst radarCustomizedChartInit = () => {\r\n  const { getColor, getData, rgbaColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-radar-customized-chart-example'\r\n  );\r\n\r\n  const getFormatter = params => {\r\n    const indicators = [\r\n      ['Marketing', 'Sales', 'Dev', 'Support', 'Tech', 'Admin'],\r\n      ['Language', 'Math', 'English', 'Physics', 'Chemistry', 'Biology']\r\n    ];\r\n    const num = params.seriesIndex;\r\n    return `<strong > ${params.name} </strong>\r\n    <div class=\"fs-9 text-body-tertiary\">\r\n      <strong >${indicators[params.seriesIndex][0]}</strong>: ${\r\n      params.value[0]\r\n    }  <br>\r\n      <strong>${indicators[num][1]}</strong>: ${params.value[1]}  <br>\r\n      <strong>${indicators[num][2]}</strong>: ${params.value[2]}  <br>\r\n      <strong>${indicators[num][3]}</strong>: ${params.value[3]}  <br>\r\n      <strong>${indicators[num][4]}</strong>: ${params.value[4]}  <br>\r\n      <strong>${indicators[num][5]}</strong>: ${params.value[5]}  <br>\r\n    </div>`;\r\n  };\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        orient: 'vertical',\r\n        left: 'left',\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: getFormatter\r\n      },\r\n\r\n      radar: [\r\n        {\r\n          startAngle: 90,\r\n          splitNumber: 4,\r\n          shape: 'circle',\r\n          indicator: [\r\n            { name: 'Admin', max: 6500 },\r\n            { name: 'Tech', max: 16000 },\r\n            { name: 'Support', max: 30000 },\r\n            { name: 'Dev', max: 38000 },\r\n            { name: 'Sales', max: 52000 },\r\n            { name: 'Marketing', max: 25000 }\r\n          ],\r\n          name: {\r\n            formatter: '{value}',\r\n            textStyle: {\r\n              color: getColor('tertiary-color')\r\n            }\r\n          },\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: rgbaColor(getColor('tertiary-color'))\r\n            }\r\n          }\r\n        },\r\n\r\n        {\r\n          indicator: [\r\n            { text: 'Language', max: 150 },\r\n            { text: 'Math', max: 150 },\r\n            { text: 'English', max: 150 },\r\n            { text: 'physics', max: 120 },\r\n            { text: 'Chemistry', max: 108 },\r\n            { text: 'Biology', max: 72 }\r\n          ],\r\n          radius: window.innerWidth < 576 ? 90 : 120,\r\n          center: window.innerWidth < 992 ? ['50%', '75%'] : ['75%', '50%'],\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: getColor('tertiary-color')\r\n            }\r\n          },\r\n          name: {\r\n            textStyle: {\r\n              color: getColor('tertiary-color'),\r\n              backgroundColor: rgbaColor(getColor('body-highlight-bg')),\r\n              borderRadius: 3,\r\n              padding: [3, 5]\r\n            }\r\n          }\r\n        }\r\n      ],\r\n\r\n      series: [\r\n        {\r\n          type: 'radar',\r\n          data: [\r\n            {\r\n              value: [5200, 4000, 20000, 30000, 20000, 18000],\r\n              name: 'Data A',\r\n              itemStyle: {\r\n                color: getColor('info')\r\n              },\r\n              areaStyle: {\r\n                color: rgbaColor(getColor('info'), 0.3)\r\n              }\r\n            },\r\n            {\r\n              value: [5000, 12000, 28000, 26000, 32000, 21000],\r\n              name: 'Data B',\r\n              itemStyle: {\r\n                color: getColor('success')\r\n              },\r\n              areaStyle: {\r\n                color: rgbaColor(getColor('success'), 0.3)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n\r\n        {\r\n          type: 'radar',\r\n          radarIndex: 1,\r\n          data: [\r\n            {\r\n              value: [130, 110, 130, 100, 99, 70],\r\n              name: 'Data C',\r\n              symbol: 'rect',\r\n              symbolSize: 12,\r\n              lineStyle: {\r\n                type: 'dashed'\r\n              },\r\n              itemStyle: {\r\n                color: getColor('warning')\r\n              },\r\n              areaStyle: {\r\n                color: rgbaColor(getColor('warning'), 0.3)\r\n              },\r\n              label: {\r\n                show: true,\r\n                formatter: params => {\r\n                  return params.value;\r\n                },\r\n                color: getColor('tertiary-color')\r\n              }\r\n            },\r\n            {\r\n              value: [100, 93, 50, 90, 70, 60],\r\n              name: 'Data D',\r\n              itemStyle: {\r\n                color: getColor('danger')\r\n              },\r\n              areaStyle: {\r\n                color: rgbaColor(getColor('danger'), 0.3)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n\r\n    const responsiveOptions = {\r\n      xs: {\r\n        radar: [\r\n          {\r\n            center: ['50%', '30%'],\r\n            radius: 90\r\n          },\r\n          {\r\n            center: ['50%', '75%'],\r\n            radius: 90\r\n          }\r\n        ]\r\n      },\r\n      sm: {\r\n        radar: [\r\n          {\r\n            radius: 120\r\n          },\r\n          {\r\n            radius: 120\r\n          }\r\n        ]\r\n      },\r\n      xl: {\r\n        radar: [\r\n          {\r\n            center: ['25%', '50%']\r\n          },\r\n          {\r\n            center: ['75%', '50%']\r\n          }\r\n        ]\r\n      }\r\n    };\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions, responsiveOptions);\r\n  }\r\n};\r\n\r\nexport default radarCustomizedChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst radarMultipleChartInit = () => {\r\n  const { getColor, getData, rgbaColor, resize } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-radar-multiple-chart-example'\r\n  );\r\n\r\n  const months = [\r\n    'January',\r\n    'February',\r\n    'March',\r\n    'April',\r\n    'May',\r\n    'June',\r\n    'July',\r\n    'August',\r\n    'September',\r\n    'October',\r\n    'November',\r\n    'December'\r\n  ];\r\n\r\n  const getCenter = () => {\r\n    if (window.innerWidth < 1540 && window.innerWidth > 992) {\r\n      return [\r\n        ['25%', '40%'],\r\n        ['50%', '75%'],\r\n        ['75%', '40%']\r\n      ];\r\n    }\r\n    if (window.innerWidth < 992) {\r\n      return [\r\n        ['50%', '20%'],\r\n        ['50%', '50%'],\r\n        ['50%', '80%']\r\n      ];\r\n    }\r\n    return [\r\n      ['15%', '50%'],\r\n      ['50%', '50%'],\r\n      ['85%', '50%']\r\n    ];\r\n  };\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      legend: {\r\n        left: 'left',\r\n        textStyle: {\r\n          color: getColor('tertiary-color')\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        position(pos, ...size) {\r\n          if (window.innerWidth <= 540) {\r\n            const tooltipHeight = size[1].offsetHeight;\r\n            const obj = { top: pos[1] - tooltipHeight - 20 };\r\n            obj[pos[0] < size[3].viewSize[0] / 2 ? 'left' : 'right'] = 5;\r\n            return obj;\r\n          }\r\n          return null;\r\n        }\r\n      },\r\n\r\n      radar: [\r\n        {\r\n          indicator: [\r\n            { text: 'Brand', max: 100 },\r\n            { text: 'content', max: 100 },\r\n            { text: 'Usability', max: 100 },\r\n            { text: 'Features', max: 100 }\r\n          ],\r\n          center: getCenter()[0],\r\n          radius: 85,\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: rgbaColor(getColor('tertiary-color'))\r\n            }\r\n          }\r\n        },\r\n        {\r\n          indicator: [\r\n            { text: 'Exterior', max: 100 },\r\n            { text: 'Take pictures', max: 100 },\r\n            { text: 'system', max: 100 },\r\n            { text: 'performance', max: 100 },\r\n            { text: 'screen', max: 100 }\r\n          ],\r\n          radius: 85,\r\n          center: getCenter()[1],\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: rgbaColor(getColor('tertiary-color'))\r\n            }\r\n          }\r\n        },\r\n        {\r\n          indicator: months.map(month => ({\r\n            text: month,\r\n            max: 100\r\n          })),\r\n          center: getCenter()[2],\r\n          radius: 85,\r\n          splitLine: {\r\n            lineStyle: {\r\n              color: rgbaColor(getColor('tertiary-color'))\r\n            }\r\n          }\r\n        }\r\n      ],\r\n\r\n      series: [\r\n        {\r\n          type: 'radar',\r\n          tooltip: {\r\n            trigger: 'item'\r\n          },\r\n          areaStyle: {\r\n            color: rgbaColor(getColor('info'), 0.5)\r\n          },\r\n          data: [\r\n            {\r\n              value: [60, 73, 85, 40],\r\n              name: 'A software',\r\n              itemStyle: {\r\n                color: getColor('info')\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          type: 'radar',\r\n          radarIndex: 1,\r\n          data: [\r\n            {\r\n              value: [85, 90, 90, 95, 95],\r\n              name: 'A staple mobile phone',\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('primary'), 0.8)\r\n              },\r\n              areaStyle: {\r\n                color: rgbaColor(getColor('primary'), 0.3)\r\n              }\r\n            },\r\n            {\r\n              value: [95, 80, 75, 90, 93],\r\n              name: 'A fruit phone',\r\n              itemStyle: {\r\n                color: getColor('success')\r\n              },\r\n              areaStyle: {\r\n                color: rgbaColor(getColor('success'), 0.3)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          type: 'radar',\r\n          radarIndex: 2,\r\n          areaStyle: {},\r\n          tooltip: {\r\n            show: false\r\n          },\r\n          data: [\r\n            {\r\n              name: 'Precipitation',\r\n              value: [\r\n                2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 75.6, 82.2, 48.7, 18.8, 6.0,\r\n                2.3\r\n              ],\r\n              itemStyle: {\r\n                color: getColor('primary')\r\n              },\r\n              areaStyle: {\r\n                color: rgbaColor(getColor('primary'), 0.5)\r\n              }\r\n            },\r\n            {\r\n              name: 'Evaporation',\r\n              value: [\r\n                2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 35.6, 62.2, 32.6, 20.0, 6.4,\r\n                3.3\r\n              ],\r\n              itemStyle: {\r\n                color: getColor('warning')\r\n              },\r\n              areaStyle: {\r\n                color: rgbaColor(getColor('warning'), 0.5)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    resize(() => {\r\n      chart.setOption({\r\n        radar: getCenter().map(item => ({\r\n          center: item\r\n        }))\r\n      });\r\n    });\r\n  }\r\n};\r\n\r\nexport default radarMultipleChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst heatmapChartInit = () => {\r\n  const { getColor, getData, rgbaColor, getRandomNumber } =\r\n    window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echart-heatmap-chart-example');\r\n\r\n  const hours = [\r\n    '12a',\r\n    '2a',\r\n    '4a',\r\n    '6a',\r\n    '8a',\r\n    '10a',\r\n    '12p',\r\n    '2p',\r\n    '4p',\r\n    '6p',\r\n    '8p',\r\n    '10p'\r\n  ];\r\n  const days = [\r\n    'Monday',\r\n    'Tuesday',\r\n    'Wednesday',\r\n    'Thursday',\r\n    'Friday',\r\n    'Saturday',\r\n    'Sunday'\r\n  ];\r\n\r\n  const data = [];\r\n  for (let i = 0; i < 7; i += 1) {\r\n    for (let j = 0; j < 12; j += 1) {\r\n      data.push([j, i, getRandomNumber(5, 12)]);\r\n    }\r\n  }\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        position: 'top',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1\r\n      },\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        top: 5,\r\n        bottom: '15%',\r\n        containLabel: true\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: hours,\r\n        splitArea: {\r\n          show: true\r\n        },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('quaternary-color')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'category',\r\n        data: days,\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          color: getColor('quaternary-color')\r\n        },\r\n        splitArea: {\r\n          show: true\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('quaternary-color')\r\n          }\r\n        }\r\n      },\r\n      visualMap: {\r\n        min: 0,\r\n        max: 10,\r\n        calculable: true,\r\n        orient: 'horizontal',\r\n        left: 'center',\r\n        bottom: '0%',\r\n        textStyle: {\r\n          color: getColor('tertiary-color'),\r\n          fontWeight: 500\r\n        },\r\n        inRange: {\r\n          color: [\r\n            rgbaColor(getColor('primary'), 1),\r\n            rgbaColor(getColor('info'), 1),\r\n            rgbaColor(getColor('success'), 1)\r\n            // utils.rgbaColor(utils.getColors()['warning'], 1),\r\n            // utils.rgbaColor(utils.getColors()['danger'], 1)\r\n          ]\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          type: 'heatmap',\r\n          data,\r\n          label: {\r\n            show: true\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowColor: rgbaColor(getColor('black'), 0.5)\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default heatmapChartInit;\r\n", "import { echartSetOption } from '../echarts-utils';\r\n\r\nconst heatmapSingleSeriesChartInit = () => {\r\n  const { getColor, getData, rgbaColor, getRandomNumber } =\r\n    window.phoenix.utils;\r\n  const $chartEl = document.querySelector(\r\n    '.echart-heatmap-single-series-chart-example'\r\n  );\r\n\r\n  const hours = [\r\n    '12a',\r\n    '2a',\r\n    '4a',\r\n    '6a',\r\n    '8a',\r\n    '10a',\r\n    '12p',\r\n    '2p',\r\n    '4p',\r\n    '6p',\r\n    '8p',\r\n    '10p'\r\n  ];\r\n  const days = [\r\n    'Monday',\r\n    'Tuesday',\r\n    'Wednesday',\r\n    'Thursday',\r\n    'Friday',\r\n    'Saturday',\r\n    'Sunday'\r\n  ];\r\n\r\n  const data = [];\r\n  for (let i = 0; i < 7; i += 1) {\r\n    for (let j = 0; j < 12; j += 1) {\r\n      data.push([j, i, getRandomNumber(1, 12)]);\r\n    }\r\n  }\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'echarts');\r\n    const chart = window.echarts.init($chartEl);\r\n    const getDefaultOptions = () => ({\r\n      gradientColor: [\r\n        rgbaColor(getColor('info'), 1),\r\n        rgbaColor(getColor('primary'), 1)\r\n      ],\r\n\r\n      tooltip: {\r\n        position: 'top',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1\r\n      },\r\n      grid: {\r\n        right: 5,\r\n        left: 5,\r\n        top: 5,\r\n        bottom: 5,\r\n        containLabel: true\r\n      },\r\n      xAxis: {\r\n        axisTick: { show: false },\r\n        type: 'category',\r\n        data: hours,\r\n        splitArea: {\r\n          show: true\r\n        },\r\n        axisLabel: {\r\n          color: getColor('quaternary-color')\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('quaternary-color')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        axisTick: { show: false },\r\n        type: 'category',\r\n        data: days,\r\n        axisLabel: {\r\n          formatter: value => value.substring(0, 3),\r\n          color: getColor('quaternary-color')\r\n        },\r\n        splitArea: {\r\n          show: true\r\n        },\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('quaternary-color')\r\n          }\r\n        }\r\n      },\r\n      visualMap: {\r\n        show: false,\r\n        min: 0,\r\n        max: 10,\r\n        calculable: true,\r\n        orient: 'horizontal',\r\n        left: 'center',\r\n        bottom: '0%',\r\n        textStyle: {\r\n          color: getColor('quaternary-color'),\r\n          fontWeight: 500\r\n        }\r\n      },\r\n\r\n      series: [\r\n        {\r\n          type: 'heatmap',\r\n          data,\r\n          label: {\r\n            show: true\r\n          },\r\n          itemStyle: {\r\n            borderColor: getColor('body-highlight-bg'),\r\n            borderWidth: 3\r\n          },\r\n          emphasis: {\r\n            itemStyle: {\r\n              shadowBlur: 10,\r\n              shadowColor: rgbaColor(getColor('black'), 0.5)\r\n            }\r\n          }\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default heatmapSingleSeriesChartInit;\r\n", "import basicLineChartInit from './examples/basic-line-chart';\r\nimport basicAreaLineChartInit from './examples/area-line-chart';\r\nimport stackedLineChartInit from './examples/stacked-line-chart';\r\nimport stackedAreaChartInit from './examples/stacked-area-chart';\r\nimport lineMarkerChartInit from './examples/line-marker-chart';\r\nimport areaPiecesChartInit from './examples/area-pieces-chart';\r\nimport stepLineChartInit from './examples/step-line-chart';\r\nimport lineGradientChartInit from './examples/line-gradient-chart';\r\nimport dynamicLineChartInit from './examples/dynamic-line-chart';\r\nimport lineLogChartInit from './examples/line-log-chart';\r\nimport shareDatasetChartInit from './examples/share-dataset-chart';\r\nimport basicBarChartInit from './examples/basic-bar-chart';\r\nimport horizontalBarChartInit from './examples/horizontal-bar-chart';\r\nimport barNegativeChartInit from './examples/bar-negative-chart';\r\nimport seriesBarChartInit from './examples/series-bar-chart';\r\nimport stackedBarChartInit from './examples/stacked-bar-chart';\r\nimport stackedHorizontalBarChartInit from './examples/stacked-horizontal-bar-chart';\r\nimport barRaceChartInit from './examples/bar-race-chart';\r\nimport barGradientChartInit from './examples/bar-gradient-chart';\r\nimport barLineMixedChartInit from './examples/bar-line-mixed-chart';\r\nimport barWaterFallChartInit from './examples/bar-waterfall-chart';\r\nimport barTimelineChartInit from './examples/bar-timeline-chart';\r\nimport basicCandlestickChartInit from './examples/basic-candlestick-chart';\r\nimport candlestickMixedChartInit from './examples/candlestick-mixed-chart';\r\nimport sessionByCountryMapInit from './examples/session-by-country-map';\r\nimport mapUSAInit from './examples/map-usa';\r\nimport basicScatterChartInit from './examples/basic-scatter-chart';\r\nimport bubbleChartInit from './examples/bubble-chart';\r\nimport quartetScatterChartInit from './examples/quartet-scatter-chart';\r\nimport singleAxisScatterChartInit from './examples/single-axis-scatter-chart';\r\nimport pieChartInit from './examples/pie-chart';\r\nimport doughnutChartInit from './examples/doughnut-chart';\r\nimport doughnutRoundedChartInit from './examples/doughnut-rounded-chart';\r\nimport pieMultipleChartInit from './examples/pie-multiple-chart';\r\nimport pieLabelAlignChartInit from './examples/pie-label-align-chart';\r\nimport pieEdgeAlignChartInit from './examples/pie-edge-align-chart';\r\nimport basicGaugeChartInit from './examples/basic-gauge-chart';\r\nimport gaugeProgressChartInit from './examples/gauge-progress-chart';\r\nimport gaugeRingChartInit from './examples/gauge-ring-chart';\r\nimport gaugeMultiRingChartInit from './examples/gauge-multi-ring-chart';\r\nimport gaugeMultiTitleChartInit from './examples/gauge-multi-title-chart';\r\nimport gaugeGradeChartInit from './examples/gauge-grade-chart';\r\nimport radarChartInit from './examples/radar-chart';\r\nimport radarCustomizedChartInit from './examples/radar-customized-chart';\r\nimport radarMultipleChartInit from './examples/radar-multiple-chart';\r\nimport heatmapChartInit from './examples/heatmap-chart';\r\nimport heatmapSingleSeriesChartInit from './examples/heatmap-single-series-chart';\r\n\r\nconst { docReady } = window.phoenix.utils;\r\n\r\ndocReady(basicLineChartInit);\r\ndocReady(basicAreaLineChartInit);\r\ndocReady(stackedLineChartInit);\r\ndocReady(stackedAreaChartInit);\r\ndocReady(lineMarkerChartInit);\r\ndocReady(areaPiecesChartInit);\r\ndocReady(stepLineChartInit);\r\ndocReady(lineGradientChartInit);\r\ndocReady(dynamicLineChartInit);\r\ndocReady(lineLogChartInit);\r\ndocReady(shareDatasetChartInit);\r\ndocReady(basicBarChartInit);\r\ndocReady(horizontalBarChartInit);\r\ndocReady(barNegativeChartInit);\r\ndocReady(seriesBarChartInit);\r\ndocReady(stackedBarChartInit);\r\ndocReady(stackedHorizontalBarChartInit);\r\ndocReady(barRaceChartInit);\r\ndocReady(barGradientChartInit);\r\ndocReady(barLineMixedChartInit);\r\ndocReady(barWaterFallChartInit);\r\ndocReady(barTimelineChartInit);\r\ndocReady(basicCandlestickChartInit);\r\ndocReady(candlestickMixedChartInit);\r\ndocReady(sessionByCountryMapInit);\r\ndocReady(mapUSAInit);\r\ndocReady(basicScatterChartInit);\r\ndocReady(bubbleChartInit);\r\ndocReady(quartetScatterChartInit);\r\ndocReady(singleAxisScatterChartInit);\r\ndocReady(pieChartInit);\r\ndocReady(doughnutChartInit);\r\ndocReady(doughnutRoundedChartInit);\r\ndocReady(pieMultipleChartInit);\r\ndocReady(pieLabelAlignChartInit);\r\ndocReady(pieEdgeAlignChartInit);\r\ndocReady(basicGaugeChartInit);\r\ndocReady(gaugeProgressChartInit);\r\ndocReady(gaugeRingChartInit);\r\ndocReady(gaugeMultiRingChartInit);\r\ndocReady(gaugeMultiTitleChartInit);\r\ndocReady(gaugeGradeChartInit);\r\ndocReady(radarChartInit);\r\ndocReady(radarCustomizedChartInit);\r\ndocReady(radarMultipleChartInit);\r\ndocReady(heatmapChartInit);\r\ndocReady(heatmapSingleSeriesChartInit);\r\n"], "names": [], "mappings": ";;;;;EAAA;EACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;AAC3B;EACA;EACO,MAAM,eAAe,GAAG;EAC/B,EAAE,KAAK;EACP,EAAE,WAAW;EACb,EAAE,iBAAiB;EACnB,EAAE,iBAAiB;EACnB,KAAK;EACL,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACvD,EAAE,MAAM,YAAY,GAAG,OAAO,IAAI;EAClC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;EACzC,MAAM,IAAI,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE;EACjD,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;AAC3D;EACA,EAAE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,aAAa;EACrD,IAAI,yBAAyB;EAC7B,GAAG,CAAC;EACJ,EAAE,IAAI,oBAAoB,EAAE;EAC5B,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,MAAM;EAC1E,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;EACrB,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,MAAM,CAAC,MAAM;EACf,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;EACnB,IAAI,IAAI,iBAAiB,EAAE;EAC3B,MAAM,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACtC,KAAK;EACL,GAAG,CAAC,CAAC;EACL,EAAE,IAAI,iBAAiB,EAAE;EACzB,IAAI,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACpC,GAAG;AACH;EACA,EAAE,eAAe,CAAC,gBAAgB;EAClC,IAAI,cAAc;EAClB,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK;EACjC,MAAM,IAAI,OAAO,KAAK,cAAc,EAAE;EACtC,QAAQ,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;EAC1E,OAAO;EACP,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK;EACL,GAAG,CAAC;EACJ,CAAC,CAAC;EACF;AACA;EACA,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;EACvE,IAAI,UAAU,EAAE;EAChB,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,IAAI;EAC5B,IAAI,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI;EAC9C,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;EAC1B,MAAM,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;EAC1B,MAAM,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE,aAAa,CAAC,mBAAmB,CAAC,CAAC;EAChE,MAAM,IAAI,KAAK,EAAE;EACjB,QAAQ,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;EAC5C,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACO,MAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,aAAa,GAAG,QAAQ,KAAK;EACtE,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;EACvB,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI;EACvB,IAAI,WAAW,IAAI,CAAC;AACpB,2FAA2F;AAC3F,UAAU,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,KAAK;AACpD,SAAS;AACT,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG;AAC7B,MAAM,OAAO,EAAE,CAAC,KAAK,KAAK,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK;AAC3D,KAAK;AACL;AACA,YAAY,CAAC,CAAC;EACd,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,CAAC;AACV;AACA,cAAc;AACd,gBAAgB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;AAC3D,oBAAoB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;AAC3E,oBAAoB,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;AACvC,eAAe;AACf;AACA,YAAY,EAAE,WAAW,CAAC;AAC1B,gBAAgB,CAAC,CAAC;EAClB,CAAC,CAAC;AACF;EACO,MAAM,qBAAqB,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK;EAC/D;EACA,EAAE,IAAI,MAAM,CAAC,UAAU,IAAI,GAAG,EAAE;EAChC,IAAI,MAAM,aAAa,GAAG,GAAG,CAAC,YAAY,CAAC;EAC3C,IAAI,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;EACrD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EAC9D,IAAI,OAAO,GAAG,CAAC;EACf,GAAG;EACH,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;;EC3GD,MAAM,kBAAkB,GAAG,MAAM;EACjC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,4BAA4B,CAAC,CAAC;EACxE,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,KAAK;EACT,IAAI,MAAM;EACV,IAAI,MAAM;EACV,IAAI,QAAQ;EACZ,IAAI,WAAW;EACf,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,UAAU;EACd,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;EAC1E,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,OAAO,CAAC;AACZ;AACA;AACA,wDAAwD,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;AAChF,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAChD;AACA;AACA,IAAI,CAAC,CAAC;EACN,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,gBAAgB;EACnC,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,QAAQ;EAC1B,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,GAAG,EAAE,GAAG;EAChB,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI;EACd,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,cAAc,EAAE,IAAI;EAC9B,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;EAClE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC9GD,MAAM,sBAAsB,GAAG,MAAM;EACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,iCAAiC,CAAC,CAAC;EAC7E,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,KAAK;EACT,IAAI,MAAM;EACV,IAAI,MAAM;EACV,IAAI,QAAQ;EACZ,IAAI,WAAW;EACf,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,UAAU;EACd,GAAG,CAAC;EACJ,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;EACzE,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,OAAO,CAAC;AACZ;AACA;AACA,wDAAwD,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;AAChF,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAChD;AACA;AACA,IAAI,CAAC,CAAC;EACN,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,GAAG,EAAE,GAAG;EAChB,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI;EACd,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,cAAc,EAAE,IAAI;EAC9B,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE;EACnB,cAAc,IAAI,EAAE,QAAQ;EAC5B,cAAc,CAAC,EAAE,CAAC;EAClB,cAAc,CAAC,EAAE,CAAC;EAClB,cAAc,EAAE,EAAE,CAAC;EACnB,cAAc,EAAE,EAAE,CAAC;EACnB,cAAc,UAAU,EAAE;EAC1B,gBAAgB;EAChB,kBAAkB,MAAM,EAAE,CAAC;EAC3B,kBAAkB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC5D,iBAAiB;EACjB,gBAAgB;EAChB,kBAAkB,MAAM,EAAE,CAAC;EAC3B,kBAAkB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;EAC1D,iBAAiB;EACjB,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;EAClE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC5HD,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;EAChF,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,QAAQ;EACZ,IAAI,SAAS;EACb,IAAI,WAAW;EACf,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,YAAY,IAAI,EAAE,QAAQ;EAC1B,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,cAAc;EAC9B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC;EACzC,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAClD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,cAAc;EAC9B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC;EAC3C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACrC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,gBAAgB;EAChC,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,cAAc;EAC9B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtD,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE;EACzE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC1JD,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;EAChF,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,QAAQ;EACZ,IAAI,SAAS;EACb,IAAI,WAAW;EACf,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,cAAc;EAC9B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAClD,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;EACnD,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC;EACzC,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACtD,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,cAAc;EAC9B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC;EACrD,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC;EAC3C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACrC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,gBAAgB;EAChC,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACtD,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,cAAc;EAC9B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,KAAK,EAAE,SAAS;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACtD,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACtD,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE;EACzE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC5KD,MAAM,mBAAmB,GAAG,MAAM;EAClC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,mCAAmC,CAAC,CAAC;EAC/E,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,QAAQ;EACZ,IAAI,SAAS;EACb,IAAI,WAAW;EACf,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B;EACA,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE;EACd,UAAU;EACV,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACjD,aAAa;EACb,WAAW;EACX,UAAU;EACV,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACjD,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,SAAS,EAAE,gBAAgB;EACnC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;EAC3C,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,aAAa;EACb,YAAY,IAAI,EAAE;EAClB,cAAc,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;EAC1C,cAAc,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE;EAC1C,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,aAAa;EACb,YAAY,KAAK,EAAE;EACnB,cAAc,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACjD,aAAa;EACb,YAAY,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;EACxD,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC;EAC3C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACrC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACvC,aAAa;EACb,YAAY,KAAK,EAAE;EACnB,cAAc,KAAK,EAAE,MAAM;EAC3B,aAAa;EACb,YAAY,IAAI,EAAE,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC;EAC/E,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACvC,aAAa;EACb,YAAY,KAAK,EAAE;EACnB,cAAc,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACjD,aAAa;EACb,YAAY,IAAI,EAAE;EAClB,cAAc,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,SAAS,EAAE;EAClD,cAAc;EACd,gBAAgB;EAChB,kBAAkB,MAAM,EAAE,MAAM;EAChC,kBAAkB,CAAC,EAAE,KAAK;EAC1B,kBAAkB,KAAK,EAAE,KAAK;EAC9B,iBAAiB;EACjB,gBAAgB;EAChB,kBAAkB,MAAM,EAAE,QAAQ;EAClC,kBAAkB,KAAK,EAAE;EACzB,oBAAoB,QAAQ,EAAE,OAAO;EACrC,oBAAoB,SAAS,EAAE,KAAK;EACpC,mBAAmB;EACnB,kBAAkB,IAAI,EAAE,KAAK;EAC7B,kBAAkB,IAAI,EAAE,eAAe;EACvC,iBAAiB;EACjB,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;EAClE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECjLD,MAAM,mBAAmB,GAAG,MAAM;EAClC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,mCAAmC,CAAC,CAAC;AAC/E;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EAClE,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,OAAO;EACP,MAAM,SAAS,EAAE;EACjB,QAAQ,IAAI,EAAE,WAAW;EACzB,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,SAAS,EAAE,CAAC;EACpB,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,EAAE,EAAE,CAAC;EACjB,YAAY,EAAE,EAAE,CAAC;EACjB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACtD,WAAW;EACX,UAAU;EACV,YAAY,EAAE,EAAE,CAAC;EACjB,YAAY,EAAE,EAAE,CAAC;EACjB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACtD,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,MAAM,EAAE,GAAG;EACrB,UAAU,MAAM,EAAE,MAAM;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,KAAK,EAAE,CAAC;EACpB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,MAAM,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EACpC,YAAY,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,YAAY,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;EAC1E,WAAW;EACX,UAAU,SAAS,EAAE,EAAE;EACvB,UAAU,IAAI,EAAE;EAChB,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;EAC/B,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;EAC/B,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;EAC/B,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;EAC/B,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;EAC/B,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;EAC/B,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;EAC/B,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;EAC/B,YAAY,CAAC,YAAY,EAAE,GAAG,CAAC;EAC/B,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE;EACzE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC3GD,MAAM,iBAAiB,GAAG,MAAM;EAChC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,iCAAiC,CAAC,CAAC;AAC7E;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,QAAQ;EACZ,IAAI,SAAS;EACb,IAAI,WAAW;EACf,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;AAC3E;EACA,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,WAAW,EAAE;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,YAAY;EAC5B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAClD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,aAAa;EAC7B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC;EAC3C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACrC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;EACjE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC7HD,MAAM,qBAAqB,GAAG,MAAM;EACpC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,qCAAqC;EACzC,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,GAAG,CAAC;EACvB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,IAAI,CAAC,YAAY,EAAE,EAAE,CAAC;EACtB,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI;EACpC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;EACnB,GAAG,CAAC,CAAC;EACL,EAAE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI;EACrC,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;EACnB,GAAG,CAAC,CAAC;AACL;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,SAAS,EAAE;EACjB,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,IAAI,EAAE,YAAY;EAC1B,QAAQ,SAAS,EAAE,CAAC;EACpB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,GAAG,EAAE,QAAQ,CAAC,MAAM,GAAG,CAAC;EAChC,QAAQ,KAAK,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;EACxD,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,QAAQ;EACtB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EAClE,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,YAAY,IAAI,EAAE,QAAQ;EAC1B,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;EACjE,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,UAAU,EAAE,KAAK;EACzB,QAAQ,UAAU,EAAE,EAAE;EACtB,QAAQ,MAAM,EAAE,QAAQ;EACxB,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,SAAS,EAAE;EACnB,UAAU,WAAW,EAAE,CAAC;EACxB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC5ID,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;AAChF;EACA,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC;EAClB,EAAE,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;EAClC,EAAE,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;EAClC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;AACnC;EACA,EAAE,MAAM,UAAU,GAAG,MAAM;EAC3B,IAAI,GAAG,GAAG,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;EAClC,IAAI,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;EAC5C,IAAI,OAAO;EACX,MAAM,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE;EAC1B,MAAM,KAAK,EAAE;EACb,QAAQ,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;EACxE,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;EACzB,OAAO;EACP,KAAK,CAAC;EACN,GAAG,CAAC;AACJ;EACA;EACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;EACjC,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;EAC5B,GAAG;AACH;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,SAAS,EAAE,KAAK;EAC1B,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;AACT;EACA,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,WAAW,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;EAChC,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,cAAc,EAAE,KAAK;EAC/B,UAAU,IAAI;EACd,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,UAAU,EAAE,EAAE;EACxB,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;EAC9D,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3D;EACA,IAAI,WAAW,CAAC,MAAM;EACtB;EACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;EAClC,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;EACrB,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;EAChC,OAAO;AACP;EACA,MAAM,KAAK,CAAC,SAAS,CAAC;EACtB,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,IAAI;EAChB,WAAW;EACX,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK,EAAE,IAAI,CAAC,CAAC;EACb,GAAG;EACH,CAAC;;EChHD,MAAM,gBAAgB,GAAG,MAAM;EAC/B,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;AAC5E;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,QAAQ,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC;EAChE,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,YAAY;EAC5B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC;EACvD,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC;EAC3C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACrC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,YAAY;EAC5B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EAClD,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,cAAc;EAC9B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE;EAChB,YAAY,CAAC,GAAG,CAAC;EACjB,YAAY,CAAC,GAAG,CAAC;EACjB,YAAY,CAAC,GAAG,CAAC;EACjB,YAAY,CAAC,GAAG,EAAE;EAClB,YAAY,CAAC,GAAG,EAAE;EAClB,YAAY,CAAC,GAAG,EAAE;EAClB,YAAY,CAAC,GAAG,GAAG;EACnB,YAAY,CAAC,GAAG,GAAG;EACnB,YAAY,CAAC,GAAG,GAAG;EACnB,WAAW;EACX,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC;EACzC,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,EAAE;EACjB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,EAAE;EACf,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC/GD,MAAM,qBAAqB,GAAG,MAAM;EACpC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,qCAAqC;EACzC,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,CAAC,QAAQ,CAAC;EAC1B,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,MAAM,CAAC;EACxB,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE,KAAK;EAC1B,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,MAAM,EAAE;EAChB,UAAU,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACrE,UAAU,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC1D,UAAU,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC9D,UAAU,CAAC,cAAc,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC9D,UAAU,CAAC,gBAAgB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC;EAC9D,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,WAAW,EAAE;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,SAAS,EAAE,CAAC;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,MAAM,EAAE,IAAI;EACtB,UAAU,cAAc,EAAE,KAAK;EAC/B,UAAU,QAAQ,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,QAAQ,CAAC;EAC3C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACrC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,MAAM,EAAE,IAAI;EACtB,UAAU,cAAc,EAAE,KAAK;EAC/B,UAAU,QAAQ,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC;EACzC,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,MAAM,EAAE,IAAI;EACtB,UAAU,cAAc,EAAE,KAAK;EAC/B,UAAU,QAAQ,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,MAAM,EAAE,IAAI;EACtB,UAAU,cAAc,EAAE,KAAK;EAC/B,UAAU,QAAQ,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,EAAE,EAAE,KAAK;EACnB,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,QAAQ,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE;EACrC,UAAU,KAAK,EAAE;EACjB,YAAY,SAAS,EAAE,qBAAqB;EAC5C,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,QAAQ,EAAE,SAAS;EAC/B,YAAY,KAAK,EAAE,MAAM;EACzB,YAAY,OAAO,EAAE,MAAM;EAC3B,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,EAAE;EACjB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC1JD,MAAM,iBAAiB,GAAG,MAAM;EAChC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,iCAAiC,CAAC,CAAC;EAC7E,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,KAAK;EACT,IAAI,MAAM;EACV,IAAI,MAAM;EACV,IAAI,QAAQ;EACZ,IAAI,WAAW;EACf,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,UAAU;EACd,GAAG,CAAC;EACJ,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;EACzE,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,WAAW,EAAE,IAAI;EACzB,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,GAAG,EAAE,GAAG;EAChB,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,IAAI;EACd,UAAU,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;EACnD,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,cAAc,EAAE,IAAI;EAC9B,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;EAClE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC/FD,MAAM,sBAAsB,GAAG,MAAM;EACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,sCAAsC;EAC1C,GAAG,CAAC;EACJ,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,KAAK;EACT,IAAI,MAAM;EACV,IAAI,MAAM;EACV,IAAI,QAAQ;EACZ,IAAI,WAAW;EACf,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,UAAU;EACd,GAAG,CAAC;EACJ,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;EACzE,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,SAAS,EAAE,gBAAgB;EACnC,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;EAChC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,GAAG,EAAE,GAAG;EAChB,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,WAAW,EAAE,IAAI;EACzB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,IAAI;EACd,UAAU,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;EACnD,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,cAAc,EAAE,IAAI;EAC9B,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;EAClE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECrGD,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;AAChF;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,QAAQ;EACxB,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,KAAK,EAAE,CAAC;EAChB,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,QAAQ,EAAE,KAAK;EACvB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,QAAQ;EAC1B,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,QAAQ,IAAI,EAAE;EACd,UAAU,KAAK;EACf,UAAU,MAAM;EAChB,UAAU,OAAO;EACjB,UAAU,OAAO;EACjB,UAAU,KAAK;EACf,UAAU,MAAM;EAChB,UAAU,MAAM;EAChB,UAAU,OAAO;EACjB,UAAU,KAAK;EACf,UAAU,KAAK;EACf,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE,KAAK;EAC5B,YAAY,KAAK,EAAE,MAAM;EACzB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAChF,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC3ED,MAAM,kBAAkB,GAAG,MAAM;EACjC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;AAC9E;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;EACpD,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,QAAQ;EACxB,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;EAChD,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,QAAQ;EAC1B,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,QAAQ,IAAI,EAAE,CAAC,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC;EAC9D,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;EACvD,UAAU,SAAS,EAAE;EACrB,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC;EACvD,UAAU,SAAS,EAAE;EACrB,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,CAAC,EAAE;EAC7D,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EChFD,MAAM,mBAAmB,GAAG,MAAM;EAClC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,mCAAmC,CAAC,CAAC;AAC/E;EACA,EAAE,MAAM,SAAS,GAAG,EAAE,CAAC;EACvB,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;EACnB,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;EACnB,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;EACnB,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB;EACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;EAClC,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1C,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EACjD,IAAI,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAC1C,GAAG;AACH;EACA,EAAE,MAAM,aAAa,GAAG;EACxB,IAAI,SAAS,EAAE;EACf,MAAM,UAAU,EAAE,EAAE;EACpB,MAAM,WAAW,EAAE,SAAS,CAAC,QAAQ,CAAC,qBAAqB,CAAC,EAAE,GAAG,CAAC;EAClE,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,MAAM,CAAC;EACxB,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,QAAQ,CAAC;EAC1B,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EAC9C,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,QAAQ,IAAI,EAAE,CAAC;EACf,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE;EACjB,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACpC,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,WAAW,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EACjD,UAAU,WAAW,EAAE,CAAC;EACxB,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;AAClC;EACA,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;AACT;EACA,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,KAAK;EACtB,UAAU,QAAQ,EAAE,aAAa;EACjC,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,KAAK;EACtB,UAAU,QAAQ,EAAE,aAAa;EACjC,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,KAAK;EACtB,UAAU,QAAQ,EAAE,aAAa;EACjC,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,KAAK;EACtB,UAAU,QAAQ,EAAE,aAAa;EACjC,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,MAAM,EAAE,EAAE;EAClB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC7HD,MAAM,6BAA6B,GAAG,MAAM;EAC5C,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,8CAA8C;EAClD,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,QAAQ;EACZ,IAAI,SAAS;EACb,IAAI,WAAW;EACf,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,CAAC,MAAM,CAAC;EACxB,QAAQ,QAAQ,CAAC,QAAQ,CAAC;EAC1B,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,QAAQ;EACxB,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,SAAS,EAAE,gBAAgB;EACnC,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE;EACjB,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;EACpC,WAAW;EACX,SAAS;EACT,QAAQ,KAAK,EAAE,CAAC;EAChB,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE;EACd,UAAU,QAAQ;EAClB,UAAU,SAAS;EACnB,UAAU,cAAc;EACxB,UAAU,UAAU;EACpB,UAAU,eAAe;EACzB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,QAAQ,IAAI,EAAE,CAAC;EACf,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,MAAM;EAC3B,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,cAAc;EAC9B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,MAAM;EAC3B,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,MAAM;EAC3B,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,eAAe;EAC/B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACxD,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,EAAE;EACjB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECrLD,MAAM,gBAAgB,GAAG,MAAM;EAC/B,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;AAC5E;EACA,EAAE,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC;EAC7C,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;EACnC,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,GAAG,EAAE,SAAS;EACtB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACjD,QAAQ,OAAO,EAAE,IAAI;EACrB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,iBAAiB,EAAE,GAAG;EAC9B,QAAQ,uBAAuB,EAAE,GAAG;EACpC,QAAQ,GAAG,EAAE,CAAC;EACd,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,YAAY,EAAE,IAAI;EAC5B,UAAU,IAAI,EAAE,GAAG;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI;EACd,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,QAAQ,EAAE,OAAO;EAC7B,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,YAAY,UAAU,EAAE,GAAG;EAC3B,YAAY,cAAc,EAAE,IAAI;EAChC,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,iBAAiB,EAAE,CAAC;EAC1B,MAAM,uBAAuB,EAAE,IAAI;EACnC,MAAM,eAAe,EAAE,QAAQ;EAC/B,MAAM,qBAAqB,EAAE,QAAQ;EACrC,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,KAAK;EACpB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3D;EACA,IAAI,MAAM,GAAG,GAAG,MAAM;EACtB,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI;EAC1B,QAAQ,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG;EAC3B,YAAY,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC;EACnD,YAAY,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC;EAClD,OAAO,CAAC;AACR;EACA,MAAM,KAAK,CAAC,SAAS,CAAC;EACtB,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,IAAI;EAChB,WAAW;EACX,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK,CAAC;AACN;EACA,IAAI,UAAU,CAAC,MAAM;EACrB,MAAM,GAAG,EAAE,CAAC;EACZ,KAAK,EAAE,CAAC,CAAC,CAAC;EACV,IAAI,WAAW,CAAC,MAAM;EACtB,MAAM,GAAG,EAAE,CAAC;EACZ,KAAK,EAAE,IAAI,CAAC,CAAC;EACb,GAAG;EACH,CAAC;;ECnGD,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;AAChF;EACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,OAAO,CAAC;AACZ;AACA,8DAA8D,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACpG;AACA,aAAa,CAAC,CAAC;EACf,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,QAAQ,GAAG;EACnB,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,IAAI,GAAG;EACP,GAAG,CAAC;EACJ,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC7E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC3B,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,kCAAkC;EAChD,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,QAAQ,IAAI,EAAE,QAAQ;EACtB,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,QAAQ;EACtB,QAAQ,SAAS,EAAE;EACnB,UAAU,MAAM,EAAE,IAAI;EACtB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,MAAM;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,CAAC,EAAE,EAAE;EACb,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,QAAQ,EAAE;EAChB,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,cAAc,EAAE,IAAI;EAC9B,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EACzE,cAAc,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE;EACpD,cAAc,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;EACzD,cAAc,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;EACvD,aAAa,CAAC;EACd,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;EAC3E,gBAAgB,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;EACzD,gBAAgB,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE;EAC3D,gBAAgB,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,EAAE;EACtD,eAAe,CAAC;EAChB,aAAa;EACb,WAAW;EACX,UAAU,IAAI;EACd,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3D;EACA,IAAI,MAAM,QAAQ,GAAG,CAAC,CAAC;EACvB,IAAI,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,IAAI;EAChC,MAAM,KAAK,CAAC,cAAc,CAAC;EAC3B,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;EAC1E,QAAQ,QAAQ;EAChB,UAAU,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,GAAG,QAAQ,GAAG,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;EAC9E,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;EACH,CAAC;;EChJD,MAAM,qBAAqB,GAAG,MAAM;EACpC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,sCAAsC;EAC1C,GAAG,CAAC;EACJ,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,KAAK;EACT,IAAI,MAAM;EACV,IAAI,MAAM;EACV,IAAI,QAAQ;EACZ,IAAI,WAAW;EACf,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,UAAU;EACd,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,UAAU,EAAE;EACtB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,eAAe,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EACvD,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,WAAW;EACX,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,OAAO,EAAE;EACjB,UAAU,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACnC,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EACjC,WAAW;EACX,UAAU,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;EACjC,UAAU,WAAW,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;EACrC,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,WAAW,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EACjD,UAAU,WAAW,EAAE,CAAC;EACxB,SAAS;AACT;EACA,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,QAAQ,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAChD,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,GAAG,EAAE,EAAE;EACf,QAAQ,IAAI,EAAE,CAAC,aAAa,EAAE,eAAe,EAAE,qBAAqB,CAAC;EACrE,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,YAAY,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACjD,WAAW;EACX,UAAU,WAAW,EAAE;EACvB,YAAY,IAAI,EAAE,QAAQ;EAC1B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC5C,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,GAAG,EAAE,CAAC;EAChB,UAAU,GAAG,EAAE,GAAG;EAClB,UAAU,QAAQ,EAAE,EAAE;EACtB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,YAAY,SAAS,EAAE,YAAY;EACnC,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,GAAG,EAAE,CAAC;EAChB,UAAU,GAAG,EAAE,EAAE;EACjB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,YAAY,SAAS,EAAE,YAAY;EACnC,WAAW;AACX;EACA,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,aAAa;EAC7B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE;EAChB,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;EAC/E,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,eAAe;EAC/B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE;EAChB,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG;EAC/E,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,qBAAqB;EACrC,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,IAAI,EAAE;EAChB,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;EAC5E,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,UAAU,EAAE,EAAE;EACxB,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECzLD,MAAM,qBAAqB,GAAG,MAAM;EACpC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,qCAAqC;EACzC,GAAG,CAAC;EACJ,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,IAAI,YAAY;EAChB,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,CAAC,aAAa,EAAE,QAAQ,CAAC;EACvC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB;EACA,QAAQ,SAAS,EAAE,MAAM,IAAI;EAC7B,UAAU,IAAI,GAAG,CAAC;EAClB,UAAU,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,GAAG,EAAE;EACvC,YAAY,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5B,WAAW,MAAM;EACjB,YAAY,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5B,WAAW;EACX,UAAU,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,KAAK;AACjE,YAAY,GAAG,CAAC,UAAU;AAC1B,WAAW,GAAG,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;EAC5B,SAAS;EACT,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,QAAQ;EACxB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EAClE,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,WAAW,EAAE,IAAI;EACzB,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,GAAG,EAAE,GAAG;EAChB,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,cAAc,EAAE,aAAa;EACzC,YAAY,KAAK,EAAE,aAAa;EAChC,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,cAAc,EAAE,aAAa;EAC3C,cAAc,KAAK,EAAE,aAAa;EAClC,aAAa;EACb,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC9E,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,QAAQ,EAAE,KAAK;EAC3B,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACxE,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,aAAa;EAC7B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,QAAQ,EAAE,QAAQ;EAC9B,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACvE,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;EACnE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC9ID,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;EAChF,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,KAAK;EACT,IAAI,MAAM;EACV,IAAI,MAAM;EACV,IAAI,QAAQ;EACZ,IAAI,WAAW;EACf,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,UAAU;EACd,GAAG,CAAC;EACJ,EAAE,MAAM,OAAO,GAAG,EAAE,CAAC;AACrB;EACA,EAAE,MAAM,aAAa,GAAG,GAAG,IAAI;EAC/B,IAAI,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK;EACjD,MAAM,OAAO;EACb,QAAQ,GAAG,GAAG;EACd,QAAQ,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM;EAC/C,UAAU,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC;EAC7B,UAAU,KAAK;EACf,SAAS,CAAC,CAAC;EACX,OAAO,CAAC;EACR,KAAK,EAAE,EAAE,CAAC,CAAC;EACX,GAAG,CAAC;AACJ;EACA,EAAE,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC;EACjC,IAAI,IAAI,EAAE;EACV,MAAM,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK;EACvE,MAAM,OAAO,EAAE,MAAM,EAAE,KAAK;EAC5B,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK;EAC1E,MAAM,OAAO,EAAE,KAAK,EAAE,OAAO;EAC7B,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;EAC5E,MAAM,OAAO,EAAE,MAAM,EAAE,OAAO;EAC9B,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK;EAC9E,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO;EAC/B,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;EAC7E,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO;EAC/B,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM;EACxE,MAAM,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;EACtC,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM;EAC/E,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO;EAC/B,KAAK;EACL,GAAG,CAAC,CAAC;AACL;EACA,EAAE,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC;EACjC,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;EAC3E,MAAM,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM;EACvC,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC5E,MAAM,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;EACzC,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC3E,MAAM,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;EAC1C,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC5E,MAAM,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;EAC1C,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EACxE,MAAM,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;EAC1C,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC1E,MAAM,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;EAC1C,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;EAC9E,MAAM,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;EAC1C,KAAK;EACL,GAAG,CAAC,CAAC;AACL;EACA,EAAE,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC;EACjC,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC5E,MAAM,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO;EACtC,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC5E,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EACxC,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC5E,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EACxC,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC5E,MAAM,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;EACzC,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC5E,MAAM,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;EACzC,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC7E,MAAM,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;EAC1C,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO;EAC7E,MAAM,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO;EAC3C,KAAK;EACL,GAAG,CAAC,CAAC;AACL;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,UAAU,EAAE;EAClB,QAAQ,QAAQ,EAAE;EAClB,UAAU,QAAQ,EAAE,UAAU;EAC9B,UAAU,QAAQ,EAAE,KAAK;EACzB,UAAU,YAAY,EAAE,IAAI;EAC5B,UAAU,IAAI,EAAE;EAChB,YAAY,YAAY;EACxB,YAAY,YAAY;EACxB,YAAY,YAAY;EACxB,YAAY,YAAY;EACxB,YAAY,YAAY;EACxB,YAAY,YAAY;EACxB,YAAY,YAAY;EACxB,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,SAAS,EAAE,CAAC,IAAI;EAC5B,cAAc,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;EAC/C,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC;EACxC,WAAW;EACX,UAAU,eAAe,EAAE;EAC3B,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,UAAU,EAAE,CAAC;EACzB,YAAY,aAAa,EAAE,CAAC;EAC5B,YAAY,aAAa,EAAE,CAAC;EAC5B,WAAW;EACX,UAAU,YAAY,EAAE;EACxB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,WAAW;EACX,SAAS;EACT,QAAQ,KAAK,EAAE;EACf,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,SAAS;EACT,QAAQ,OAAO,EAAE;EACjB,UAAU,OAAO,EAAE,MAAM;EACzB,UAAU,WAAW,EAAE;EACvB,YAAY,IAAI,EAAE,QAAQ;EAC1B,WAAW;EACX,UAAU,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EAC1B,UAAU,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACxD,UAAU,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC/C,UAAU,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC/D,UAAU,WAAW,EAAE,CAAC;EACxB,UAAU,kBAAkB,EAAE,CAAC;EAC/B,UAAU,SAAS,EAAE,gBAAgB;EACrC,SAAS;EACT,QAAQ,MAAM,EAAE;EAChB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,IAAI,EAAE,CAAC,kBAAkB,EAAE,oBAAoB,EAAE,mBAAmB,CAAC;EAC/E,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,SAAS;EACT,QAAQ,UAAU,EAAE,IAAI;EACxB,QAAQ,KAAK,EAAE;EACf,UAAU;EACV,YAAY,IAAI,EAAE,UAAU;EAC5B,YAAY,IAAI,EAAE,MAAM;EACxB,YAAY,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACtC,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACjD,aAAa;EACb,YAAY,QAAQ,EAAE;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACnD,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ,KAAK,EAAE;EACf,UAAU;EACV,YAAY,IAAI,EAAE,OAAO;EACzB,YAAY,SAAS,EAAE;EACvB,cAAc,SAAS,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;EACpD,cAAc,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACjD,aAAa;EACb,YAAY,SAAS,EAAE;EACvB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC/C,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,IAAI,EAAE,kBAAkB;EACpC,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,cAAc,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3C,aAAa;EACb,WAAW;EACX,UAAU;EACV,YAAY,IAAI,EAAE,oBAAoB;EACtC,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACrC,cAAc,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3C,aAAa;EACb,WAAW;EACX,UAAU;EACV,YAAY,IAAI,EAAE,mBAAmB;EACrC,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,cAAc,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC3C,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ,IAAI,EAAE;EACd,UAAU,GAAG,EAAE,KAAK;EACpB,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,IAAI,EAAE,CAAC;EACjB,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,YAAY,EAAE,IAAI;EAC5B,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ;EACR,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACjC,UAAU,MAAM,EAAE;EAClB,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACjC,UAAU,MAAM,EAAE;EAClB,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACjC,UAAU,MAAM,EAAE;EAClB,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACjC,UAAU,MAAM,EAAE;EAClB,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACjC,UAAU,MAAM,EAAE;EAClB,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACjC,UAAU,MAAM,EAAE;EAClB,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACjC,UAAU,MAAM,EAAE;EAClB,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;EAC5C,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECzTD,MAAM,yBAAyB,GAAG,MAAM;EACxC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,yCAAyC;EAC7C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;EACjD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;EACpD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EAChD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EACnD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;EAClD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,CAAC;EACpD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;EACnD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EACnD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EACnD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,CAAC;EACjD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC;EACjD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC;EAClD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;EACnD,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACnD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACpD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,IAAI,CAAC,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC;EACrD,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,OAAO,EAAE;EACjB,UAAU,QAAQ,EAAE;EACpB,YAAY,UAAU,EAAE,KAAK;EAC7B,WAAW;EACX,UAAU,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;EACjC,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,WAAW,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EACjD,UAAU,WAAW,EAAE,CAAC;EACxB,SAAS;AACT;EACA,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,QAAQ,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAChD,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,QAAQ,EAAE;EAChB,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,KAAK,EAAE,CAAC;EAClB,UAAU,GAAG,EAAE,GAAG;EAClB,UAAU,YAAY,EAAE,EAAE;EAC1B,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;EACvC,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,QAAQ,WAAW,EAAE,EAAE;EACvB,QAAQ,GAAG,EAAE,SAAS;EACtB,QAAQ,GAAG,EAAE,SAAS;EACtB,QAAQ,WAAW,EAAE,IAAI;EACzB,QAAQ,WAAW,EAAE;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,QAAQ;EAC1B,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,SAAS,EAAE,KAAK;EAC1B,YAAY,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EAC9D,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,UAAU,EAAE,GAAG;EACzB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,WAAW,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACpC,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,YAAY,IAAI,EAAE,QAAQ;EAC1B,WAAW;EACX,SAAS;EACT,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,UAAU,EAAE,GAAG;EACzB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,aAAa;EAC7B,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC/C,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;EACvC,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,YAAY,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC7C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC/KD,MAAM,yBAAyB,GAAG,MAAM;EACxC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACnE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,yCAAyC;EAC7C,GAAG,CAAC;EACJ,EAAE,MAAM,SAAS,GAAG;EACpB,IAAI,QAAQ,CAAC,SAAS,CAAC;EACvB,IAAI,QAAQ,CAAC,MAAM,CAAC;EACpB,IAAI,QAAQ,CAAC,qBAAqB,CAAC;EACnC,IAAI,QAAQ,CAAC,SAAS,CAAC;EACvB,GAAG,CAAC;AACJ;EACA;EACA,EAAE,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE,IAAI,KAAK;EAC1C,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC;EACtB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;EACxD,MAAM,IAAI,CAAC,GAAG,QAAQ,EAAE;EACxB,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;EACzB,QAAQ,SAAS;EACjB,OAAO;EACP,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC;EAClB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,IAAI,CAAC,EAAE;EAC5C,QAAQ,GAAG,IAAI,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC9B,OAAO;EACP,MAAM,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/C,KAAK;EACL,IAAI,OAAO,MAAM,CAAC;EAClB,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI;EACzC,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC;EAC7C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACnD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACpD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,CAAC;EACnD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,CAAC;EACvD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACrD,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC;EACtD,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACvC;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,SAAS,EAAE,KAAK;EACtB,MAAM,KAAK,EAAE,SAAS;EACtB,MAAM,MAAM,EAAE;EACd,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,IAAI,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,CAAC;EACtC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,OAAO;EACP,MAAM,WAAW,EAAE;EACnB,QAAQ,IAAI,EAAE;EACd,UAAU;EACV,YAAY,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAC9B,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,QAAQ,EAAE;EAChB,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5B,UAAU,QAAQ,EAAE,KAAK;EACzB,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,GAAG,EAAE,EAAE;EACjB,UAAU,GAAG,EAAE,EAAE;EACjB,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,UAAU;EACpB,YAAY,yLAAyL;EACrM,UAAU,UAAU,EAAE,MAAM;EAC5B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAC5B,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,GAAG,EAAE,EAAE;EACjB,UAAU,GAAG,EAAE,EAAE;EACjB,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE;EACzD,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,YAAY,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EACpE,WAAW;EACX,UAAU,GAAG,EAAE,SAAS;EACxB,UAAU,GAAG,EAAE,SAAS;EACxB,UAAU,WAAW,EAAE;EACvB,YAAY,IAAI,EAAE,IAAI;EACtB,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,SAAS,EAAE,CAAC;EACtB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,IAAI;EACrB,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACpC,UAAU,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACpC,UAAU,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACnC,UAAU,QAAQ,EAAE,EAAE,SAAS,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;EACpD,UAAU,WAAW,EAAE,EAAE;EACzB,UAAU,GAAG,EAAE,SAAS;EACxB,UAAU,GAAG,EAAE,SAAS;EACxB,UAAU,WAAW,EAAE;EACvB,YAAY,IAAI,EAAE,QAAQ;EAC1B,YAAY,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,YAAY,cAAc,EAAE,IAAI;EAChC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,KAAK,EAAE,IAAI;EACrB,UAAU,WAAW,EAAE,CAAC;EACxB,UAAU,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACnC,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACnC,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,KAAK,EAAE,IAAI;EACrB,UAAU,SAAS,EAAE,CAAC;EACtB,UAAU,WAAW,EAAE,CAAC;EACxB,UAAU,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACpC,UAAU,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACnC,UAAU,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACnC,UAAU,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACpC,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ;EACR,UAAU,IAAI,EAAE,CAAC;EACjB,UAAU,KAAK,EAAE,EAAE;EACnB;EACA,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,MAAM,EAAE,GAAG;EACrB,UAAU,YAAY,EAAE,IAAI;EAC5B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,EAAE;EAClB,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,GAAG,EAAE,GAAG;EAClB,UAAU,YAAY,EAAE,IAAI;EAC5B,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,aAAa;EACb,WAAW;EACX,UAAU,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC;EACzC,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,aAAa;EAC7B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI;EACd,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,YAAY,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC;EACpC,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,YAAY,EAAE,QAAQ,CAAC,MAAM,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,MAAM,EAAE,IAAI;EACtB,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,CAAC;EACpB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECjRD,MAAM,uBAAuB,GAAG,MAAM;EACtC,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;EAC5E,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAE,KAAK,EAAE,QAAQ,EAAE;EACrD,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,qCAAqC,EAAE,KAAK,EAAE,OAAO,EAAE;EACnE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,EAAE;EAC9C,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,UAAU,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,wBAAwB,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvD,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAE,KAAK,EAAE,QAAQ,EAAE;EACrD,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE;EAC/C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,SAAS,EAAE;EACjD,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,KAAK,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,SAAS,EAAE;EAChD,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,SAAS,EAAE;EACpD,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE;EACpC,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,MAAM,EAAE;EAC/C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC/C,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE;EAC/C,IAAI,EAAE,IAAI,EAAE,mBAAmB,EAAE,KAAK,EAAE,OAAO,EAAE;EACjD,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE;EACpC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,UAAU,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,SAAS,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,kBAAkB,EAAE,KAAK,EAAE,QAAQ,EAAE;EACjD,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE;EAC9C,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,EAAE;EAC9C,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,OAAO,EAAE;EAC/C,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,QAAQ,EAAE;EACnD,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,qBAAqB,EAAE,KAAK,EAAE,QAAQ,EAAE;EACpD,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,UAAU,EAAE;EAChD,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,MAAM,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,SAAS,EAAE;EAC9C,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE;EAC1C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG,OAAO,CAAC;AACxB;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,MAAM;EACzB,UAAU,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,EAAE;AACrD,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK;AACvC,YAAY,GAAG;AACf,YAAY,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACzB,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,OAAO,EAAE;EACjB,UAAU,OAAO,EAAE,EAAE;EACrB,SAAS;EACT,OAAO;EACP,MAAM,SAAS,EAAE;EACjB,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,GAAG,EAAE,GAAG;EAChB,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,OAAO,EAAE;EACjB,UAAU,KAAK,EAAE;EACjB,YAAY,QAAQ,CAAC,SAAS,CAAC;EAC/B,YAAY,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC/C,YAAY,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC/C,YAAY,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC/C,YAAY,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC/C,WAAW,CAAC,OAAO,EAAE;EACrB,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,GAAG,EAAE,OAAO;EACtB,UAAU,IAAI;EACd,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,UAAU,EAAE;EACtB,YAAY,GAAG,EAAE,CAAC;EAClB,YAAY,GAAG,EAAE,CAAC;EAClB,WAAW;EACX,UAAU,IAAI,EAAE,CAAC;EACjB,UAAU,KAAK,EAAE,CAAC;EAClB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EACjD,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE;EACnB,cAAc,IAAI,EAAE,KAAK;EACzB,aAAa;EACb,YAAY,SAAS,EAAE;EACvB,cAAc,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3D;EACA,IAAI,QAAQ;EACZ,OAAO,aAAa,CAAC,+BAA+B,CAAC;EACrD,QAAQ,gBAAgB,CAAC,OAAO,EAAE,MAAM;EACxC,QAAQ,KAAK,CAAC,cAAc,CAAC;EAC7B,UAAU,IAAI,EAAE,SAAS;EACzB,SAAS,CAAC,CAAC;EACX,OAAO,CAAC,CAAC;EACT,GAAG;EACH,CAAC;;ECvQD,MAAM,UAAU,GAAG,MAAM;EACzB,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,yBAAyB,CAAC,CAAC;AACrE;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,sBAAsB,EAAE,KAAK,EAAE,MAAM,EAAE;EACnD,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE;EACpC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE;EAC9C,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE;EACrC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,OAAO,EAAE;EAC5C,IAAI,EAAE,IAAI,EAAE,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE;EAC9C,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,MAAM,EAAE;EAC3C,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE;EACpC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE;EACxC,IAAI,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE;EAC1C,IAAI,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE;EAC7C,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE;EACzC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE;EACtC,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE;EAC3C,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,SAAS,EAAE,MAAM,IAAI;EAC7B,UAAU,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;EAC/E,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,OAAO,EAAE;EACjB,UAAU,OAAO,EAAE,EAAE;EACrB,SAAS;EACT,OAAO;EACP,MAAM,SAAS,EAAE;EACjB,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,GAAG,EAAE,MAAM;EACnB,QAAQ,GAAG,EAAE,QAAQ;EACrB,QAAQ,OAAO,EAAE;EACjB,UAAU,KAAK,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;EACxD,SAAS;EACT,QAAQ,IAAI,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EAC7B,QAAQ,UAAU,EAAE,IAAI;EACxB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;EAC9C,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,EAAE;EAClB,UAAU,IAAI,EAAE,kBAAkB;EAClC,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,GAAG;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,UAAU,EAAE;EACtB,YAAY,GAAG,EAAE,CAAC;EAClB,YAAY,GAAG,EAAE,CAAC;EAClB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EACjD,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,KAAK,EAAE,MAAM;EACzB,WAAW;EACX,UAAU,GAAG,EAAE,KAAK;EACpB,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE;EACnB,cAAc,IAAI,EAAE,IAAI;EACxB,cAAc,KAAK,EAAE,MAAM;EAC3B,aAAa;AACb;EACA,YAAY,SAAS,EAAE;EACvB,cAAc,SAAS,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,aAAa;EACb,WAAW;EACX,UAAU,IAAI;EACd,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,IAAI,QAAQ,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;EAC7E,MAAM,KAAK,CAAC,cAAc,CAAC;EAC3B,QAAQ,IAAI,EAAE,SAAS;EACvB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;EACH,CAAC;;ECvID,MAAM,qBAAqB,GAAG,MAAM;EACpC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,qCAAqC;EACzC,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;AACT;EACA,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR;EACA,UAAU,IAAI,EAAE;EAChB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC;EACvB,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC;EACvB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;EACxB,WAAW;EACX,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACrC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECpGD,MAAM,eAAe,GAAG,MAAM;EAC9B,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,8BAA8B,CAAC,CAAC;AAC1E;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI;EACJ,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC;EAC9C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,IAAI,EAAE,EAAE,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC;EAC3C,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC;EAC3C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC;EAC9C,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;EAC5C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,KAAK,EAAE,EAAE,EAAE,QAAQ,EAAE,aAAa,EAAE,IAAI,CAAC;EAChD,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC;EACjD,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC5C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC9C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,IAAI,CAAC;EACrD,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;EACrD,KAAK;EACL,IAAI;EACJ,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC;EAChD,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC;EAC9C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC;EAC9C,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,IAAI,CAAC;EAClD,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,IAAI,CAAC;EACjD,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC5C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC7C,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC;EAC/C,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,IAAI,CAAC;EACrD,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,eAAe,EAAE,IAAI,CAAC;EACrD,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,uCAAuC;EACrD,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,UAAU,UAAU,EAAE,GAAG;EACzB,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC;EAC9B,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,SAAS,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC;EAChD,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;AACT;EACA,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;AACT;EACA,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;EACvB,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,UAAU,EAAE,KAAK,IAAI;EAC/B,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;EAC7C,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,YAAY,KAAK,EAAE;EACnB,cAAc,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC/C,cAAc,IAAI,EAAE,IAAI;EACxB,cAAc,SAAS,EAAE,KAAK,IAAI;EAClC,gBAAgB,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EACrC,eAAe;EACf,cAAc,QAAQ,EAAE,KAAK;EAC7B,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACtD,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;EACvB,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,UAAU,EAAE,KAAK,IAAI;EAC/B,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;EAC7C,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,YAAY,KAAK,EAAE;EACnB,cAAc,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACjD,cAAc,IAAI,EAAE,IAAI;EACxB,cAAc,SAAS,EAAE,KAAK,IAAI;EAClC,gBAAgB,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EACrC,eAAe;EACf,cAAc,QAAQ,EAAE,KAAK;EAC7B,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACtD,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,KAAK,EAAE,EAAE;EACjB,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC5JD,MAAM,uBAAuB,GAAG,MAAM;EACtC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,uCAAuC;EAC3C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,OAAO,GAAG;EAClB,IAAI;EACJ,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC;EACnB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,KAAK;EACL,IAAI;EACJ,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC;EAChB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,KAAK;EACL,IAAI;EACJ,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC;EACnB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,KAAK;EACL,IAAI;EACJ,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC;EAClB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC;EACjB,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG,OAAO;EACvB,IAAI,SAAS,EAAE;EACf,MAAM,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACzC,KAAK;EACL,IAAI,QAAQ,EAAE;EACd,MAAM,IAAI,EAAE,IAAI;EAChB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EACtC,OAAO;EACP,KAAK;AACL;EACA,IAAI,SAAS,EAAE;EACf,MAAM,IAAI,EAAE,IAAI;EAChB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EACvC,OAAO;EACP,KAAK;EACL,GAAG,CAAC,CAAC;AACL;EACA,EAAE,MAAM,KAAK,GAAG,OAAO;EACvB,IAAI,SAAS,EAAE;EACf,MAAM,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACzC,KAAK;EACL,IAAI,SAAS,EAAE;EACf,MAAM,IAAI,EAAE,IAAI;EAChB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EACvC,OAAO;EACP,KAAK;AACL;EACA,IAAI,QAAQ,EAAE;EACd,MAAM,IAAI,EAAE,IAAI;EAChB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EACtC,OAAO;EACP,KAAK;EACL,GAAG,CAAC,CAAC;AACL;EACA,EAAE,MAAM,WAAW,GAAG;EACtB,IAAI,SAAS,EAAE,KAAK;EACpB,IAAI,KAAK,EAAE;EACX,MAAM,SAAS,EAAE,iBAAiB;EAClC,MAAM,KAAK,EAAE,OAAO;EACpB,MAAM,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EACvC,MAAM,UAAU,EAAE,GAAG;EACrB,KAAK;EACL,IAAI,SAAS,EAAE;EACf,MAAM,IAAI,EAAE,OAAO;EACnB,KAAK;EACL,IAAI,OAAO,EAAE;EACb,MAAM,SAAS,EAAE,iBAAiB;EAClC,KAAK;EACL,IAAI,IAAI,EAAE;EACV,MAAM;EACN,QAAQ;EACR,UAAU,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EACvB,UAAU,MAAM,EAAE,MAAM;EACxB,SAAS;EACT,QAAQ;EACR,UAAU,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC;EACzB,UAAU,MAAM,EAAE,MAAM;EACxB,SAAS;EACT,OAAO;EACP,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,QAAQ,GAAG;EACnB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;EAC3D,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;EAC5D,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;EAC7D,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;EAC9D,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,UAAU,GAAG;EACrB,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE;EACnD,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;EACpD,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE;EACvD,IAAI,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;EACpD,GAAG,CAAC;EACJ,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,QAAQ,CAAC;EAC1B,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,kBAAkB;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,oBAAoB;EAClC,QAAQ,IAAI,EAAE,QAAQ;EACtB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG,UAAU,GAAG,QAAQ;EAC3D,MAAM,KAAK,EAAE;EACb,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE;EACrD,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE;EACrD,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE;EACrD,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE;EACrD,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE;EACrD,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE;EACrD,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE;EACrD,QAAQ,EAAE,SAAS,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE;EACrD,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,GAAG;EACnB,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;EAC1B,UAAU,QAAQ,EAAE,WAAW;EAC/B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;EAC1B,UAAU,QAAQ,EAAE,WAAW;EAC/B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;EAC1B,UAAU,QAAQ,EAAE,WAAW;EAC/B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC;EAC1B,UAAU,QAAQ,EAAE,WAAW;EAC/B,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,MAAM,iBAAiB,GAAG;EAC9B,MAAM,EAAE,EAAE;EACV,QAAQ,IAAI,EAAE,UAAU;EACxB,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,IAAI,EAAE,QAAQ;EACtB,OAAO;EACP,KAAK,CAAC;EACN,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;EAC9E,GAAG;EACH,CAAC;;ECrOD,MAAM,0BAA0B,GAAG,MAAM;EACzC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACtE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,2CAA2C;EAC/C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG;EAChB,IAAI,MAAM;EACV,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,MAAM;EACV,IAAI,MAAM;EACV,IAAI,MAAM;EACV,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,MAAM;EACV,IAAI,MAAM;EACV,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,IAAI,UAAU;EACd,IAAI,WAAW;EACf,IAAI,SAAS;EACb,IAAI,QAAQ;EACZ,IAAI,QAAQ;EACZ,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC;EAClB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EACjC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;EACpC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAChD,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,QAAQ,EAAE,KAAK;EACvB,QAAQ,SAAS,EAAE,MAAM,IAAI;EAC7B,UAAU,OAAO,CAAC;AAClB,YAAY,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACpC,YAAY,EAAE,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC1D,UAAU,CAAC,CAAC;EACZ,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,MAAM,EAAE,EAAE;EACpB,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,YAAY;EAC5B,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,UAAU,EAAE,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;EACvC,UAAU,IAAI;EACd,UAAU,cAAc,EAAE,GAAG,IAAI;EACjC,YAAY,OAAO,GAAG,GAAG,CAAC,CAAC;EAC3B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,EAAE;EACjB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECnID,MAAM,YAAY,GAAG,MAAM;EAC3B,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;AACvE;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK;EACzD,UAAU,KAAK,EAAE;EACjB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,IAAI;EACzB,cAAc,IAAI,EAAE,UAAU;EAC9B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,IAAI;EACzB,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACzC,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACvC,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,UAAU;EAC9B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,QAAQ;EAC5B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,UAAU,EAAE,EAAE;EAC5B,cAAc,aAAa,EAAE,CAAC;EAC9B,cAAc,WAAW,EAAE,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,EAAE,GAAG,CAAC;EACrE,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,iBAAiB,GAAG;EAC9B,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,MAAM,EAAE,KAAK;EACzB,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,MAAM,EAAE,KAAK;EACzB,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;EAC9E,GAAG;EACH,CAAC;;ECrGD,MAAM,iBAAiB,GAAG,MAAM;EAChC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;AAC5E;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,iBAAiB,EAAE,KAAK;EAClC,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,QAAQ,EAAE,QAAQ;EAC9B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,IAAI;EACzB,cAAc,IAAI,EAAE,UAAU;EAC9B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,IAAI;EACzB,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACzC,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACvC,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,UAAU;EAC9B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,QAAQ;EAC5B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECjFD,MAAM,wBAAwB,GAAG,MAAM;EACvC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,wCAAwC;EAC5C,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,MAAM,EAAE;EACd,QAAQ,MAAM,EAAE,UAAU;EAC1B,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;EAC3E,UAAU,iBAAiB,EAAE,KAAK;EAClC,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,EAAE;EAC5B,YAAY,WAAW,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,QAAQ,EAAE,QAAQ;EAC9B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,IAAI;EACzB,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,IAAI;EACzB,cAAc,IAAI,EAAE,OAAO;EAC3B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACzC,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACvC,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,UAAU;EAC9B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,iBAAiB,GAAG;EAC9B,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAClC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAClC,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;EAC9E,GAAG;EACH,CAAC;;EC3GD,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,oCAAoC,CAAC,CAAC;EAChF,EAAE,MAAM,KAAK,GAAG;EAChB,IAAI;EACJ,MAAM,KAAK,EAAE,IAAI;EACjB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACjC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,OAAO;EACnB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC;EACpC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EAC/B,OAAO;EACP,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG;EAChB,IAAI;EACJ,MAAM,KAAK,EAAE,IAAI;EACjB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACjC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EAC/B,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,QAAQ;EACpB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,oBAAoB;EACpC,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,SAAS;EACT,OAAO;AACP;EACA,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;AACP;EACA,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK;EACzD,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK;EACzD,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,iBAAiB,EAAE,KAAK;EAClC,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,iBAAiB,GAAG;EAC9B,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;EACtD,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;EACtD,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;EAC9E,GAAG;EACH,CAAC;;ECnJD,MAAM,sBAAsB,GAAG,MAAM;EACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,uCAAuC;EAC3C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAClD,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,aAAa;EACzB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACjC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,OAAO;EACnB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC;EACpC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,kBAAkB;EAC9B,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAClD,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EAC/B,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,cAAc;EAC1B,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAClD,OAAO;EACP,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,uBAAuB;EACvC,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,OAAO,EAAE,sBAAsB;EACzC,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,GAAG,EAAE,KAAK;EACpB,UAAU,SAAS,EAAE,QAAQ;EAC7B,UAAU,YAAY,EAAE;EACxB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,SAAS;EACT,OAAO;AACP;EACA,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,QAAQ,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE;EAC/B,UAAU,IAAI,MAAM,CAAC,UAAU,IAAI,GAAG,EAAE;EACxC,YAAY,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;EACvD,YAAY,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;EAC7D,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EACzE,YAAY,OAAO,GAAG,CAAC;EACvB,WAAW;EACX,UAAU,OAAO,IAAI,CAAC;EACtB,SAAS;EACT,OAAO;AACP;EACA,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK;EACzD,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,IAAI;EACd,UAAU,KAAK,EAAE;EACjB,YAAY,QAAQ,EAAE,OAAO;EAC7B,YAAY,OAAO,EAAE,WAAW;EAChC,YAAY,WAAW,EAAE,CAAC;EAC1B,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,IAAI;EACrB,UAAU,GAAG,EAAE,CAAC;EAChB,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,iBAAiB,GAAG;EAC9B,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,MAAM,EAAE,KAAK;EACzB,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,MAAM,EAAE,KAAK;EACzB,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;EAC9E,GAAG;EACH,CAAC;;EC5JD,MAAM,qBAAqB,GAAG,MAAM;EACpC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,sCAAsC;EAC1C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAClD,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,aAAa;EACzB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACjC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,OAAO;EACnB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC;EACpC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,kBAAkB;EAC9B,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAClD,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAClC,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,UAAU;EACtB,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EAC/B,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,KAAK,EAAE,GAAG;EAChB,MAAM,IAAI,EAAE,cAAc;EAC1B,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAClD,OAAO;EACP,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,sBAAsB;EACtC,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,OAAO,EAAE,iBAAiB;EACpC,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,GAAG,EAAE,KAAK;EACpB,UAAU,SAAS,EAAE,QAAQ;EAC7B,UAAU,YAAY,EAAE;EACxB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,SAAS;EACT,OAAO;AACP;EACA,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,QAAQ,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE;EAC/B,UAAU,IAAI,MAAM,CAAC,UAAU,IAAI,GAAG,EAAE;EACxC,YAAY,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;EACvD,YAAY,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;EAC7D,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EACzE,YAAY,OAAO,GAAG,CAAC;EACvB,WAAW;EACX,UAAU,OAAO,IAAI,CAAC;EACtB,SAAS;EACT,OAAO;AACP;EACA,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK;EACzD,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,IAAI;EACd,UAAU,KAAK,EAAE;EACjB,YAAY,QAAQ,EAAE,OAAO;EAC7B,YAAY,OAAO,EAAE,MAAM;EAC3B,YAAY,MAAM,EAAE,EAAE;EACtB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,KAAK,EAAE,IAAI;EACrB,UAAU,GAAG,EAAE,CAAC;EAChB,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,iBAAiB,GAAG;EAC9B,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,MAAM,EAAE,KAAK;EACzB,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,MAAM,EAAE,KAAK;EACzB,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;EAC9E,GAAG;EACH,CAAC;;EC5JD,MAAM,mBAAmB,GAAG,MAAM;EAClC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,mCAAmC,CAAC,CAAC;AAC/E;EACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,OAAO,CAAC;AACZ;AACA;AACA,wDAAwD,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC1E,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAChD;AACA;AACA,IAAI,CAAC,CAAC;EACN,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE,MAAM;EACpB,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC/C,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,SAAS,EAAE,SAAS;EAChC,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,EAAE;EACvB,cAAc,IAAI,EAAE,OAAO;EAC3B,cAAc,MAAM,EAAE;EACtB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EACjD,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECjED,MAAM,sBAAsB,GAAG,MAAM;EACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,sCAAsC;EAC1C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,OAAO,CAAC;AACZ;AACA;AACA,wDAAwD,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC1E,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAChD;AACA;AACA,IAAI,CAAC,CAAC;EACN,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,MAAM,EAAE,MAAM;EACxB,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,KAAK,EAAE,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACrC,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,YAAY,WAAW,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC5D,YAAY,UAAU,EAAE,EAAE;EAC1B,YAAY,aAAa,EAAE,CAAC;EAC5B,YAAY,aAAa,EAAE,CAAC;EAC5B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;EACpD,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACjD,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE,IAAI;EAC3B,YAAY,IAAI,EAAE,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACrC,aAAa;EACb,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,cAAc,EAAE,IAAI;EAChC,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;EACpC,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,EAAE;EACvB,cAAc,MAAM,EAAE;EACtB,gBAAgB,QAAQ,EAAE,EAAE;EAC5B,gBAAgB,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACnD,gBAAgB,YAAY,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;EACxC,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECzGD,MAAM,kBAAkB,GAAG,MAAM;EACjC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,kCAAkC,CAAC,CAAC;AAC9E;EACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,OAAO,CAAC;AACZ;AACA;AACA,wDAAwD,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC1E,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAChD;AACA;AACA,IAAI,CAAC,CAAC;EACN,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,MAAM,EAAE,MAAM;EACxB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,QAAQ,EAAE,CAAC,GAAG;EACxB,UAAU,OAAO,EAAE;EACnB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,OAAO,EAAE,KAAK;EAC1B,YAAY,QAAQ,EAAE,IAAI;EAC1B,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,WAAW,EAAE,CAAC;EAC5B,cAAc,WAAW,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACvD,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;EACpD,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,QAAQ,EAAE,CAAC;EACvB,YAAY,MAAM,EAAE,EAAE;EACtB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,QAAQ,EAAE,EAAE;EACxB,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,EAAE;EACvB,cAAc,KAAK,EAAE;EACrB,gBAAgB,YAAY,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAC1C,eAAe;EACf,cAAc,MAAM,EAAE;EACtB,gBAAgB,YAAY,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC;EAC1C,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,QAAQ,EAAE,EAAE;EACxB,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,KAAK,EAAE,EAAE;EACrB,YAAY,MAAM,EAAE,EAAE;EACtB,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,KAAK,EAAE,MAAM;EACzB,YAAY,SAAS,EAAE,UAAU;EACjC,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EClGD,MAAM,uBAAuB,GAAG,MAAM;EACtC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,uCAAuC;EAC3C,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,QAAQ,EAAE,CAAC,GAAG;EACxB,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,OAAO,EAAE;EACnB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,OAAO,EAAE,KAAK;EAC1B,YAAY,QAAQ,EAAE,IAAI;EAC1B,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACrC,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;EACpD,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC;EACpB,UAAU,MAAM,EAAE;EAClB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,iBAAiB,EAAE,IAAI;EACjC,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,QAAQ,EAAE,CAAC,GAAG;EACxB,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,OAAO,EAAE;EACnB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,OAAO,EAAE,KAAK;EAC1B,YAAY,QAAQ,EAAE,IAAI;EAC1B,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;EACpD,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC;EACpB,UAAU,MAAM,EAAE;EAClB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,iBAAiB,EAAE,IAAI;EACjC,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,QAAQ,EAAE,CAAC,GAAG;EACxB,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,OAAO,EAAE;EACnB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,OAAO,EAAE,KAAK;EAC1B,YAAY,QAAQ,EAAE,IAAI;EAC1B,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;EACpD,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE,CAAC,EAAE,CAAC;EACpB,UAAU,MAAM,EAAE;EAClB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,iBAAiB,EAAE,IAAI;EACjC,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECpID,MAAM,wBAAwB,GAAG,MAAM;EACvC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,yCAAyC;EAC7C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,OAAO,CAAC;AACZ;AACA;AACA,wDAAwD,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC1E,UAAU,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAChD;AACA;AACA,IAAI,CAAC,CAAC;EACN,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,MAAM,EAAE,MAAM;EACxB,UAAU,MAAM,EAAE;EAClB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE,IAAI;EAC3B,YAAY,IAAI,EAAE,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,aAAa;EACb,WAAW;AACX;EACA,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,OAAO,EAAE,IAAI;EACzB,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,QAAQ,EAAE,IAAI;EAC1B,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;EACpD,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACjD,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,EAAE;EACvB,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE;EACrB,gBAAgB,YAAY,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EAC7C,eAAe;EACf,cAAc,MAAM,EAAE;EACtB,gBAAgB,YAAY,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EAC7C,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,EAAE;EACvB,cAAc,IAAI,EAAE,MAAM;EAC1B,cAAc,KAAK,EAAE;EACrB,gBAAgB,YAAY,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;EAC3C,eAAe;EACf,cAAc,MAAM,EAAE;EACtB,gBAAgB,YAAY,EAAE,CAAC,IAAI,EAAE,KAAK,CAAC;EAC3C,eAAe;AACf;EACA,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,EAAE;EACvB,cAAc,IAAI,EAAE,UAAU;EAC9B,cAAc,KAAK,EAAE;EACrB,gBAAgB,YAAY,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAC5C,eAAe;EACf,cAAc,MAAM,EAAE;EACtB,gBAAgB,YAAY,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAC5C,eAAe;AACf;EACA,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,KAAK,EAAE,EAAE;EACrB,YAAY,MAAM,EAAE,EAAE;EACtB,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,KAAK,EAAE,MAAM;EACzB,YAAY,eAAe,EAAE,MAAM;EACnC,YAAY,YAAY,EAAE,CAAC;EAC3B,YAAY,SAAS,EAAE,UAAU;EACjC,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECpID,MAAM,mBAAmB,GAAG,MAAM;EAClC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,mCAAmC,CAAC,CAAC;AAC/E;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,MAAM,EAAE,MAAM;EACxB,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,GAAG,EAAE,CAAC;EAChB,UAAU,GAAG,EAAE,CAAC;EAChB,UAAU,WAAW,EAAE,CAAC;EACxB,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,KAAK,EAAE;EACrB,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;EAC1C,gBAAgB,CAAC,GAAG,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;EAC1C,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;EACxC,gBAAgB,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC;EACxC,eAAe;EACf,aAAa;EACb,WAAW;EACX,UAAU,OAAO,EAAE;EACnB,YAAY,IAAI,EAAE,wCAAwC;EAC1D,YAAY,MAAM,EAAE,KAAK;EACzB,YAAY,KAAK,EAAE,EAAE;EACrB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;EACrC,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,MAAM;EAC3B,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,MAAM,EAAE,EAAE;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,MAAM;EAC3B,cAAc,KAAK,EAAE,CAAC;EACtB,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,MAAM,EAAE,EAAE;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,MAAM;EAC3B,cAAc,KAAK,EAAE,CAAC;EACtB,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,YAAY,QAAQ,EAAE,CAAC,EAAE;EACzB,YAAY,SAAS,EAAE,KAAK,IAAI;EAChC,cAAc,IAAI,KAAK,KAAK,KAAK,EAAE;EACnC,gBAAgB,OAAO,WAAW,CAAC;EACnC,eAAe;EACf,cAAc,IAAI,KAAK,KAAK,KAAK,EAAE;EACnC,gBAAgB,OAAO,MAAM,CAAC;EAC9B,eAAe;EACf,cAAc,IAAI,KAAK,KAAK,KAAK,EAAE;EACnC,gBAAgB,OAAO,MAAM,CAAC;EAC9B,eAAe;EACf,cAAc,IAAI,KAAK,KAAK,KAAK,EAAE;EACnC,gBAAgB,OAAO,KAAK,CAAC;EAC7B,eAAe;EACf,cAAc,OAAO,EAAE,CAAC;EACxB,aAAa;EACb,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC;EACrC,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;EACnC,YAAY,cAAc,EAAE,IAAI;EAChC,YAAY,SAAS,EAAE,KAAK,IAAI;EAChC,cAAc,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC;EAC7C,aAAa;EACb,YAAY,KAAK,EAAE,MAAM;EACzB,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,GAAG;EACxB,cAAc,IAAI,EAAE,OAAO;EAC3B,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC9FD,MAAM,cAAc,GAAG,MAAM;EAC7B,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,6BAA6B,CAAC,CAAC;AACzE;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,MAAM,EAAE;EACd,QAAQ,MAAM,EAAE,UAAU;EAC1B,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,QAAQ,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE;EAC/B,UAAU,IAAI,MAAM,CAAC,UAAU,IAAI,GAAG,EAAE;EACxC,YAAY,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;EACvD,YAAY,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;EAC7D,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EACzE,YAAY,OAAO,GAAG,CAAC;EACvB,WAAW;EACX,UAAU,OAAO,IAAI,CAAC;EACtB,SAAS;EACT,OAAO;AACP;EACA,MAAM,KAAK,EAAE;EACb,QAAQ,SAAS,EAAE;EACnB,UAAU,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,IAAI,EAAE;EAC1C,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;EACvC,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE;EACtC,UAAU,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE;EACzC,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE;EACtC,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,KAAK,EAAE;EACxC,SAAS;EACT,QAAQ,MAAM,EAAE,GAAG;EACnB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EACxD,WAAW;EACX,SAAS;EACT,OAAO;AACP;EACA,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC7D,cAAc,IAAI,EAAE,QAAQ;EAC5B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9D,cAAc,IAAI,EAAE,QAAQ;EAC5B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC9ED,MAAM,wBAAwB,GAAG,MAAM;EACvC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAChE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,wCAAwC;EAC5C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,YAAY,GAAG,MAAM,IAAI;EACjC,IAAI,MAAM,UAAU,GAAG;EACvB,MAAM,CAAC,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;EAC/D,MAAM,CAAC,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,CAAC;EACxE,KAAK,CAAC;EACN,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,WAAW,CAAC;EACnC,IAAI,OAAO,CAAC,UAAU,EAAE,MAAM,CAAC,IAAI,CAAC;AACpC;AACA,eAAe,EAAE,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW;AAC9D,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACrB,KAAK;AACL,cAAc,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChE,cAAc,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChE,cAAc,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChE,cAAc,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChE,cAAc,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChE,UAAU,CAAC,CAAC;EACZ,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,MAAM,EAAE;EACd,QAAQ,MAAM,EAAE,UAAU;EAC1B,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,SAAS,EAAE,YAAY;EAC/B,OAAO;AACP;EACA,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,WAAW,EAAE,CAAC;EACxB,UAAU,KAAK,EAAE,QAAQ;EACzB,UAAU,SAAS,EAAE;EACrB,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE;EACxC,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE;EACxC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE;EAC3C,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;EACvC,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,KAAK,EAAE;EACzC,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,KAAK,EAAE;EAC7C,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY,SAAS,EAAE,SAAS;EAChC,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC/C,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EAC1D,aAAa;EACb,WAAW;EACX,SAAS;AACT;EACA,QAAQ;EACR,UAAU,SAAS,EAAE;EACrB,YAAY,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE;EAC1C,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;EACtC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;EACzC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;EACzC,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE;EAC3C,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,EAAE,EAAE;EACxC,WAAW;EACX,UAAU,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG;EACpD,UAAU,MAAM,EAAE,MAAM,CAAC,UAAU,GAAG,GAAG,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,CAAC;EAC3E,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC/C,aAAa;EACb,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC/C,cAAc,eAAe,EAAE,SAAS,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EACvE,cAAc,YAAY,EAAE,CAAC;EAC7B,cAAc,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;EAC7B,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;AACP;EACA,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC7D,cAAc,IAAI,EAAE,QAAQ;EAC5B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACvC,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;EACvD,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EAC9D,cAAc,IAAI,EAAE,QAAQ;EAC5B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC1D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;AACT;EACA,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;EACjD,cAAc,IAAI,EAAE,QAAQ;EAC5B,cAAc,MAAM,EAAE,MAAM;EAC5B,cAAc,UAAU,EAAE,EAAE;EAC5B,cAAc,SAAS,EAAE;EACzB,gBAAgB,IAAI,EAAE,QAAQ;EAC9B,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC1D,eAAe;EACf,cAAc,KAAK,EAAE;EACrB,gBAAgB,IAAI,EAAE,IAAI;EAC1B,gBAAgB,SAAS,EAAE,MAAM,IAAI;EACrC,kBAAkB,OAAO,MAAM,CAAC,KAAK,CAAC;EACtC,iBAAiB;EACjB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EACjD,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAC9C,cAAc,IAAI,EAAE,QAAQ;EAC5B,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;EACzC,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAG,CAAC;EACzD,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,iBAAiB,GAAG;EAC9B,MAAM,EAAE,EAAE;EACV,QAAQ,KAAK,EAAE;EACf,UAAU;EACV,YAAY,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAClC,YAAY,MAAM,EAAE,EAAE;EACtB,WAAW;EACX,UAAU;EACV,YAAY,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAClC,YAAY,MAAM,EAAE,EAAE;EACtB,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,KAAK,EAAE;EACf,UAAU;EACV,YAAY,MAAM,EAAE,GAAG;EACvB,WAAW;EACX,UAAU;EACV,YAAY,MAAM,EAAE,GAAG;EACvB,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,KAAK,EAAE;EACf,UAAU;EACV,YAAY,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAClC,WAAW;EACX,UAAU;EACV,YAAY,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAClC,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;EAC9E,GAAG;EACH,CAAC;;EChND,MAAM,sBAAsB,GAAG,MAAM;EACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACxE,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,sCAAsC;EAC1C,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,OAAO;EACX,IAAI,OAAO;EACX,IAAI,KAAK;EACT,IAAI,MAAM;EACV,IAAI,MAAM;EACV,IAAI,QAAQ;EACZ,IAAI,WAAW;EACf,IAAI,SAAS;EACb,IAAI,UAAU;EACd,IAAI,UAAU;EACd,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,SAAS,GAAG,MAAM;EAC1B,IAAI,IAAI,MAAM,CAAC,UAAU,GAAG,IAAI,IAAI,MAAM,CAAC,UAAU,GAAG,GAAG,EAAE;EAC7D,MAAM,OAAO;EACb,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;EACtB,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;EACtB,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;EACtB,OAAO,CAAC;EACR,KAAK;EACL,IAAI,IAAI,MAAM,CAAC,UAAU,GAAG,GAAG,EAAE;EACjC,MAAM,OAAO;EACb,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;EACtB,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;EACtB,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC;EACtB,OAAO,CAAC;EACR,KAAK;EACL,IAAI,OAAO;EACX,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;EACpB,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;EACpB,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;EACpB,KAAK,CAAC;EACN,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,QAAQ,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE;EAC/B,UAAU,IAAI,MAAM,CAAC,UAAU,IAAI,GAAG,EAAE;EACxC,YAAY,MAAM,aAAa,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC;EACvD,YAAY,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;EAC7D,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EACzE,YAAY,OAAO,GAAG,CAAC;EACvB,WAAW;EACX,UAAU,OAAO,IAAI,CAAC;EACtB,SAAS;EACT,OAAO;AACP;EACA,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,SAAS,EAAE;EACrB,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE;EACvC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;EACzC,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE;EAC3C,YAAY,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE;EAC1C,WAAW;EACX,UAAU,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;EAChC,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EAC1D,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,SAAS,EAAE;EACrB,YAAY,EAAE,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG,EAAE;EAC1C,YAAY,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,EAAE,GAAG,EAAE;EAC/C,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE;EACxC,YAAY,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,EAAE,GAAG,EAAE;EAC7C,YAAY,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE;EACxC,WAAW;EACX,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;EAChC,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EAC1D,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,KAAK;EAC1C,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,GAAG,EAAE,GAAG;EACpB,WAAW,CAAC,CAAC;EACb,UAAU,MAAM,EAAE,SAAS,EAAE,CAAC,CAAC,CAAC;EAChC,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EAC1D,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;AACP;EACA,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,OAAO,EAAE;EACnB,YAAY,OAAO,EAAE,MAAM;EAC3B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;EACnD,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACrC,cAAc,IAAI,EAAE,YAAY;EAChC,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACvC,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACzC,cAAc,IAAI,EAAE,uBAAuB;EAC3C,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC1D,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC1D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EACzC,cAAc,IAAI,EAAE,eAAe;EACnC,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC1D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE,EAAE;EACvB,UAAU,OAAO,EAAE;EACnB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,IAAI,EAAE,eAAe;EACnC,cAAc,KAAK,EAAE;EACrB,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;EAC5E,gBAAgB,GAAG;EACnB,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC1D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,aAAa;EACjC,cAAc,KAAK,EAAE;EACrB,gBAAgB,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG;EAC5E,gBAAgB,GAAG;EACnB,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC1C,eAAe;EACf,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EAC1D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3D;EACA,IAAI,MAAM,CAAC,MAAM;EACjB,MAAM,KAAK,CAAC,SAAS,CAAC;EACtB,QAAQ,KAAK,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK;EACxC,UAAU,MAAM,EAAE,IAAI;EACtB,SAAS,CAAC,CAAC;EACX,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;EACH,CAAC;;ECvND,MAAM,gBAAgB,GAAG,MAAM;EAC/B,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE;EACzD,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACzB,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,+BAA+B,CAAC,CAAC;AAC3E;EACA,EAAE,MAAM,KAAK,GAAG;EAChB,IAAI,KAAK;EACT,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,KAAK;EACT,GAAG,CAAC;EACJ,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,QAAQ;EACZ,IAAI,SAAS;EACb,IAAI,WAAW;EACf,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC;EAClB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EACjC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;EACpC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAChD,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,QAAQ,EAAE,KAAK;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,MAAM,EAAE,KAAK;EACrB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,SAAS,EAAE;EACjB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,GAAG,EAAE,EAAE;EACf,QAAQ,UAAU,EAAE,IAAI;EACxB,QAAQ,MAAM,EAAE,YAAY;EAC5B,QAAQ,IAAI,EAAE,QAAQ;EACtB,QAAQ,MAAM,EAAE,IAAI;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC3C,UAAU,UAAU,EAAE,GAAG;EACzB,SAAS;EACT,QAAQ,OAAO,EAAE;EACjB,UAAU,KAAK,EAAE;EACjB,YAAY,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;EAC7C,YAAY,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;EAC1C,YAAY,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;EAC7C;EACA;EACA,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,IAAI;EACd,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,UAAU,EAAE,EAAE;EAC5B,cAAc,WAAW,EAAE,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC;EAC5D,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC/HD,MAAM,4BAA4B,GAAG,MAAM;EAC3C,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE;EACzD,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACzB,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa;EACzC,IAAI,6CAA6C;EACjD,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG;EAChB,IAAI,KAAK;EACT,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,KAAK;EACT,IAAI,KAAK;EACT,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,IAAI;EACR,IAAI,KAAK;EACT,GAAG,CAAC;EACJ,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,QAAQ;EACZ,IAAI,SAAS;EACb,IAAI,WAAW;EACf,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,IAAI,UAAU;EACd,IAAI,QAAQ;EACZ,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC;EAClB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;EACjC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;EACpC,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,eAAe,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAChD,KAAK;EACL,GAAG;AACH;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;EAChD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,aAAa,EAAE;EACrB,QAAQ,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;EACtC,QAAQ,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;EACzC,OAAO;AACP;EACA,MAAM,OAAO,EAAE;EACf,QAAQ,QAAQ,EAAE,KAAK;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EACnD,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,SAAS,EAAE;EACjB,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,GAAG,EAAE,EAAE;EACf,QAAQ,UAAU,EAAE,IAAI;EACxB,QAAQ,MAAM,EAAE,YAAY;EAC5B,QAAQ,IAAI,EAAE,QAAQ;EACtB,QAAQ,MAAM,EAAE,IAAI;EACpB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC7C,UAAU,UAAU,EAAE,GAAG;EACzB,SAAS;EACT,OAAO;AACP;EACA,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,IAAI;EACd,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,WAAW,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,UAAU,EAAE,EAAE;EAC5B,cAAc,WAAW,EAAE,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC;EAC5D,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECvFD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1C;EACA,QAAQ,CAAC,kBAAkB,CAAC,CAAC;EAC7B,QAAQ,CAAC,sBAAsB,CAAC,CAAC;EACjC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC/B,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC/B,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EAC9B,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EAC9B,QAAQ,CAAC,iBAAiB,CAAC,CAAC;EAC5B,QAAQ,CAAC,qBAAqB,CAAC,CAAC;EAChC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC/B,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EAC3B,QAAQ,CAAC,qBAAqB,CAAC,CAAC;EAChC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;EAC5B,QAAQ,CAAC,sBAAsB,CAAC,CAAC;EACjC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC/B,QAAQ,CAAC,kBAAkB,CAAC,CAAC;EAC7B,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EAC9B,QAAQ,CAAC,6BAA6B,CAAC,CAAC;EACxC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EAC3B,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC/B,QAAQ,CAAC,qBAAqB,CAAC,CAAC;EAChC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;EAChC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC/B,QAAQ,CAAC,yBAAyB,CAAC,CAAC;EACpC,QAAQ,CAAC,yBAAyB,CAAC,CAAC;EACpC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;EAClC,QAAQ,CAAC,UAAU,CAAC,CAAC;EACrB,QAAQ,CAAC,qBAAqB,CAAC,CAAC;EAChC,QAAQ,CAAC,eAAe,CAAC,CAAC;EAC1B,QAAQ,CAAC,uBAAuB,CAAC,CAAC;EAClC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACrC,QAAQ,CAAC,YAAY,CAAC,CAAC;EACvB,QAAQ,CAAC,iBAAiB,CAAC,CAAC;EAC5B,QAAQ,CAAC,wBAAwB,CAAC,CAAC;EACnC,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC/B,QAAQ,CAAC,sBAAsB,CAAC,CAAC;EACjC,QAAQ,CAAC,qBAAqB,CAAC,CAAC;EAChC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EAC9B,QAAQ,CAAC,sBAAsB,CAAC,CAAC;EACjC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;EAC7B,QAAQ,CAAC,uBAAuB,CAAC,CAAC;EAClC,QAAQ,CAAC,wBAAwB,CAAC,CAAC;EACnC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EAC9B,QAAQ,CAAC,cAAc,CAAC,CAAC;EACzB,QAAQ,CAAC,wBAAwB,CAAC,CAAC;EACnC,QAAQ,CAAC,sBAAsB,CAAC,CAAC;EACjC,QAAQ,CAAC,gBAAgB,CAAC,CAAC;EAC3B,QAAQ,CAAC,4BAA4B,CAAC;;;;;;"}