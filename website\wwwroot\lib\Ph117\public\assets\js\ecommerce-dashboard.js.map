{"version": 3, "file": "ecommerce-dashboard.js", "sources": ["../../../src/js/theme/charts/echarts/echarts-utils.js", "../../../src/js/theme/charts/echarts/new-customers.js", "../../../src/js/theme/charts/echarts/paying-customer-chart.js", "../../../src/js/theme/charts/echarts/projection-vs-actual-chart.js", "../../../src/js/theme/data.js", "../../../src/js/theme/charts/echarts/returning-customer-chart.js", "../../../src/js/theme/charts/echarts/top-coupons-chart.js", "../../../src/js/theme/charts/echarts/total-orders-chart.js", "../../../src/js/theme/charts/echarts/total-sales-chart.js", "../../../src/js/theme/revenue-map.js", "../../../src/js/pages/ecommerce-dashboard.js"], "sourcesContent": ["// import * as echarts from 'echarts';\r\nconst { merge } = window._;\r\n\r\n// form config.js\r\nexport const echartSetOption = (\r\n  chart,\r\n  userOptions,\r\n  getDefaultOptions,\r\n  responsiveOptions\r\n) => {\r\n  const { breakpoints, resize } = window.phoenix.utils;\r\n  const handleResize = options => {\r\n    Object.keys(options).forEach(item => {\r\n      if (window.innerWidth > breakpoints[item]) {\r\n        chart.setOption(options[item]);\r\n      }\r\n    });\r\n  };\r\n\r\n  const themeController = document.body;\r\n  // Merge user options with lodash\r\n  chart.setOption(merge(getDefaultOptions(), userOptions));\r\n\r\n  const navbarVerticalToggle = document.querySelector(\r\n    '.navbar-vertical-toggle'\r\n  );\r\n  if (navbarVerticalToggle) {\r\n    navbarVerticalToggle.addEventListener('navbar.vertical.toggle', () => {\r\n      chart.resize();\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    });\r\n  }\r\n\r\n  resize(() => {\r\n    chart.resize();\r\n    if (responsiveOptions) {\r\n      handleResize(responsiveOptions);\r\n    }\r\n  });\r\n  if (responsiveOptions) {\r\n    handleResize(responsiveOptions);\r\n  }\r\n\r\n  themeController.addEventListener(\r\n    'clickControl',\r\n    ({ detail: { control } }) => {\r\n      if (control === 'phoenixTheme') {\r\n        chart.setOption(window._.merge(getDefaultOptions(), userOptions));\r\n      }\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    }\r\n  );\r\n};\r\n// -------------------end config.js--------------------\r\n\r\nconst echartTabs = document.querySelectorAll('[data-tab-has-echarts]');\r\nif (echartTabs) {\r\n  echartTabs.forEach(tab => {\r\n    tab.addEventListener('shown.bs.tab', e => {\r\n      const el = e.target;\r\n      const { hash } = el;\r\n      const id = hash || el.dataset.bsTarget;\r\n      const content = document.getElementById(id.substring(1));\r\n      const chart = content?.querySelector('[data-echart-tab]');\r\n      if (chart) {\r\n        window.echarts.init(chart).resize();\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport const tooltipFormatter = (params, dateFormatter = 'MMM DD') => {\r\n  let tooltipItem = ``;\r\n  params.forEach(el => {\r\n    tooltipItem += `<div class='ms-1'>\r\n        <h6 class=\"text-body-tertiary\"><span class=\"fas fa-circle me-1 fs-10\" style=\"color:${\r\n          el.borderColor ? el.borderColor : el.color\r\n        }\"></span>\r\n          ${el.seriesName} : ${\r\n      typeof el.value === 'object' ? el.value[1] : el.value\r\n    }\r\n        </h6>\r\n      </div>`;\r\n  });\r\n  return `<div>\r\n            <p class='mb-2 text-body-tertiary'>\r\n              ${\r\n                window.dayjs(params[0].axisValue).isValid()\r\n                  ? window.dayjs(params[0].axisValue).format(dateFormatter)\r\n                  : params[0].axisValue\r\n              }\r\n            </p>\r\n            ${tooltipItem}\r\n          </div>`;\r\n};\r\n\r\nexport const handleTooltipPosition = ([pos, , dom, , size]) => {\r\n  // only for mobile device\r\n  if (window.innerWidth <= 540) {\r\n    const tooltipHeight = dom.offsetHeight;\r\n    const obj = { top: pos[1] - tooltipHeight - 20 };\r\n    obj[pos[0] < size.viewSize[0] / 2 ? 'left' : 'right'] = 5;\r\n    return obj;\r\n  }\r\n  return null; // else default behaviour\r\n};\r\n", "import { echartSetOption } from './echarts-utils';\r\n/* -------------------------------------------------------------------------- */\r\n/*                     Echart Bar Member info                                 */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst newCustomersChartsInit = () => {\r\n  const { getColor, getData, getDates } = window.phoenix.utils;\r\n  const $echartNewCustomersCharts = document.querySelector(\r\n    '.echarts-new-customers'\r\n  );\r\n  const tooltipFormatter = params => {\r\n    const currentDate = window.dayjs(params[0].axisValue);\r\n    const prevDate = window.dayjs(params[0].axisValue).subtract(1, 'month');\r\n\r\n    const result = params.map((param, index) => ({\r\n      value: param.value,\r\n      date: index > 0 ? prevDate : currentDate,\r\n      color: param.color\r\n    }));\r\n\r\n    let tooltipItem = ``;\r\n    result.forEach((el, index) => {\r\n      tooltipItem += `<h6 class=\"fs-9 text-body-tertiary ${\r\n        index > 0 && 'mb-0'\r\n      }\"><span class=\"fas fa-circle me-2\" style=\"color:${el.color}\"></span>\r\n      ${el.date.format('MMM DD')} : ${el.value}\r\n    </h6>`;\r\n    });\r\n    return `<div class='ms-1'>\r\n              ${tooltipItem}\r\n            </div>`;\r\n  };\r\n\r\n  if ($echartNewCustomersCharts) {\r\n    const userOptions = getData($echartNewCustomersCharts, 'echarts');\r\n    const chart = window.echarts.init($echartNewCustomersCharts);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: 10,\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: tooltipFormatter,\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      xAxis: [\r\n        {\r\n          type: 'category',\r\n          data: getDates(\r\n            new Date('5/1/2022'),\r\n            new Date('5/7/2022'),\r\n            1000 * 60 * 60 * 24\r\n          ),\r\n          show: true,\r\n          boundaryGap: false,\r\n          axisLine: {\r\n            show: true,\r\n            lineStyle: { color: getColor('secondary-bg') }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            formatter: value => window.dayjs(value).format('DD MMM'),\r\n            showMinLabel: true,\r\n            showMaxLabel: false,\r\n            color: getColor('secondary-color'),\r\n            align: 'left',\r\n            interval: 5,\r\n            fontFamily: 'Nunito Sans',\r\n            fontWeight: 600,\r\n            fontSize: 12.8\r\n          }\r\n        },\r\n        {\r\n          type: 'category',\r\n          position: 'bottom',\r\n          show: true,\r\n          data: getDates(\r\n            new Date('5/1/2022'),\r\n            new Date('5/7/2022'),\r\n            1000 * 60 * 60 * 24\r\n          ),\r\n          axisLabel: {\r\n            formatter: value => window.dayjs(value).format('DD MMM'),\r\n            interval: 130,\r\n            showMaxLabel: true,\r\n            showMinLabel: false,\r\n            color: getColor('secondary-color'),\r\n            align: 'right',\r\n            fontFamily: 'Nunito Sans',\r\n            fontWeight: 600,\r\n            fontSize: 12.8\r\n          },\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          boundaryGap: false\r\n        }\r\n      ],\r\n      yAxis: {\r\n        show: false,\r\n        type: 'value',\r\n        boundaryGap: false\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          data: [150, 100, 300, 200, 250, 180, 250],\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          lineStyle: {\r\n            width: 2,\r\n            color: getColor('secondary-bg')\r\n          },\r\n          emphasis: {\r\n            lineStyle: {\r\n              color: getColor('secondary-bg')\r\n            }\r\n          },\r\n          zlevel: 2\r\n        },\r\n        {\r\n          type: 'line',\r\n          data: [200, 150, 250, 100, 500, 400, 600],\r\n          lineStyle: {\r\n            width: 2,\r\n            color: getColor('primary')\r\n          },\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          zlevel: 2\r\n        }\r\n      ],\r\n      grid: { left: 0, right: 0, top: 5, bottom: 20 }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default newCustomersChartsInit;\r\n", "import { echartSetOption, handleTooltipPosition } from './echarts-utils';\r\n\r\nconst { echarts } = window;\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                                Market Share                                */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst payingCustomerChartInit = () => {\r\n  const { getData, getColor } = window.phoenix.utils;\r\n  const $chartEl = document.querySelector('.echarts-paying-customer-chart');\r\n\r\n  if ($chartEl) {\r\n    const userOptions = getData($chartEl, 'options');\r\n    const chart = echarts.init($chartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        position: (...params) => handleTooltipPosition(params),\r\n        transitionDuration: 0,\r\n        formatter: params => {\r\n          return `<strong>${params.seriesName}:</strong> ${params.value}%`;\r\n        },\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      legend: { show: false },\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          center: ['50%', '60%'],\r\n          name: 'Paying customer',\r\n          startAngle: 180,\r\n          endAngle: 0,\r\n          min: 0,\r\n          max: 100,\r\n          splitNumber: 12,\r\n          itemStyle: {\r\n            color: getColor('primary')\r\n          },\r\n          progress: {\r\n            show: true,\r\n            roundCap: true,\r\n            width: 12,\r\n            itemStyle: {\r\n              shadowBlur: 0,\r\n              shadowColor: '#0000'\r\n            }\r\n          },\r\n          pointer: {\r\n            show: false\r\n          },\r\n          axisLine: {\r\n            roundCap: true,\r\n            lineStyle: {\r\n              width: 12,\r\n              color: [[1, getColor('primary-bg-subtle')]]\r\n            }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          title: {\r\n            show: false\r\n          },\r\n          detail: {\r\n            show: false\r\n          },\r\n          data: [\r\n            {\r\n              value: 30\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default payingCustomerChartInit;\r\n", "import {\r\n  echartSetOption,\r\n  handleTooltipPosition,\r\n  tooltipFormatter\r\n} from './echarts-utils';\r\n\r\n// dayjs.extend(advancedFormat);\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Total Sales                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst projectionVsActualChartInit = () => {\r\n  const { getColor, getData, getPastDates } = window.phoenix.utils;\r\n  const $projectionVsActualChartEl = document.querySelector(\r\n    '.echart-projection-actual'\r\n  );\r\n\r\n  const dates = getPastDates(10);\r\n\r\n  const data1 = [\r\n    44485, 20428, 47302, 45180, 31034, 46358, 26581, 36628, 38219, 43256\r\n  ];\r\n\r\n  const data2 = [\r\n    38911, 29452, 31894, 47876, 31302, 27731, 25490, 30355, 27176, 30393\r\n  ];\r\n\r\n  if ($projectionVsActualChartEl) {\r\n    const userOptions = getData($projectionVsActualChartEl, 'echarts');\r\n    const chart = window.echarts.init($projectionVsActualChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [getColor('primary'), getColor('tertiary-bg')],\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        position: (...params) => handleTooltipPosition(params),\r\n        formatter: params => tooltipFormatter(params),\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      legend: {\r\n        data: ['Projected revenue', 'Actual revenue'],\r\n        right: 'right',\r\n        width: '100%',\r\n        itemWidth: 16,\r\n        itemHeight: 8,\r\n        itemGap: 20,\r\n        top: 3,\r\n        inactiveColor: getColor('quaternary-color'),\r\n        textStyle: {\r\n          color: getColor('body-color'),\r\n          fontWeight: 600,\r\n          fontFamily: 'Nunito Sans'\r\n          // fontSize: '12.8px'\r\n        }\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        // boundaryGap: false,\r\n        axisLabel: {\r\n          color: getColor('secondary-color'),\r\n          formatter: value => window.dayjs(value).format('MMM DD'),\r\n          interval: 3,\r\n          fontFamily: 'Nunito Sans',\r\n          fontWeight: 600,\r\n          fontSize: 12.8\r\n        },\r\n        data: dates,\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('tertiary-bg')\r\n          }\r\n        },\r\n        axisTick: false\r\n      },\r\n      yAxis: {\r\n        axisPointer: { type: 'none' },\r\n        // boundaryGap: false,\r\n        axisTick: 'none',\r\n        splitLine: {\r\n          interval: 5,\r\n          lineStyle: {\r\n            color: getColor('secondary-bg')\r\n          }\r\n        },\r\n        axisLine: { show: false },\r\n        axisLabel: {\r\n          fontFamily: 'Nunito Sans',\r\n          fontWeight: 600,\r\n          fontSize: 12.8,\r\n          color: getColor('secondary-color'),\r\n          margin: 20,\r\n          verticalAlign: 'bottom',\r\n          formatter: value => `$${value.toLocaleString()}`\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Projected revenue',\r\n          type: 'bar',\r\n          barWidth: '6px',\r\n          data: data2,\r\n          barGap: '30%',\r\n          label: { show: false },\r\n          itemStyle: {\r\n            borderRadius: [2, 2, 0, 0],\r\n            color: getColor('primary')\r\n          }\r\n        },\r\n        {\r\n          name: 'Actual revenue',\r\n          type: 'bar',\r\n          data: data1,\r\n          barWidth: '6px',\r\n          barGap: '30%',\r\n          label: { show: false },\r\n          z: 10,\r\n          itemStyle: {\r\n            borderRadius: [2, 2, 0, 0],\r\n            color: getColor('info-bg-subtle')\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 0,\r\n        left: 3,\r\n        bottom: 0,\r\n        top: '15%',\r\n        containLabel: true\r\n      },\r\n      animation: false\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default projectionVsActualChartInit;\r\n", "export const months = [\r\n  'January',\r\n  'February',\r\n  'March',\r\n  'April',\r\n  'May',\r\n  'June',\r\n  'July',\r\n  'August',\r\n  'September',\r\n  'October',\r\n  'November',\r\n  'December'\r\n];\r\n\r\nexport const monthsShort = [\r\n  'Jan',\r\n  'Feb',\r\n  'Mar',\r\n  'Apr',\r\n  'May',\r\n  'Jun',\r\n  'Jul',\r\n  'Aug',\r\n  'Sep',\r\n  'Oct',\r\n  'Nov',\r\n  'Dec'\r\n];\r\n\r\nexport const leaftletPoints = [\r\n  {\r\n    lat: 53.958332,\r\n    long: -1.080278,\r\n    name: '<PERSON>',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 52.958332,\r\n    long: -1.080278,\r\n    name: '<PERSON>',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 51.958332,\r\n    long: -1.080278,\r\n    name: '<PERSON>',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 53.958332,\r\n    long: -1.080278,\r\n    name: '<PERSON>',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 54.958332,\r\n    long: -1.080278,\r\n    name: '<PERSON>',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 55.958332,\r\n    long: -1.080278,\r\n    name: '<PERSON>',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 53.908332,\r\n    long: -1.080278,\r\n    name: 'Diana Meyer',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 53.008332,\r\n    long: -1.080278,\r\n    name: '<PERSON> Meyer',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 53.158332,\r\n    long: -1.080278,\r\n    name: 'Diana Meyer',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 53.000032,\r\n    long: -1.080278,\r\n    name: 'Diana Meyer',\r\n    street: 'Slude Strand 27',\r\n    location: '1130 Kobenhavn'\r\n  },\r\n  {\r\n    lat: 52.292001,\r\n    long: -2.22,\r\n    name: 'Anke Schroder',\r\n    street: 'Industrivej 54',\r\n    location: '4140 Borup'\r\n  },\r\n  {\r\n    lat: 52.392001,\r\n    long: -2.22,\r\n    name: 'Anke Schroder',\r\n    street: 'Industrivej 54',\r\n    location: '4140 Borup'\r\n  },\r\n  {\r\n    lat: 51.492001,\r\n    long: -2.22,\r\n    name: 'Anke Schroder',\r\n    street: 'Industrivej 54',\r\n    location: '4140 Borup'\r\n  },\r\n  {\r\n    lat: 51.192001,\r\n    long: -2.22,\r\n    name: 'Anke Schroder',\r\n    street: 'Industrivej 54',\r\n    location: '4140 Borup'\r\n  },\r\n  {\r\n    lat: 52.292001,\r\n    long: -2.22,\r\n    name: 'Anke Schroder',\r\n    street: 'Industrivej 54',\r\n    location: '4140 Borup'\r\n  },\r\n  {\r\n    lat: 54.392001,\r\n    long: -2.22,\r\n    name: 'Anke Schroder',\r\n    street: 'Industrivej 54',\r\n    location: '4140 Borup'\r\n  },\r\n  {\r\n    lat: 51.292001,\r\n    long: -2.22,\r\n    name: 'Anke Schroder',\r\n    street: 'Industrivej 54',\r\n    location: '4140 Borup'\r\n  },\r\n  {\r\n    lat: 52.102001,\r\n    long: -2.22,\r\n    name: 'Anke Schroder',\r\n    street: 'Industrivej 54',\r\n    location: '4140 Borup'\r\n  },\r\n  {\r\n    lat: 52.202001,\r\n    long: -2.22,\r\n    name: 'Anke Schroder',\r\n    street: 'Industrivej 54',\r\n    location: '4140 Borup'\r\n  },\r\n  {\r\n    lat: 51.063202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.363202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.463202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.563202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.763202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.863202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.963202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.000202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.000202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.163202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 52.263202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 53.463202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 55.163202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 56.263202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 56.463202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 56.563202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 56.663202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 56.763202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 56.863202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 56.963202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 57.973202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 57.163202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.163202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.263202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.363202,\r\n    long: -1.308,\r\n    name: 'Tobias Vogel',\r\n    street: 'Mollebakken 33',\r\n    location: '3650 Olstykke'\r\n  },\r\n  {\r\n    lat: 51.409,\r\n    long: -2.647,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.68,\r\n    long: -1.49,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 50.259998,\r\n    long: -5.051,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 54.906101,\r\n    long: -1.38113,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.383331,\r\n    long: -1.466667,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.483002,\r\n    long: -2.2931,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 51.509865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 51.109865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 51.209865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 51.309865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 51.409865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 51.609865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 51.709865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 51.809865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 51.909865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.109865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.209865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.309865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.409865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.509865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.609865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.709865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.809865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.909865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.519865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.529865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.539865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.549865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 52.549865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.109865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.209865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.319865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.329865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.409865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.559865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.619865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.629865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.639865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.649865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.669865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.669865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.719865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.739865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.749865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.759865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.769865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.769865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.819865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.829865,\r\n    long: -0.118092,\r\n    name: 'Richard Hendricks',\r\n    street: '37 Seafield Place',\r\n    location: 'London'\r\n  },\r\n  {\r\n    lat: 53.483959,\r\n    long: -2.244644,\r\n    name: 'Ethel B. Brooks',\r\n    street: '2576 Sun Valley Road'\r\n  },\r\n  {\r\n    lat: 40.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 39.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 38.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 37.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 40.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 41.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 42.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 43.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 44.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 45.737,\r\n    long: -73.923,\r\n    name: 'Marshall D. Lewis',\r\n    street: '1489 Michigan Avenue',\r\n    location: 'Michigan'\r\n  },\r\n  {\r\n    lat: 46.7128,\r\n    long: 74.006,\r\n    name: 'Elizabeth C. Lyons',\r\n    street: '4553 Kenwood Place',\r\n    location: 'Fort Lauderdale'\r\n  },\r\n  {\r\n    lat: 40.7128,\r\n    long: 74.1181,\r\n    name: 'Elizabeth C. Lyons',\r\n    street: '4553 Kenwood Place',\r\n    location: 'Fort Lauderdale'\r\n  },\r\n  {\r\n    lat: 14.235,\r\n    long: 51.9253,\r\n    name: 'Ralph D. Wylie',\r\n    street: '3186 Levy Court',\r\n    location: 'North Reading'\r\n  },\r\n  {\r\n    lat: 15.235,\r\n    long: 51.9253,\r\n    name: 'Ralph D. Wylie',\r\n    street: '3186 Levy Court',\r\n    location: 'North Reading'\r\n  },\r\n  {\r\n    lat: 16.235,\r\n    long: 51.9253,\r\n    name: 'Ralph D. Wylie',\r\n    street: '3186 Levy Court',\r\n    location: 'North Reading'\r\n  },\r\n  {\r\n    lat: 14.235,\r\n    long: 51.9253,\r\n    name: 'Ralph D. Wylie',\r\n    street: '3186 Levy Court',\r\n    location: 'North Reading'\r\n  },\r\n  {\r\n    lat: 15.8267,\r\n    long: 47.9218,\r\n    name: 'Hope A. Atkins',\r\n    street: '3715 Hillcrest Drive',\r\n    location: 'Seattle'\r\n  },\r\n  {\r\n    lat: 15.9267,\r\n    long: 47.9218,\r\n    name: 'Hope A. Atkins',\r\n    street: '3715 Hillcrest Drive',\r\n    location: 'Seattle'\r\n  },\r\n  {\r\n    lat: 23.4425,\r\n    long: 58.4438,\r\n    name: 'Samuel R. Bailey',\r\n    street: '2883 Raoul Wallenberg Place',\r\n    location: 'Cheshire'\r\n  },\r\n  {\r\n    lat: 23.5425,\r\n    long: 58.3438,\r\n    name: 'Samuel R. Bailey',\r\n    street: '2883 Raoul Wallenberg Place',\r\n    location: 'Cheshire'\r\n  },\r\n  {\r\n    lat: -37.8927369333,\r\n    long: 175.4087452333,\r\n    name: 'Samuel R. Bailey',\r\n    street: '3228 Glory Road',\r\n    location: 'Nashville'\r\n  },\r\n  {\r\n    lat: -38.9064188833,\r\n    long: 175.4441556833,\r\n    name: 'Samuel R. Bailey',\r\n    street: '3228 Glory Road',\r\n    location: 'Nashville'\r\n  },\r\n  {\r\n    lat: -12.409874,\r\n    long: -65.596832,\r\n    name: 'Ann J. Perdue',\r\n    street: '921 Ella Street',\r\n    location: 'Dublin'\r\n  },\r\n  {\r\n    lat: -22.090887,\r\n    long: -57.411827,\r\n    name: 'Jorge C. Woods',\r\n    street: '4800 North Bend River Road',\r\n    location: 'Allen'\r\n  },\r\n  {\r\n    lat: -19.019585,\r\n    long: -65.261963,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: -16.500093,\r\n    long: -68.214684,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: -17.413977,\r\n    long: -66.165321,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: -16.489689,\r\n    long: -68.119293,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: 54.766323,\r\n    long: 3.08603729,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: 54.866323,\r\n    long: 3.08603729,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: 49.537685,\r\n    long: 3.08603729,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: 54.715424,\r\n    long: 0.509207,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: 44.891666,\r\n    long: 10.136665,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: 48.078335,\r\n    long: 14.535004,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: -26.358055,\r\n    long: 27.398056,\r\n    name: 'Russ E. Panek',\r\n    street: '4068 Hartland Avenue',\r\n    location: 'Appleton'\r\n  },\r\n  {\r\n    lat: -29.1,\r\n    long: 26.2167,\r\n    name: 'Wilbur J. Dry',\r\n    street: '2043 Jadewood Drive',\r\n    location: 'Northbrook'\r\n  },\r\n  {\r\n    lat: -29.883333,\r\n    long: 31.049999,\r\n    name: 'Wilbur J. Dry',\r\n    street: '2043 Jadewood Drive',\r\n    location: 'Northbrook'\r\n  },\r\n  {\r\n    lat: -26.266111,\r\n    long: 27.865833,\r\n    name: 'Wilbur J. Dry',\r\n    street: '2043 Jadewood Drive',\r\n    location: 'Northbrook'\r\n  },\r\n  {\r\n    lat: -29.087217,\r\n    long: 26.154898,\r\n    name: 'Wilbur J. Dry',\r\n    street: '2043 Jadewood Drive',\r\n    location: 'Northbrook'\r\n  },\r\n  {\r\n    lat: -33.958252,\r\n    long: 25.619022,\r\n    name: 'Wilbur J. Dry',\r\n    street: '2043 Jadewood Drive',\r\n    location: 'Northbrook'\r\n  },\r\n  {\r\n    lat: -33.977074,\r\n    long: 22.457581,\r\n    name: 'Wilbur J. Dry',\r\n    street: '2043 Jadewood Drive',\r\n    location: 'Northbrook'\r\n  },\r\n  {\r\n    lat: -26.563404,\r\n    long: 27.844164,\r\n    name: 'Wilbur J. Dry',\r\n    street: '2043 Jadewood Drive',\r\n    location: 'Northbrook'\r\n  },\r\n  {\r\n    lat: 51.21389,\r\n    long: -102.462776,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 52.321945,\r\n    long: -106.584167,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 50.288055,\r\n    long: -107.793892,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 52.7575,\r\n    long: -108.28611,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 50.393333,\r\n    long: -105.551941,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 50.930557,\r\n    long: -102.807777,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 52.856388,\r\n    long: -104.610001,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 52.289722,\r\n    long: -106.666664,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 52.201942,\r\n    long: -105.123055,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 53.278046,\r\n    long: -110.00547,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 49.13673,\r\n    long: -102.990959,\r\n    name: 'Joseph B. Poole',\r\n    street: '3364 Lunetta Street',\r\n    location: 'Wichita Falls'\r\n  },\r\n  {\r\n    lat: 45.484531,\r\n    long: -73.597023,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 45.266666,\r\n    long: -71.900002,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 45.349998,\r\n    long: -72.51667,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 47.333332,\r\n    long: -79.433334,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 45.400002,\r\n    long: -74.033333,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 45.683334,\r\n    long: -73.433334,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 48.099998,\r\n    long: -77.783333,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 45.5,\r\n    long: -72.316666,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 46.349998,\r\n    long: -72.550003,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 48.119999,\r\n    long: -69.18,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 45.599998,\r\n    long: -75.25,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 46.099998,\r\n    long: -71.300003,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 45.700001,\r\n    long: -73.633331,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 47.68,\r\n    long: -68.879997,\r\n    name: 'Claudette D. Nowakowski',\r\n    street: '3742 Farland Avenue',\r\n    location: 'San Antonio'\r\n  },\r\n  {\r\n    lat: 46.716667,\r\n    long: -79.099998,\r\n    name: '299'\r\n  },\r\n  {\r\n    lat: 45.016666,\r\n    long: -72.099998,\r\n    name: '299'\r\n  }\r\n];\r\n", "import { months } from '../../data';\r\nimport { echartSetOption, tooltipFormatter } from './echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                     Echart Bar Member info                                 */\r\n/* -------------------------------------------------------------------------- */\r\nconst { echarts } = window;\r\n\r\nconst returningCustomerChartInit = () => {\r\n  const { getColor, getData } = window.phoenix.utils;\r\n\r\n  const $returningCustomerChart = document.querySelector(\r\n    '.echart-returning-customer'\r\n  );\r\n\r\n  if ($returningCustomerChart) {\r\n    const userOptions = getData($returningCustomerChart, 'echarts');\r\n    const chart = echarts.init($returningCustomerChart);\r\n    const getDefaultOptions = () => ({\r\n      color: getColor('body-highlight-bg'),\r\n      legend: {\r\n        data: [\r\n          {\r\n            name: 'Fourth time',\r\n            icon: 'roundRect',\r\n            itemStyle: {\r\n              color: getColor('primary-light'),\r\n              borderWidth: 0\r\n            }\r\n          },\r\n          {\r\n            name: 'Third time',\r\n            icon: 'roundRect',\r\n            itemStyle: { color: getColor('info-lighter'), borderWidth: 0 }\r\n          },\r\n          {\r\n            name: 'Second time',\r\n            icon: 'roundRect',\r\n            itemStyle: { color: getColor('primary'), borderWidth: 0 }\r\n          }\r\n        ],\r\n\r\n        right: 'right',\r\n        width: '100%',\r\n        itemWidth: 16,\r\n        itemHeight: 8,\r\n        itemGap: 20,\r\n        top: 3,\r\n        inactiveColor: getColor('quaternary-color'),\r\n        inactiveBorderWidth: 0,\r\n        textStyle: {\r\n          color: getColor('body-color'),\r\n          fontWeight: 600,\r\n          fontFamily: 'Nunito Sans'\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: tooltipFormatter,\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: months,\r\n        show: true,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: { color: getColor('tertiary-bg') }\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        axisLabel: {\r\n          // interval: 1,\r\n          showMinLabel: false,\r\n          showMaxLabel: false,\r\n          color: getColor('secondary-color'),\r\n          formatter: value => value.slice(0, 3),\r\n          fontFamily: 'Nunito Sans',\r\n          fontWeight: 600,\r\n          fontSize: 12.8\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: { color: getColor('secondary-bg'), type: 'dashed' }\r\n        }\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          showMinLabel: true,\r\n          showMaxLabel: true,\r\n          color: getColor('secondary-color'),\r\n          formatter: value => `${value}%`,\r\n          fontFamily: 'Nunito Sans',\r\n          fontWeight: 600,\r\n          fontSize: 12.8\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: { color: getColor('secondary-bg') }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Fourth time',\r\n          type: 'line',\r\n          data: [62, 90, 90, 90, 78, 84, 17, 17, 17, 17, 82, 95],\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          symbolSize: 10,\r\n          emphasis: {\r\n            lineStyle: {\r\n              width: 1\r\n            }\r\n          },\r\n          lineStyle: {\r\n            type: 'dashed',\r\n            width: 1,\r\n            color: getColor('primary-light')\r\n          },\r\n          itemStyle: {\r\n            borderColor: getColor('primary-light'),\r\n            borderWidth: 3\r\n          },\r\n          zlevel: 3\r\n        },\r\n        {\r\n          name: 'Third time',\r\n          type: 'line',\r\n          data: [50, 50, 30, 62, 18, 70, 70, 22, 70, 70, 70, 70],\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          symbolSize: 10,\r\n          emphasis: {\r\n            lineStyle: {\r\n              width: 1\r\n            }\r\n          },\r\n          lineStyle: {\r\n            width: 1,\r\n            color: getColor('info-lighter')\r\n          },\r\n          itemStyle: {\r\n            borderColor: getColor('info-lighter'),\r\n            borderWidth: 3\r\n          },\r\n          zlevel: 2\r\n        },\r\n        {\r\n          name: 'Second time',\r\n          type: 'line',\r\n          data: [40, 78, 60, 78, 60, 20, 60, 40, 60, 40, 20, 78],\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          symbolSize: 10,\r\n          emphasis: {\r\n            lineStyle: {\r\n              width: 3\r\n            }\r\n          },\r\n          lineStyle: {\r\n            width: 3,\r\n            color: getColor('primary')\r\n          },\r\n          itemStyle: {\r\n            borderColor: getColor('primary'),\r\n            borderWidth: 3\r\n          },\r\n          zlevel: 1\r\n        }\r\n      ],\r\n      grid: { left: 0, right: 8, top: '14%', bottom: 0, containLabel: true }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default returningCustomerChartInit;\r\n", "// import * as echarts from 'echarts';\r\nimport { echartSetOption } from './echarts-utils';\r\n\r\nconst { echarts } = window;\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                                Market Share                                */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst topCouponsChartInit = () => {\r\n  const { getData, getColor } = window.phoenix.utils;\r\n  const ECHART_TOP_COUPONS = '.echart-top-coupons';\r\n  const $echartTopCoupons = document.querySelector(ECHART_TOP_COUPONS);\r\n\r\n  if ($echartTopCoupons) {\r\n    const userOptions = getData($echartTopCoupons, 'options');\r\n    const chart = echarts.init($echartTopCoupons);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        getColor('primary'),\r\n        getColor('primary-lighter'),\r\n        getColor('info-dark')\r\n      ],\r\n\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position(pos, params, el, elRect, size) {\r\n          const obj = { top: pos[1] - 35 }; // set tooltip position over 35px from pointer\r\n          if (window.innerWidth > 540) {\r\n            if (pos[0] <= size.viewSize[0] / 2) {\r\n              obj.left = pos[0] + 20; // 'move in right';\r\n            } else {\r\n              obj.left = pos[0] - size.contentSize[0] - 20;\r\n            }\r\n          } else {\r\n            obj[pos[0] < size.viewSize[0] / 2 ? 'left' : 'right'] = 0;\r\n          }\r\n          return obj;\r\n        },\r\n        formatter: params => {\r\n          return `<strong>${params.data.name}:</strong> ${params.percent}%`;\r\n        },\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      legend: { show: false },\r\n      series: [\r\n        {\r\n          name: '72%',\r\n          type: 'pie',\r\n          radius: ['100%', '87%'],\r\n          avoidLabelOverlap: false,\r\n          emphasis: {\r\n            scale: false,\r\n            itemStyle: {\r\n              color: 'inherit'\r\n            }\r\n          },\r\n          itemStyle: {\r\n            borderWidth: 2,\r\n            borderColor: getColor('body-bg')\r\n          },\r\n          label: {\r\n            show: true,\r\n            position: 'center',\r\n            formatter: '{a}',\r\n            fontSize: 23,\r\n            color: getColor('light-text-emphasis')\r\n          },\r\n          data: [\r\n            { value: 7200000, name: 'Percentage discount' },\r\n            { value: 1800000, name: 'Fixed card discount' },\r\n            { value: 1000000, name: 'Fixed product discount' }\r\n          ]\r\n        }\r\n      ],\r\n      grid: { containLabel: true }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default topCouponsChartInit;\r\n", "import { echartSetOption, handleTooltipPosition } from './echarts-utils';\r\n\r\n// dayjs.extend(advancedFormat);\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Total Sales                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst totalOrdersChartInit = () => {\r\n  const { getColor, getData, getDates } = window.phoenix.utils;\r\n  const totalOrdersChartEl = document.querySelector('.echart-total-orders');\r\n\r\n  if (totalOrdersChartEl) {\r\n    const userOptions = getData(totalOrdersChartEl, 'echarts');\r\n    const chart = window.echarts.init(totalOrdersChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: getColor('primary'),\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        position: (...params) => handleTooltipPosition(params),\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: params => {\r\n          return `<strong>${window\r\n            .dayjs(params.name)\r\n            .format('DD MMM')}:</strong> ${params.value}`;\r\n        },\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: getDates(\r\n          new Date('5/1/2022'),\r\n          new Date('5/7/2022'),\r\n          1000 * 60 * 60 * 24\r\n        ),\r\n        show: true,\r\n        boundaryGap: false,\r\n        axisLine: {\r\n          show: true,\r\n          lineStyle: { color: getColor('secondary-bg') }\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        axisLabel: {\r\n          formatter: value => window.dayjs(value).format('DD MMM'),\r\n          interval: 6,\r\n          showMinLabel: true,\r\n          showMaxLabel: true,\r\n          color: getColor('secondary-color')\r\n        }\r\n      },\r\n      yAxis: {\r\n        show: false,\r\n        type: 'value',\r\n        boundaryGap: false\r\n      },\r\n      series: [\r\n        {\r\n          type: 'bar',\r\n          barWidth: '5px',\r\n          data: [120, 200, 150, 80, 70, 110, 120],\r\n          showBackground: true,\r\n          symbol: 'none',\r\n          itemStyle: {\r\n            borderRadius: 10\r\n          },\r\n          backgroundStyle: {\r\n            borderRadius: 10,\r\n            color: getColor('primary-bg-subtle')\r\n          }\r\n        }\r\n      ],\r\n      grid: { right: 10, left: 10, bottom: 0, top: 0 }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default totalOrdersChartInit;\r\n", "import { echartSetOption } from './echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Total Sales                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst totalSalesChartInit = () => {\r\n  const { getColor, getData, getDates } = window.phoenix.utils;\r\n  const $totalSalesChart = document.querySelector('.echart-total-sales-chart');\r\n\r\n  // getItemFromStore('phoenixTheme')\r\n\r\n  const dates = getDates(\r\n    new Date('5/1/2022'),\r\n    new Date('5/30/2022'),\r\n    1000 * 60 * 60 * 24\r\n  );\r\n\r\n  const currentMonthData = [\r\n    100, 200, 300, 300, 300, 250, 200, 200, 200, 200, 200, 500, 500, 500, 600,\r\n    700, 800, 900, 1000, 1100, 850, 600, 600, 600, 400, 200, 200, 300, 300, 300\r\n  ];\r\n\r\n  const prevMonthData = [\r\n    200, 200, 100, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 50, 200, 400, 600,\r\n    600, 600, 800, 1000, 700, 400, 450, 500, 600, 700, 650, 600, 550\r\n  ];\r\n\r\n  const tooltipFormatter = params => {\r\n    const currentDate = window.dayjs(params[0].axisValue);\r\n    const prevDate = window.dayjs(params[0].axisValue).subtract(1, 'month');\r\n\r\n    const result = params.map((param, index) => ({\r\n      value: param.value,\r\n      date: index > 0 ? prevDate : currentDate,\r\n      color: param.color\r\n    }));\r\n\r\n    let tooltipItem = ``;\r\n    result.forEach((el, index) => {\r\n      tooltipItem += `<h6 class=\"fs-9 text-body-tertiary ${\r\n        index > 0 && 'mb-0'\r\n      }\"><span class=\"fas fa-circle me-2\" style=\"color:${el.color}\"></span>\r\n      ${el.date.format('MMM DD')} : ${el.value}\r\n    </h6>`;\r\n    });\r\n    return `<div class='ms-1'>\r\n              ${tooltipItem}\r\n            </div>`;\r\n  };\r\n\r\n  if ($totalSalesChart) {\r\n    const userOptions = getData($totalSalesChart, 'echarts');\r\n    const chart = window.echarts.init($totalSalesChart);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [getColor('primary'), getColor('info')],\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: 10,\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none',\r\n          z: 0\r\n        },\r\n        formatter: tooltipFormatter,\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      xAxis: [\r\n        {\r\n          type: 'category',\r\n          data: dates,\r\n          axisLabel: {\r\n            formatter: value => window.dayjs(value).format('DD MMM'),\r\n            interval: 13,\r\n            showMinLabel: true,\r\n            showMaxLabel: false,\r\n            color: getColor('secondary-color'),\r\n            align: 'left',\r\n            fontFamily: 'Nunito Sans',\r\n            fontWeight: 600,\r\n            fontSize: 12.8\r\n          },\r\n          axisLine: {\r\n            show: true,\r\n            lineStyle: {\r\n              color: getColor('secondary-bg')\r\n            }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: true,\r\n            interval: 0,\r\n            lineStyle: {\r\n              color:\r\n                window.config.config.phoenixTheme === 'dark'\r\n                  ? getColor('body-highlight-bg')\r\n                  : getColor('secondary-bg')\r\n            }\r\n          },\r\n          boundaryGap: false\r\n        },\r\n        {\r\n          type: 'category',\r\n          position: 'bottom',\r\n          data: dates,\r\n          axisLabel: {\r\n            formatter: value => window.dayjs(value).format('DD MMM'),\r\n            interval: 130,\r\n            showMaxLabel: true,\r\n            showMinLabel: false,\r\n            color: getColor('secondary-color'),\r\n            align: 'right',\r\n            fontFamily: 'Nunito Sans',\r\n            fontWeight: 600,\r\n            fontSize: 12.8\r\n          },\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          boundaryGap: false\r\n        }\r\n      ],\r\n      yAxis: {\r\n        position: 'right',\r\n        axisPointer: { type: 'none' },\r\n        axisTick: 'none',\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        axisLine: { show: false },\r\n        axisLabel: { show: false }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'd',\r\n          type: 'line',\r\n          // data: Array.from(Array(30).keys()).map(() =>\r\n          //   getRandomNumber(100, 300)\r\n          // ),\r\n          data: currentMonthData,\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          zlevel: 2\r\n        },\r\n        {\r\n          name: 'e',\r\n          type: 'line',\r\n          // data: Array.from(Array(30).keys()).map(() =>\r\n          //   getRandomNumber(100, 300)\r\n          // ),\r\n          data: prevMonthData,\r\n          // symbol: 'none',\r\n          lineStyle: {\r\n            type: 'dashed',\r\n            width: 1,\r\n            color: getColor('info')\r\n          },\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          zlevel: 1\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 2,\r\n        left: 5,\r\n        bottom: '20px',\r\n        top: '2%',\r\n        containLabel: false\r\n      },\r\n      animation: false\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default totalSalesChartInit;\r\n", "/* eslint-disable no-nested-ternary */\r\nimport { leaftletPoints } from './data';\r\n/*-----------------------------------------------\r\n|   Gooogle Map\r\n-----------------------------------------------*/\r\n\r\nconst revenueMapInit = () => {\r\n  const themeController = document.body;\r\n  const $googlemaps = document.querySelectorAll('.revenue-map');\r\n  if ($googlemaps.length && window.google) {\r\n    // Visit https://snazzymaps.com/ for more themes\r\n    const mapStyles = {\r\n      SnazzyCustomLight: [\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'all',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'labels',\r\n          stylers: [\r\n            {\r\n              visibility: 'on'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'labels.text.fill',\r\n          stylers: [\r\n            {\r\n              color: '#525b75'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'labels.text.stroke',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'labels.icon',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative.country',\r\n          elementType: 'geometry.stroke',\r\n          stylers: [\r\n            {\r\n              visibility: 'on'\r\n            },\r\n            {\r\n              color: '#ffffff'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative.province',\r\n          elementType: 'geometry.stroke',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'landscape',\r\n          elementType: 'geometry',\r\n          stylers: [\r\n            {\r\n              visibility: 'on'\r\n            },\r\n            {\r\n              color: '#E3E6ED'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'landscape.natural',\r\n          elementType: 'labels',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'poi',\r\n          elementType: 'all',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'road',\r\n          elementType: 'all',\r\n          stylers: [\r\n            {\r\n              color: '#eff2f6'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'road',\r\n          elementType: 'labels',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'transit',\r\n          elementType: 'labels.icon',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'transit.line',\r\n          elementType: 'geometry',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'transit.line',\r\n          elementType: 'labels.text',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'transit.station.airport',\r\n          elementType: 'geometry',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'transit.station.airport',\r\n          elementType: 'labels',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'water',\r\n          elementType: 'geometry',\r\n          stylers: [\r\n            {\r\n              color: '#F5F7FA'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'water',\r\n          elementType: 'labels',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        }\r\n      ],\r\n      SnazzyCustomDark: [\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'all',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'labels',\r\n          stylers: [{ visibility: 'on' }]\r\n        },\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'labels.text.fill',\r\n          stylers: [\r\n            {\r\n              color: '#8a94ad'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'labels.text.stroke',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative',\r\n          elementType: 'labels.icon',\r\n          stylers: [\r\n            {\r\n              visibility: 'off'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative.country',\r\n          elementType: 'geometry.stroke',\r\n          stylers: [\r\n            { visibility: 'on' },\r\n            {\r\n              color: '#000000'\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          featureType: 'administrative.province',\r\n          elementType: 'geometry.stroke',\r\n          stylers: [{ visibility: 'off' }]\r\n        },\r\n        {\r\n          featureType: 'landscape',\r\n          elementType: 'geometry',\r\n          stylers: [{ visibility: 'on' }, { color: '#222834' }]\r\n        },\r\n        {\r\n          featureType: 'landscape.natural',\r\n          elementType: 'labels',\r\n          stylers: [{ visibility: 'off' }]\r\n        },\r\n        {\r\n          featureType: 'poi',\r\n          elementType: 'all',\r\n          stylers: [{ visibility: 'off' }]\r\n        },\r\n        {\r\n          featureType: 'road',\r\n          elementType: 'all',\r\n          stylers: [{ color: '#141824' }]\r\n        },\r\n        {\r\n          featureType: 'road',\r\n          elementType: 'labels',\r\n          stylers: [{ visibility: 'off' }]\r\n        },\r\n        {\r\n          featureType: 'transit',\r\n          elementType: 'labels.icon',\r\n          stylers: [{ visibility: 'off' }]\r\n        },\r\n        {\r\n          featureType: 'transit.line',\r\n          elementType: 'geometry',\r\n          stylers: [{ visibility: 'off' }]\r\n        },\r\n        {\r\n          featureType: 'transit.line',\r\n          elementType: 'labels.text',\r\n          stylers: [{ visibility: 'off' }]\r\n        },\r\n        {\r\n          featureType: 'transit.station.airport',\r\n          elementType: 'geometry',\r\n          stylers: [{ visibility: 'off' }]\r\n        },\r\n        {\r\n          featureType: 'transit.station.airport',\r\n          elementType: 'labels',\r\n          stylers: [{ visibility: 'off' }]\r\n        },\r\n        {\r\n          featureType: 'water',\r\n          elementType: 'geometry',\r\n          stylers: [{ color: '#0f111a' }]\r\n        },\r\n        {\r\n          featureType: 'water',\r\n          elementType: 'labels',\r\n          stylers: [{ visibility: 'off' }]\r\n        }\r\n      ]\r\n    };\r\n\r\n    $googlemaps.forEach(itm => {\r\n      const mapElement = itm;\r\n      const mapOptions = {\r\n        zoom: 1.4,\r\n        minZoom: 1.4,\r\n        zoomControl: false,\r\n        scrollwheel: true,\r\n        disableDefaultUI: true,\r\n        center: new window.google.maps.LatLng(25.659195, 30.182691),\r\n        // styles: mapStyles.SnazzyCustomLight\r\n        styles:\r\n          window.config.config.phoenixTheme === 'dark'\r\n            ? mapStyles.SnazzyCustomDark\r\n            : mapStyles.SnazzyCustomLight\r\n      };\r\n\r\n      const map = new window.google.maps.Map(mapElement, mapOptions);\r\n      const infoWindow = new window.google.maps.InfoWindow();\r\n\r\n      const markers = leaftletPoints.map(point => {\r\n        const { name, location, street } = point;\r\n\r\n        const label = `\r\n        <h6 class=\"mb-1\">${name}</h6>\r\n        <p class=\"m-0 text-body-quaternary\">${street}, ${location}</p>\r\n      `;\r\n        const marker = new window.google.maps.Marker({\r\n          position: { lat: point.lat, lng: point.lng }\r\n        });\r\n\r\n        marker.addListener('click', () => {\r\n          infoWindow.setContent(label);\r\n          infoWindow.open(map, marker);\r\n        });\r\n        return marker;\r\n      });\r\n\r\n      const renderer = {\r\n        render: ({ count, position }) => {\r\n          let color = '#3874ff';\r\n          if (count > 10) {\r\n            color = '#e5780b';\r\n          }\r\n          if (count > 90) {\r\n            color = '#25b003';\r\n          }\r\n\r\n          const svg = window.btoa(`\r\n            <svg fill=\"${color}\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 240 240\">\r\n              <circle cx=\"120\" cy=\"120\" opacity=\".9\" r=\"70\" />\r\n              <circle cx=\"120\" cy=\"120\" opacity=\".3\" r=\"90\" />\r\n              <circle cx=\"120\" cy=\"120\" opacity=\".2\" r=\"110\" />\r\n            </svg>`);\r\n\r\n          return new window.google.maps.Marker({\r\n            label: { text: String(count), color: 'white', fontSize: '10px' },\r\n            position,\r\n            icon: {\r\n              url: `data:image/svg+xml;base64,${svg}`,\r\n              scaledSize: new window.google.maps.Size(45, 45)\r\n            },\r\n            // adjust zIndex to be above other markers\r\n            zIndex: Number(window.google.maps.Marker.MAX_ZINDEX) + count\r\n          });\r\n        }\r\n      };\r\n\r\n      themeController &&\r\n        themeController.addEventListener(\r\n          'clickControl',\r\n          ({ detail: { control, value } }) => {\r\n            if (control === 'phoenixTheme') {\r\n              map.set(\r\n                'styles',\r\n                value === 'dark'\r\n                  ? mapStyles.SnazzyCustomDark\r\n                  : mapStyles.SnazzyCustomLight\r\n              );\r\n            }\r\n          }\r\n        );\r\n      return new window.markerClusterer.MarkerClusterer({\r\n        markers,\r\n        map,\r\n        renderer\r\n      });\r\n    });\r\n  }\r\n};\r\n\r\nexport default revenueMapInit;\r\n", "import newCustomersChartsInit from '../theme/charts/echarts/new-customers';\r\nimport payingCustomerChartInit from '../theme/charts/echarts/paying-customer-chart';\r\nimport projectionVsActualChartInit from '../theme/charts/echarts/projection-vs-actual-chart';\r\nimport returningCustomerChartInit from '../theme/charts/echarts/returning-customer-chart';\r\nimport topCouponsChartInit from '../theme/charts/echarts/top-coupons-chart';\r\nimport totalOrdersChartInit from '../theme/charts/echarts/total-orders-chart';\r\nimport totalSalesChartInit from '../theme/charts/echarts/total-sales-chart';\r\nimport revenueMapInit from '../theme/revenue-map';\r\n\r\nconst { docReady } = window.phoenix.utils;\r\n\r\nwindow.revenueMapInit = revenueMapInit;\r\ndocReady(totalSalesChartInit);\r\ndocReady(newCustomersChartsInit);\r\ndocReady(topCouponsChartInit);\r\ndocReady(projectionVsActualChartInit);\r\ndocReady(returningCustomerChartInit);\r\ndocReady(payingCustomerChartInit);\r\ndocReady(totalOrdersChartInit);\r\n"], "names": ["echarts"], "mappings": ";;;;;EAAA;EACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;AAC3B;EACA;EACO,MAAM,eAAe,GAAG;EAC/B,EAAE,KAAK;EACP,EAAE,WAAW;EACb,EAAE,iBAAiB;EACnB,EAAE,iBAAiB;EACnB,KAAK;EACL,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACvD,EAAE,MAAM,YAAY,GAAG,OAAO,IAAI;EAClC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;EACzC,MAAM,IAAI,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE;EACjD,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;AAC3D;EACA,EAAE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,aAAa;EACrD,IAAI,yBAAyB;EAC7B,GAAG,CAAC;EACJ,EAAE,IAAI,oBAAoB,EAAE;EAC5B,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,MAAM;EAC1E,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;EACrB,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,MAAM,CAAC,MAAM;EACf,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;EACnB,IAAI,IAAI,iBAAiB,EAAE;EAC3B,MAAM,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACtC,KAAK;EACL,GAAG,CAAC,CAAC;EACL,EAAE,IAAI,iBAAiB,EAAE;EACzB,IAAI,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACpC,GAAG;AACH;EACA,EAAE,eAAe,CAAC,gBAAgB;EAClC,IAAI,cAAc;EAClB,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK;EACjC,MAAM,IAAI,OAAO,KAAK,cAAc,EAAE;EACtC,QAAQ,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;EAC1E,OAAO;EACP,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK;EACL,GAAG,CAAC;EACJ,CAAC,CAAC;EACF;AACA;EACA,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;EACvE,IAAI,UAAU,EAAE;EAChB,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,IAAI;EAC5B,IAAI,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI;EAC9C,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;EAC1B,MAAM,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;EAC1B,MAAM,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE,aAAa,CAAC,mBAAmB,CAAC,CAAC;EAChE,MAAM,IAAI,KAAK,EAAE;EACjB,QAAQ,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;EAC5C,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACO,MAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,aAAa,GAAG,QAAQ,KAAK;EACtE,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;EACvB,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI;EACvB,IAAI,WAAW,IAAI,CAAC;AACpB,2FAA2F;AAC3F,UAAU,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,KAAK;AACpD,SAAS;AACT,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG;AAC7B,MAAM,OAAO,EAAE,CAAC,KAAK,KAAK,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK;AAC3D,KAAK;AACL;AACA,YAAY,CAAC,CAAC;EACd,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,CAAC;AACV;AACA,cAAc;AACd,gBAAgB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;AAC3D,oBAAoB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;AAC3E,oBAAoB,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;AACvC,eAAe;AACf;AACA,YAAY,EAAE,WAAW,CAAC;AAC1B,gBAAgB,CAAC,CAAC;EAClB,CAAC,CAAC;AACF;EACO,MAAM,qBAAqB,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK;EAC/D;EACA,EAAE,IAAI,MAAM,CAAC,UAAU,IAAI,GAAG,EAAE;EAChC,IAAI,MAAM,aAAa,GAAG,GAAG,CAAC,YAAY,CAAC;EAC3C,IAAI,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;EACrD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EAC9D,IAAI,OAAO,GAAG,CAAC;EACf,GAAG;EACH,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;;EC5GD;EACA;EACA;AACA;EACA,MAAM,sBAAsB,GAAG,MAAM;EACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAC/D,EAAE,MAAM,yBAAyB,GAAG,QAAQ,CAAC,aAAa;EAC1D,IAAI,wBAAwB;EAC5B,GAAG,CAAC;EACJ,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;EAC1D,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC5E;EACA,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM;EACjD,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;EACxB,MAAM,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,WAAW;EAC9C,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;EACxB,KAAK,CAAC,CAAC,CAAC;AACR;EACA,IAAI,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;EACzB,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,KAAK;EAClC,MAAM,WAAW,IAAI,CAAC,mCAAmC;AACzD,QAAQ,KAAK,GAAG,CAAC,IAAI,MAAM;AAC3B,OAAO,gDAAgD,EAAE,EAAE,CAAC,KAAK,CAAC;AAClE,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,CAAC;AAC/C,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,CAAC;AACZ,cAAc,EAAE,WAAW,CAAC;AAC5B,kBAAkB,CAAC,CAAC;EACpB,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,yBAAyB,EAAE;EACjC,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,yBAAyB,EAAE,SAAS,CAAC,CAAC;EACtE,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;EACjE,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,EAAE;EACnB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,SAAS,EAAE,gBAAgB;EACnC,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,QAAQ;EACxB,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC;EAChC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC;EAChC,YAAY,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC/B,WAAW;EACX,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE;EAC1D,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EACpE,YAAY,YAAY,EAAE,IAAI;EAC9B,YAAY,YAAY,EAAE,KAAK;EAC/B,YAAY,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC9C,YAAY,KAAK,EAAE,MAAM;EACzB,YAAY,QAAQ,EAAE,CAAC;EACvB,YAAY,UAAU,EAAE,aAAa;EACrC,YAAY,UAAU,EAAE,GAAG;EAC3B,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,QAAQ,EAAE,QAAQ;EAC5B,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,IAAI,EAAE,QAAQ;EACxB,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC;EAChC,YAAY,IAAI,IAAI,CAAC,UAAU,CAAC;EAChC,YAAY,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC/B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EACpE,YAAY,QAAQ,EAAE,GAAG;EACzB,YAAY,YAAY,EAAE,IAAI;EAC9B,YAAY,YAAY,EAAE,KAAK;EAC/B,YAAY,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC9C,YAAY,KAAK,EAAE,OAAO;EAC1B,YAAY,UAAU,EAAE,aAAa;EACrC,YAAY,UAAU,EAAE,GAAG;EAC3B,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,WAAW,EAAE,KAAK;EAC5B,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,WAAW,EAAE,KAAK;EAC1B,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,CAAC;EACpB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,aAAa;EACb,WAAW;EACX,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,CAAC;EACpB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;EACrD,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECpJD,MAAM,WAAEA,SAAO,EAAE,GAAG,MAAM,CAAC;AAC3B;EACA;EACA;EACA;AACA;EACA,MAAM,uBAAuB,GAAG,MAAM;EACtC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,gCAAgC,CAAC,CAAC;AAC5E;EACA,EAAE,IAAI,QAAQ,EAAE;EAChB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;EACrD,IAAI,MAAM,KAAK,GAAGA,SAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACzC;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,MAAM,IAAI;EAC7B,UAAU,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3E,SAAS;EACT,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAC7B,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,IAAI,EAAE,iBAAiB;EACjC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,GAAG,EAAE,CAAC;EAChB,UAAU,GAAG,EAAE,GAAG;EAClB,UAAU,WAAW,EAAE,EAAE;EACzB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,QAAQ,EAAE,IAAI;EAC1B,YAAY,KAAK,EAAE,EAAE;EACrB,YAAY,SAAS,EAAE;EACvB,cAAc,UAAU,EAAE,CAAC;EAC3B,cAAc,WAAW,EAAE,OAAO;EAClC,aAAa;EACb,WAAW;EACX,UAAU,OAAO,EAAE;EACnB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,QAAQ,EAAE,IAAI;EAC1B,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC;EACzD,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,EAAE;EACvB,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECpFD;AACA;EACA;EACA;EACA;AACA;EACA,MAAM,2BAA2B,GAAG,MAAM;EAC1C,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACnE,EAAE,MAAM,0BAA0B,GAAG,QAAQ,CAAC,aAAa;EAC3D,IAAI,2BAA2B;EAC/B,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG,YAAY,CAAC,EAAE,CAAC,CAAC;AACjC;EACA,EAAE,MAAM,KAAK,GAAG;EAChB,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;EACxE,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,KAAK,GAAG;EAChB,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK;EACxE,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,0BAA0B,EAAE;EAClC,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;EACvE,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;AAClE;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;EAC3D,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,CAAC,mBAAmB,EAAE,gBAAgB,CAAC;EACrD,QAAQ,KAAK,EAAE,OAAO;EACtB,QAAQ,KAAK,EAAE,MAAM;EACrB,QAAQ,SAAS,EAAE,EAAE;EACrB,QAAQ,UAAU,EAAE,CAAC;EACrB,QAAQ,OAAO,EAAE,EAAE;EACnB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,aAAa,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACnD,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC;EACvC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,UAAU,EAAE,aAAa;EACnC;EACA,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB;EACA,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC5C,UAAU,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EAClE,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,UAAU,EAAE,aAAa;EACnC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,QAAQ,EAAE,IAAI;EACxB,SAAS;EACT,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,KAAK;EACvB,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACrC;EACA,QAAQ,QAAQ,EAAE,MAAM;EACxB,QAAQ,SAAS,EAAE;EACnB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE;EACnB,UAAU,UAAU,EAAE,aAAa;EACnC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,QAAQ,EAAE,IAAI;EACxB,UAAU,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC5C,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,aAAa,EAAE,QAAQ;EACjC,UAAU,SAAS,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;EAC1D,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,mBAAmB;EACnC,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,QAAQ,EAAE,KAAK;EACzB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAChC,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,gBAAgB;EAChC,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,QAAQ,EAAE,KAAK;EACzB,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAChC,UAAU,CAAC,EAAE,EAAE;EACf,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,YAAY,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EAC7C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,KAAK;EAClB,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,MAAM,SAAS,EAAE,KAAK;EACtB,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EChJM,MAAM,MAAM,GAAG;EACtB,EAAE,SAAS;EACX,EAAE,UAAU;EACZ,EAAE,OAAO;EACT,EAAE,OAAO;EACT,EAAE,KAAK;EACP,EAAE,MAAM;EACR,EAAE,MAAM;EACR,EAAE,QAAQ;EACV,EAAE,WAAW;EACb,EAAE,SAAS;EACX,EAAE,UAAU;EACZ,EAAE,UAAU;EACZ,CAAC,CAAC;AAgBF;EACO,MAAM,cAAc,GAAG;EAC9B,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,aAAa;EACvB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,gBAAgB;EAC9B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,MAAM,EAAE,gBAAgB;EAC5B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,KAAK;EACd,IAAI,IAAI,EAAE,CAAC,IAAI;EACf,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,OAAO;EAClB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,mBAAmB;EAC/B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,sBAAsB;EAClC,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,CAAC,MAAM;EACjB,IAAI,IAAI,EAAE,mBAAmB;EAC7B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,OAAO;EAChB,IAAI,IAAI,EAAE,MAAM;EAChB,IAAI,IAAI,EAAE,oBAAoB;EAC9B,IAAI,MAAM,EAAE,oBAAoB;EAChC,IAAI,QAAQ,EAAE,iBAAiB;EAC/B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,OAAO;EAChB,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,oBAAoB;EAC9B,IAAI,MAAM,EAAE,oBAAoB;EAChC,IAAI,QAAQ,EAAE,iBAAiB;EAC/B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,gBAAgB;EAC1B,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,gBAAgB;EAC1B,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,gBAAgB;EAC1B,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,MAAM;EACf,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,gBAAgB;EAC1B,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,OAAO;EAChB,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,gBAAgB;EAC1B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,SAAS;EACvB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,OAAO;EAChB,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,gBAAgB;EAC1B,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,SAAS;EACvB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,OAAO;EAChB,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,kBAAkB;EAC5B,IAAI,MAAM,EAAE,6BAA6B;EACzC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,OAAO;EAChB,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,kBAAkB;EAC5B,IAAI,MAAM,EAAE,6BAA6B;EACzC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,aAAa;EACvB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,IAAI,EAAE,kBAAkB;EAC5B,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,WAAW;EACzB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,aAAa;EACvB,IAAI,IAAI,EAAE,cAAc;EACxB,IAAI,IAAI,EAAE,kBAAkB;EAC5B,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,WAAW;EACzB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,iBAAiB;EAC7B,IAAI,QAAQ,EAAE,QAAQ;EACtB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,gBAAgB;EAC1B,IAAI,MAAM,EAAE,4BAA4B;EACxC,IAAI,QAAQ,EAAE,OAAO;EACrB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,UAAU;EACpB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,UAAU;EACpB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,UAAU;EACpB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,QAAQ;EAClB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,sBAAsB;EAClC,IAAI,QAAQ,EAAE,UAAU;EACxB,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,IAAI;EACd,IAAI,IAAI,EAAE,OAAO;EACjB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,CAAC,SAAS;EACnB,IAAI,IAAI,EAAE,SAAS;EACnB,IAAI,IAAI,EAAE,eAAe;EACzB,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,YAAY;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,QAAQ;EACjB,IAAI,IAAI,EAAE,CAAC,UAAU;EACrB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,UAAU;EACrB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,UAAU;EACrB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,OAAO;EAChB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,UAAU;EACrB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,UAAU;EACrB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,UAAU;EACrB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,UAAU;EACrB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,UAAU;EACrB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,QAAQ;EACjB,IAAI,IAAI,EAAE,CAAC,UAAU;EACrB,IAAI,IAAI,EAAE,iBAAiB;EAC3B,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,eAAe;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,QAAQ;EACnB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,IAAI;EACb,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,KAAK;EAChB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,KAAK;EACd,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,yBAAyB;EACnC,IAAI,MAAM,EAAE,qBAAqB;EACjC,IAAI,QAAQ,EAAE,aAAa;EAC3B,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,KAAK;EACf,GAAG;EACH,EAAE;EACF,IAAI,GAAG,EAAE,SAAS;EAClB,IAAI,IAAI,EAAE,CAAC,SAAS;EACpB,IAAI,IAAI,EAAE,KAAK;EACf,GAAG;EACH,CAAC;;EC5oCD;EACA;EACA;EACA,MAAM,WAAEA,SAAO,EAAE,GAAG,MAAM,CAAC;AAC3B;EACA,MAAM,0BAA0B,GAAG,MAAM;EACzC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AACrD;EACA,EAAE,MAAM,uBAAuB,GAAG,QAAQ,CAAC,aAAa;EACxD,IAAI,4BAA4B;EAChC,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,uBAAuB,EAAE;EAC/B,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC;EACpE,IAAI,MAAM,KAAK,GAAGA,SAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;EACxD,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAC1C,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE;EACd,UAAU;EACV,YAAY,IAAI,EAAE,aAAa;EAC/B,YAAY,IAAI,EAAE,WAAW;EAC7B,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,eAAe,CAAC;EAC9C,cAAc,WAAW,EAAE,CAAC;EAC5B,aAAa;EACb,WAAW;EACX,UAAU;EACV,YAAY,IAAI,EAAE,YAAY;EAC9B,YAAY,IAAI,EAAE,WAAW;EAC7B,YAAY,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;EAC1E,WAAW;EACX,UAAU;EACV,YAAY,IAAI,EAAE,aAAa;EAC/B,YAAY,IAAI,EAAE,WAAW;EAC7B,YAAY,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE;EACrE,WAAW;EACX,SAAS;AACT;EACA,QAAQ,KAAK,EAAE,OAAO;EACtB,QAAQ,KAAK,EAAE,MAAM;EACrB,QAAQ,SAAS,EAAE,EAAE;EACrB,QAAQ,UAAU,EAAE,CAAC;EACrB,QAAQ,OAAO,EAAE,EAAE;EACnB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,aAAa,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACnD,QAAQ,mBAAmB,EAAE,CAAC;EAC9B,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC;EACvC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,UAAU,EAAE,aAAa;EACnC,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,gBAAgB;EACnC,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,aAAa,CAAC,EAAE;EACvD,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB;EACA,UAAU,YAAY,EAAE,KAAK;EAC7B,UAAU,YAAY,EAAE,KAAK;EAC7B,UAAU,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC5C,UAAU,SAAS,EAAE,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EAC/C,UAAU,UAAU,EAAE,aAAa;EACnC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,QAAQ,EAAE,IAAI;EACxB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;EACxE,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,YAAY,EAAE,IAAI;EAC5B,UAAU,YAAY,EAAE,IAAI;EAC5B,UAAU,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC5C,UAAU,SAAS,EAAE,KAAK,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACzC,UAAU,UAAU,EAAE,aAAa;EACnC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,QAAQ,EAAE,IAAI;EACxB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE;EACxD,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,aAAa;EAC7B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAChE,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,QAAQ;EAC1B,YAAY,KAAK,EAAE,CAAC;EACpB,YAAY,KAAK,EAAE,QAAQ,CAAC,eAAe,CAAC;EAC5C,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,WAAW,EAAE,QAAQ,CAAC,eAAe,CAAC;EAClD,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,YAAY;EAC5B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAChE,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,CAAC;EACpB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EACjD,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,aAAa;EAC7B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;EAChE,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,CAAC;EACpB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE;EAC5E,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC3LD;AAEA;EACA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;AAC3B;EACA;EACA;EACA;AACA;EACA,MAAM,mBAAmB,GAAG,MAAM;EAClC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,kBAAkB,GAAG,qBAAqB,CAAC;EACnD,EAAE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;AACvE;EACA,EAAE,IAAI,iBAAiB,EAAE;EACzB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;EAC9D,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAClD;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,QAAQ,QAAQ,CAAC,iBAAiB,CAAC;EACnC,QAAQ,QAAQ,CAAC,WAAW,CAAC;EAC7B,OAAO;AACP;EACA,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE;EAChD,UAAU,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC;EAC3C,UAAU,IAAI,MAAM,CAAC,UAAU,GAAG,GAAG,EAAE;EACvC,YAAY,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;EAChD,cAAc,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EACrC,aAAa,MAAM;EACnB,cAAc,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EAC3D,aAAa;EACb,WAAW,MAAM;EACjB,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EACtE,WAAW;EACX,UAAU,OAAO,GAAG,CAAC;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE,MAAM,IAAI;EAC7B,UAAU,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;EAC5E,SAAS;EACT,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,MAAM,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAC7B,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,CAAC,MAAM,EAAE,KAAK,CAAC;EACjC,UAAU,iBAAiB,EAAE,KAAK;EAClC,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,KAAK;EACxB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,SAAS;EAC9B,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,WAAW,EAAE,CAAC;EAC1B,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,QAAQ,EAAE,QAAQ;EAC9B,YAAY,SAAS,EAAE,KAAK;EAC5B,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC;EAClD,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,qBAAqB,EAAE;EAC3D,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,qBAAqB,EAAE;EAC3D,YAAY,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,wBAAwB,EAAE;EAC9D,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,YAAY,EAAE,IAAI,EAAE;EAClC,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECrFD;AACA;EACA;EACA;EACA;AACA;EACA,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAC/D,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;AAC5E;EACA,EAAE,IAAI,kBAAkB,EAAE;EAC1B,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,kBAAkB,EAAE,SAAS,CAAC,CAAC;EAC/D,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;AAC1D;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAChC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,MAAM,IAAI;EAC7B,UAAU,OAAO,CAAC,QAAQ,EAAE,MAAM;AAClC,aAAa,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AAC/B,aAAa,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EAC1D,SAAS;EACT,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,QAAQ;EACtB,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;EAC9B,UAAU,IAAI,IAAI,CAAC,UAAU,CAAC;EAC9B,UAAU,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC7B,SAAS;EACT,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE;EACxD,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EAClE,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,YAAY,EAAE,IAAI;EAC5B,UAAU,YAAY,EAAE,IAAI;EAC5B,UAAU,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC5C,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,WAAW,EAAE,KAAK;EAC1B,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,QAAQ,EAAE,KAAK;EACzB,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;EACjD,UAAU,cAAc,EAAE,IAAI;EAC9B,UAAU,MAAM,EAAE,MAAM;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,EAAE;EAC5B,WAAW;EACX,UAAU,eAAe,EAAE;EAC3B,YAAY,YAAY,EAAE,EAAE;EAC5B,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;EACtD,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EClFD;EACA;EACA;AACA;EACA,MAAM,mBAAmB,GAAG,MAAM;EAClC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAC/D,EAAE,MAAM,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;AAC/E;EACA;AACA;EACA,EAAE,MAAM,KAAK,GAAG,QAAQ;EACxB,IAAI,IAAI,IAAI,CAAC,UAAU,CAAC;EACxB,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC;EACzB,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EACvB,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,gBAAgB,GAAG;EAC3B,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC7E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC/E,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,aAAa,GAAG;EACxB,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EACpE,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;EAC1D,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC5E;EACA,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM;EACjD,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;EACxB,MAAM,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,WAAW;EAC9C,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;EACxB,KAAK,CAAC,CAAC,CAAC;AACR;EACA,IAAI,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;EACzB,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,KAAK;EAClC,MAAM,WAAW,IAAI,CAAC,mCAAmC;AACzD,QAAQ,KAAK,GAAG,CAAC,IAAI,MAAM;AAC3B,OAAO,gDAAgD,EAAE,EAAE,CAAC,KAAK,CAAC;AAClE,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,KAAK,CAAC;AAC/C,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,CAAC;AACZ,cAAc,EAAE,WAAW,CAAC;AAC5B,kBAAkB,CAAC,CAAC;EACpB,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,gBAAgB,EAAE;EACxB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,gBAAgB,EAAE,SAAS,CAAC,CAAC;EAC7D,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACxD;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;EACpD,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,EAAE;EACnB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,CAAC,EAAE,CAAC;EACd,SAAS;EACT,QAAQ,SAAS,EAAE,gBAAgB;EACnC,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EACpE,YAAY,QAAQ,EAAE,EAAE;EACxB,YAAY,YAAY,EAAE,IAAI;EAC9B,YAAY,YAAY,EAAE,KAAK;EAC/B,YAAY,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC9C,YAAY,KAAK,EAAE,MAAM;EACzB,YAAY,UAAU,EAAE,aAAa;EACrC,YAAY,UAAU,EAAE,GAAG;EAC3B,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,QAAQ,EAAE,CAAC;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK;EACnB,gBAAgB,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,KAAK,MAAM;EAC5D,oBAAoB,QAAQ,CAAC,mBAAmB,CAAC;EACjD,oBAAoB,QAAQ,CAAC,cAAc,CAAC;EAC5C,aAAa;EACb,WAAW;EACX,UAAU,WAAW,EAAE,KAAK;EAC5B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,QAAQ,EAAE,QAAQ;EAC5B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EACpE,YAAY,QAAQ,EAAE,GAAG;EACzB,YAAY,YAAY,EAAE,IAAI;EAC9B,YAAY,YAAY,EAAE,KAAK;EAC/B,YAAY,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC9C,YAAY,KAAK,EAAE,OAAO;EAC1B,YAAY,UAAU,EAAE,aAAa;EACrC,YAAY,UAAU,EAAE,GAAG;EAC3B,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,WAAW,EAAE,KAAK;EAC5B,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,EAAE,OAAO;EACzB,QAAQ,WAAW,EAAE,EAAE,IAAI,EAAE,MAAM,EAAE;EACrC,QAAQ,QAAQ,EAAE,MAAM;EACxB,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,QAAQ,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EACjC,QAAQ,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE;EAClC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,GAAG;EACnB,UAAU,IAAI,EAAE,MAAM;EACtB;EACA;EACA;EACA,UAAU,IAAI,EAAE,gBAAgB;EAChC,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,GAAG;EACnB,UAAU,IAAI,EAAE,MAAM;EACtB;EACA;EACA;EACA,UAAU,IAAI,EAAE,aAAa;EAC7B;EACA,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,QAAQ;EAC1B,YAAY,KAAK,EAAE,CAAC;EACpB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,WAAW;EACX,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,MAAM;EACtB,QAAQ,GAAG,EAAE,IAAI;EACjB,QAAQ,YAAY,EAAE,KAAK;EAC3B,OAAO;EACP,MAAM,SAAS,EAAE,KAAK;EACtB,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC1LD;EAEA;EACA;EACA;AACA;EACA,MAAM,cAAc,GAAG,MAAM;EAC7B,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;EACxC,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;EAChE,EAAE,IAAI,WAAW,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;EAC3C;EACA,IAAI,MAAM,SAAS,GAAG;EACtB,MAAM,iBAAiB,EAAE;EACzB,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,IAAI;EAC9B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,kBAAkB;EACzC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,KAAK,EAAE,SAAS;EAC9B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,oBAAoB;EAC3C,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,aAAa;EACpC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,wBAAwB;EAC/C,UAAU,WAAW,EAAE,iBAAiB;EACxC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,IAAI;EAC9B,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,SAAS;EAC9B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,yBAAyB;EAChD,UAAU,WAAW,EAAE,iBAAiB;EACxC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,WAAW;EAClC,UAAU,WAAW,EAAE,UAAU;EACjC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,IAAI;EAC9B,aAAa;EACb,YAAY;EACZ,cAAc,KAAK,EAAE,SAAS;EAC9B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,mBAAmB;EAC1C,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,MAAM;EAC7B,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,KAAK,EAAE,SAAS;EAC9B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,MAAM;EAC7B,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,SAAS;EAChC,UAAU,WAAW,EAAE,aAAa;EACpC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,cAAc;EACrC,UAAU,WAAW,EAAE,UAAU;EACjC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,cAAc;EACrC,UAAU,WAAW,EAAE,aAAa;EACpC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,yBAAyB;EAChD,UAAU,WAAW,EAAE,UAAU;EACjC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,yBAAyB;EAChD,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,OAAO;EAC9B,UAAU,WAAW,EAAE,UAAU;EACjC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,KAAK,EAAE,SAAS;EAC9B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,OAAO;EAC9B,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,gBAAgB,EAAE;EACxB,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;EACzC,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,kBAAkB;EACzC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,KAAK,EAAE,SAAS;EAC9B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,oBAAoB;EAC3C,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,gBAAgB;EACvC,UAAU,WAAW,EAAE,aAAa;EACpC,UAAU,OAAO,EAAE;EACnB,YAAY;EACZ,cAAc,UAAU,EAAE,KAAK;EAC/B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,wBAAwB;EAC/C,UAAU,WAAW,EAAE,iBAAiB;EACxC,UAAU,OAAO,EAAE;EACnB,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE;EAChC,YAAY;EACZ,cAAc,KAAK,EAAE,SAAS;EAC9B,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,yBAAyB;EAChD,UAAU,WAAW,EAAE,iBAAiB;EACxC,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,WAAW;EAClC,UAAU,WAAW,EAAE,UAAU;EACjC,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;EAC/D,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,mBAAmB;EAC1C,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,MAAM;EAC7B,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;EACzC,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,MAAM;EAC7B,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,SAAS;EAChC,UAAU,WAAW,EAAE,aAAa;EACpC,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,cAAc;EACrC,UAAU,WAAW,EAAE,UAAU;EACjC,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,cAAc;EACrC,UAAU,WAAW,EAAE,aAAa;EACpC,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,yBAAyB;EAChD,UAAU,WAAW,EAAE,UAAU;EACjC,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,yBAAyB;EAChD,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,OAAO;EAC9B,UAAU,WAAW,EAAE,UAAU;EACjC,UAAU,OAAO,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC;EACzC,SAAS;EACT,QAAQ;EACR,UAAU,WAAW,EAAE,OAAO;EAC9B,UAAU,WAAW,EAAE,QAAQ;EAC/B,UAAU,OAAO,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,CAAC;EAC1C,SAAS;EACT,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,WAAW,CAAC,OAAO,CAAC,GAAG,IAAI;EAC/B,MAAM,MAAM,UAAU,GAAG,GAAG,CAAC;EAC7B,MAAM,MAAM,UAAU,GAAG;EACzB,QAAQ,IAAI,EAAE,GAAG;EACjB,QAAQ,OAAO,EAAE,GAAG;EACpB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,WAAW,EAAE,IAAI;EACzB,QAAQ,gBAAgB,EAAE,IAAI;EAC9B,QAAQ,MAAM,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,SAAS,CAAC;EACnE;EACA,QAAQ,MAAM;EACd,UAAU,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,YAAY,KAAK,MAAM;EACtD,cAAc,SAAS,CAAC,gBAAgB;EACxC,cAAc,SAAS,CAAC,iBAAiB;EACzC,OAAO,CAAC;AACR;EACA,MAAM,MAAM,GAAG,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;EACrE,MAAM,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;AAC7D;EACA,MAAM,MAAM,OAAO,GAAG,cAAc,CAAC,GAAG,CAAC,KAAK,IAAI;EAClD,QAAQ,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC;AACjD;EACA,QAAQ,MAAM,KAAK,GAAG,CAAC;AACvB,yBAAyB,EAAE,IAAI,CAAC;AAChC,4CAA4C,EAAE,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC;AAClE,MAAM,CAAC,CAAC;EACR,QAAQ,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;EACrD,UAAU,QAAQ,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,CAAC,GAAG,EAAE;EACtD,SAAS,CAAC,CAAC;AACX;EACA,QAAQ,MAAM,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM;EAC1C,UAAU,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;EACvC,UAAU,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;EACvC,SAAS,CAAC,CAAC;EACX,QAAQ,OAAO,MAAM,CAAC;EACtB,OAAO,CAAC,CAAC;AACT;EACA,MAAM,MAAM,QAAQ,GAAG;EACvB,QAAQ,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK;EACzC,UAAU,IAAI,KAAK,GAAG,SAAS,CAAC;EAChC,UAAU,IAAI,KAAK,GAAG,EAAE,EAAE;EAC1B,YAAY,KAAK,GAAG,SAAS,CAAC;EAC9B,WAAW;EACX,UAAU,IAAI,KAAK,GAAG,EAAE,EAAE;EAC1B,YAAY,KAAK,GAAG,SAAS,CAAC;EAC9B,WAAW;AACX;EACA,UAAU,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AACnC,uBAAuB,EAAE,KAAK,CAAC;AAC/B;AACA;AACA;AACA,kBAAkB,CAAC,CAAC,CAAC;AACrB;EACA,UAAU,OAAO,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;EAC/C,YAAY,KAAK,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAE;EAC5E,YAAY,QAAQ;EACpB,YAAY,IAAI,EAAE;EAClB,cAAc,GAAG,EAAE,CAAC,0BAA0B,EAAE,GAAG,CAAC,CAAC;EACrD,cAAc,UAAU,EAAE,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC;EAC7D,aAAa;EACb;EACA,YAAY,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,KAAK;EACxE,WAAW,CAAC,CAAC;EACb,SAAS;EACT,OAAO,CAAC;AACR;EACA,MAAM,eAAe;EACrB,QAAQ,eAAe,CAAC,gBAAgB;EACxC,UAAU,cAAc;EACxB,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,KAAK,EAAE,EAAE,KAAK;EAC9C,YAAY,IAAI,OAAO,KAAK,cAAc,EAAE;EAC5C,cAAc,GAAG,CAAC,GAAG;EACrB,gBAAgB,QAAQ;EACxB,gBAAgB,KAAK,KAAK,MAAM;EAChC,oBAAoB,SAAS,CAAC,gBAAgB;EAC9C,oBAAoB,SAAS,CAAC,iBAAiB;EAC/C,eAAe,CAAC;EAChB,aAAa;EACb,WAAW;EACX,SAAS,CAAC;EACV,MAAM,OAAO,IAAI,MAAM,CAAC,eAAe,CAAC,eAAe,CAAC;EACxD,QAAQ,OAAO;EACf,QAAQ,GAAG;EACX,QAAQ,QAAQ;EAChB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;EACP,GAAG;EACH,CAAC;;ECtYD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1C;EACA,MAAM,CAAC,cAAc,GAAG,cAAc,CAAC;EACvC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EAC9B,QAAQ,CAAC,sBAAsB,CAAC,CAAC;EACjC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EAC9B,QAAQ,CAAC,2BAA2B,CAAC,CAAC;EACtC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACrC,QAAQ,CAAC,uBAAuB,CAAC,CAAC;EAClC,QAAQ,CAAC,oBAAoB,CAAC;;;;;;"}