@inject NavigationManager NavigationManagerObj
@code {
    [CascadingParameter]
    public ComponentPassObj CObj { get; set; } = new();

    [CascadingParameter] IMudDialogInstance? MudDialog { get; set; }

    eZeroCore.Web.Column col = new("COLABE58A0CCC");
    protected override Task OnInitializedAsync()
    {
        return base.OnInitializedAsync();
    }

    async Task Ok()
    {
        if (!CObj.IsLogin)
        {
            NavigationManagerObj.NavigateTo($"/register?from={CObj.ThisUrl_NotLoginOrRegister}", true);
        }
        else
        {
            NavigationManagerObj.NavigateTo("/plan", true);
        }
        /*
        if (MudDialog is not null)
        {

            //await eZeroCore.Web.Document.SortingNew2025(Ids);
            MudDialog.Close(DialogResult.Ok<string>("ok"));
            await Task.CompletedTask;
        }
        */
    }

    private void Cancel()
    {
        if (MudDialog is not null)
        {
            MudDialog.Cancel();
        }
    }
}

<MudDialogContent ActionsClass="d-grid gap-2 col-10 col-md-8 mx-auto">
    <ChildContent>
        <div class="ratio ratio-21x9 bgImg" style="background-image:url(@col.TitlePhotoUrls)"></div>
        <div class="mt-2 text-body-emphasis">
            @if (CObj.IsLogin)
            {
                @(new MarkupString(col.ColumnContent))
            }
            else
            {
                @(new MarkupString(col.ColumnContentSummary))
            }
        </div>
    </ChildContent>
    <MudDialogActions>
        <Ez_Btn Color="General.Color.warning" OnBtnClick="Ok">
            @if (CObj.IsLogin)
            {
                @CObj.UserLang.GetLangValue("升级服务")
            }
            else
            {
                @CObj.UserLang.GetLangValue("确定")
            }
        </Ez_Btn>
    </MudDialogActions>
</MudDialogContent>