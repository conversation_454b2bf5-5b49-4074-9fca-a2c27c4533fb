@using System.Text
@using System.Globalization
@inject IJSRuntime JS
@inject NavigationManager NavManagerObj
@using eZeroCore.Web.Stk.Data
@using AspNetCoreWeb.Blazor.CityV3.UI.RzDataGrid
@using <PERSON><PERSON><PERSON>
@code {
    [CascadingParameter]
    public ComponentPassObj CObj { get; set; } = new();
    [Parameter]
    public int PageSize { get; set; } = 18;
    [Parameter]
    public bool AllowPaging { get; set; } = true;

    [Parameter]
    public EventCallback<string> OnSelectedStock { get; set; }

    Settings settings = new();

    protected override async Task OnInitializedAsync()
    {
        await Init();
        //return base.OnInitializedAsync();
    }

    public async Task Init(bool onSelect = false)
    {
        await Generic.Loading(JS);
        if (SelectedModal is null)
        {
            SelectedModal = aIModels.FirstOrDefault() ?? new();
        }
        await LoadData();
        if (onSelect)
        {
            try
            {
                await SelectFirstStock();
            }
            catch { }
        }
        await Generic.Loading(JS, false);
    }

    RzDataTable<Stock_Quant.Data> rzDataTable;
    List<Stock_Quant.Data> Datas = [];
    bool isLoading = false;
    int TotalCount { get; set; }
    int lastPageNumber { get; set; }
    Stock_Quant.List listProperty { get; set; } = new()
        {
            PageSize = 300
        };
    async Task LoadData(LoadDataArgs? args = null)
    {
        isLoading = true;
        //StateHasChanged();
        DateTime dateTimeStart = WebDefaultValue.GetLastOpenMarketDateTime(useCache: true, timeout: 300);
        Stock_Quant.List list = new()
            {
                PageSize = listProperty.PageSize,
                Stocks = listProperty.Stocks,
                MinPrice = listProperty.MinPrice,
                MaxPrice = listProperty.MaxPrice,
                MinChangePercent = listProperty.MinChangePercent,
                MaxChangePercent = listProperty.MaxChangePercent,
                MinVolaccu = listProperty.MinVolaccu,
                MaxVolaccu = listProperty.MaxVolaccu,
                Type = Convert.ToInt32(SelectedModal?.Model),
                CreateDate = dateTimeStart
            };
        Datas = await list.DataTableListAsync(args, PageSize);
        TotalCount = list.TotalCount;

        if (lastPageNumber != list.SetPageNumber)
        {
            lastPageNumber = list.SetPageNumber;
            await SelectFirstStock();
        }
        isLoading = false;
    }

    async Task SelectFirstStock()
    {
        try
        {
            await ClickSelectStock(Datas.FirstOrDefault()!.Stock.ToUpper());
        }
        catch { }
    }

    string Stocks
    {
        get
        {
            return string.Join(',', listProperty.Stocks);
        }
        set
        {
            if (!string.IsNullOrWhiteSpace(value))
            {
                string[] items = value.Split(',').Select(items => items.Trim()).ToArray();
                listProperty.Stocks = new(items);
            }
            else
            {
                listProperty.Stocks.Clear();
            }
        }
    }
    public string SelectedStock { get; set; } = "";
    async Task ClickSelectStock(string stock)
    {
        //await JS.InvokeVoidAsync("console.log", $"{stock}");
        if (!SelectedStock.Equals(stock))
        {
            SelectedStock = stock;
            if (rzDataTable is not null)
            {
                await rzDataTable.SetSelectedItem(Datas.Find(d => d.Stock.Equals(SelectedStock, StringComparison.OrdinalIgnoreCase)));
            }
            await OnSelectedStock.InvokeAsync(SelectedStock);
        }
    }
    async Task OnSelected(Stock_Quant.Data? data)
    {
        if (data is not null)
        {
            if (AppSettings.IsTestModeAll)
            {
                await JS.InvokeVoidAsync("console.log", $"OnSelected:【{data.Stock}】");
            }
            await OnSelectedStock.InvokeAsync(data.Stock.ToUpper());
        }
        await Task.CompletedTask;
    }

    AIModel? SelectedModal { get; set; }

    async Task OnSelectedModel(AIModel model)
    {
        SelectedModal = model;
        await Task.Delay(0);
        await Init(true);
    }



}

<div class="rz-form-field-content px-2 p-1 mb-2 w-100 Selector">
    <div class="dropdown w-100">
        <div class="dropdown-toggle w-100 hand" data-bs-toggle="dropdown" aria-expanded="false">
            <span class="dropdownMenusModalName">
                @SelectedModal?.GetLocalizedName(CObj)
            </span>
            <b class="golden-text ms-2">
                @SelectedModal?.SubTitle
            </b>
        </div>
        <ul class="dropdown-menu">
            @foreach (AIModel md in aIModels)
            {
                <li @key=md.GetHashCode()>
                    <button @onclick="async ()=>{ await OnSelectedModel(md); }"
                            class="dropdown-item @(md.Model == SelectedModal?.Model?"Active":"")" type="button">
                        <span class="dropdownMenusModalName">
                            @md.GetLocalizedName(CObj)
                        </span>
                        <b class="golden-text ms-2">
                            @md.SubTitle
                        </b>
                    </button>
                </li>
            }
        </ul>
    </div>
</div>

<div class="card">
    <div class="card-body p-1 position-relative">
        <div class="position-relative d-none">
            <hr class="bg-body-secondary mb-4">
            <div class="divider-content-center">
                <i class="fal fa-filter me-1"></i>
                @CObj.UserLang.GetLangValue("过滤器")
            </div>
        </div>
        <div class="input-group mb-3">
            <span class="input-group-text">
                @CObj.UserLang.GetLangValue("股价范围"):
            </span>
            <input type="text" @bind-value=@listProperty.MinPrice class="form-control" placeholder="@CObj.UserLang.GetLangValue("最小值")" />
            <span class="input-group-text">
                ~
            </span>
            <input type="text" @bind-value=@listProperty.MaxPrice class="form-control" placeholder="@CObj.UserLang.GetLangValue("最大值")" />
        </div>

        <div class="input-group mb-3">
            <span class="input-group-text">
                @CObj.UserLang.GetLangValue("成交量范围"):
            </span>
            <input type="text" @bind-value=@listProperty.MinVolaccu class="form-control" placeholder="@CObj.UserLang.GetLangValue("最小值")" />
            <span class="input-group-text">
                ~
            </span>
            <input type="text" @bind-value=@listProperty.MaxVolaccu class="form-control" placeholder="@CObj.UserLang.GetLangValue("最大值")" />
        </div>

        <div class="d-grid gap-2 mb-2 position-absolute top-0 end-0" style="margin-top: -2.8rem;">
            <Ez_Btn OnBtnClick="async ()=>{ await LoadData();}" ClassName="btn btn-phoenix-success btn-sm me-1 mb-1">
                @CObj.UserLang.GetLangValue("应用过滤器")
            </Ez_Btn>
        </div>



    </div>
</div>

<RzDataTable @ref=rzDataTable TItem="Stock_Quant.Data"
             PageNumbersCount="3"
             OverflowXScroll="true"
             TheadVisable="true"
             AllowRowSelectOnRowClick=true
             ShowPagingSummary="false"
             PagerAlignCenter="true"
             AllowPaging="@AllowPaging"
             Slim="true"
             PageArgsEnabled="true"
             Data="@Datas" PageSize="@PageSize"
             IsLoading="@isLoading"
             OnSelected="@OnSelected"
             Count="@TotalCount" LoadData="@LoadData">

    <RadzenDataGridColumn TItem="Stock_Quant.Data" Sortable="false" Title="@CObj.UserLang.GetLangValue("扫描时间(美东)")"
                          CssClass="hand" Property="CreateDate" Frozen="true" Width="102px">
        <Template Context="data">
            @data.CreateDate.ToString("MM/dd HH:mm")
        </Template>
    </RadzenDataGridColumn>
    <RadzenDataGridColumn TItem="Stock_Quant.Data" Sortable="false" Title="@CObj.UserLang.GetLangValue("代号")"
                          CssClass="hand" Property="Stock" Frozen="true" Width="102px">
        <Template Context="data">
            <div class="badge badge-phoenix fs-8 badge-phoenix-warning my-1" style="width:80px;">
                @data.Stock
            </div>
        </Template>
    </RadzenDataGridColumn>
    <RadzenDataGridColumn TItem="Stock_Quant.Data" Sortable="false" Title="@CObj.UserLang.GetLangValue("股价")" Width="78px"
                          CssClass="hand" Property="Close">
        <Template Context="data">
            <div class="text-warning-light fs-8 fw-bold">
                @eZeroCore.ENumeric.OutMonetaryStr(data.Close, true).Replace("$", "")
            </div>
        </Template>
    </RadzenDataGridColumn>
    <RadzenDataGridColumn TItem="Stock_Quant.Data" Sortable="false" Title="@CObj.UserLang.GetLangValue("波动百分比")" Width="88px"
                          CssClass="hand" Property="ChangePercent">
        <Template Context="data">
            <div class="text-warning-info fw-bold">
                @((data.ChangePercent).ToString("N2"))%
            </div>
        </Template>
    </RadzenDataGridColumn>
    <RadzenDataGridColumn TItem="Stock_Quant.Data" Sortable="false" Title="@CObj.UserLang.GetLangValue("日均成交量")" Width="108px"
                          CssClass="hand" Property="VolAccu">
        <Template Context="data">
            <div class="text-warning-info fw-bold">
                @eZeroCore.ENumeric.OutMonetaryStr(data.AvgVolume, true, N: 0).Replace("$", "")
            </div>
        </Template>
    </RadzenDataGridColumn>

</RzDataTable>

<div class="mt-3">
    @(new MarkupString(eZeroCore.Web.Column.GetColumnContent("COL2D08DB9B19")!.Replace("<br/>", "")))
</div>

@code {

    List<AIModel> aIModels
    {
        get
        {
            return [
                new AIModel(){
            Name="突破上行模型",
            SubTitle="",
            Model=1
                },
            new AIModel(){
            Name="突破下行模型",
            SubTitle="",
            Model=2
                },
            new AIModel(){
            Name="双顶模型",
            SubTitle="",
            Model=3
                },
            new AIModel(){
            Name="双底模型",
            SubTitle="",
            Model=4
                }

            ];
        }
    }

    public class AIModel
    {
        public string Name { get; set; } = "";
        public string SubTitle { get; set; } = "";
        public decimal Model { get; set; } = 1;

        public string GetLocalizedName(ComponentPassObj cObj)
        {
            return cObj.UserLang.GetLangValue(Name);
        }
    }
}