@using eZeroCore.Web.Stk;
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }


    [Parameter]
    public EventCallback<int> OnSelectedIndex { get; set; }

    [Parameter]
    public bool SetDisabled { get; set; }

    [Parameter]
    public ClientManager.StockClient.StatusEnumCn SelectedStatus { get; set; } = ClientManager.StockClient.StatusEnumCn.None;

    [Parameter]
    public int StartIndex { get; set; } = 0;

    [Parameter]
    public string NoneName { get; set; } = "&nbsp;";

    [Parameter]
    public bool ScrollBar { get; set; }

    [Parameter]
    public int MaxHeight { get; set; } = 330;

    protected override async Task OnInitializedAsync()
    {
        await Init();
    }

    public async Task Init(bool ClearSelector = false)
    {
        if (ClearSelector)
        {
            SelectedStatus = ClientManager.StockClient.StatusEnumCn.None;
        }
        SelectedTitle = eZeroCore.Web.Stk.ClientManager.StockClient.GetStatusItemName(SelectedStatus, NoneName);
        LastSelectedIndex = (int)SelectedStatus;
        StateHasChanged();
        await Task.Delay(0);
    }

    (string v, string c) SelectedTitle { get; set; } = new();
    int LastSelectedIndex { get; set; }

    async Task StatusSelected(int i)
    {
        if (i != LastSelectedIndex)
        {
            await OnSelectedIndex.InvokeAsync(i);
            SelectedStatus = (ClientManager.StockClient.StatusEnumCn)i;
            await Init();
            LastSelectedIndex = i;
        }
    }
    List<(int v, string t)> StatusValueItems()
    {
        List<(int v, string t)> items = [];
        for (int i = StartIndex; i < Enum.GetNames(typeof(ClientManager.StockClient.StatusEnumCn)).Length - 1; i++)
        {
            string sItemStr = ((ClientManager.StockClient.StatusEnumCn)i).ToString();
            if (!sItemStr.StartsWith('_'))
            {
                items.Add((i, sItemStr));
            }
        }
        return items;
    }
}

<div class="dropdown w-100 p-1 @LastSelectedIndex">
    <div class="dropdown-toggle w-100 hand @(SetDisabled?"disabled":"") serverSelector" data-bs-toggle="dropdown" aria-expanded="false">
        <span class="@(SelectedTitle.c) ps-2">
            @SelectedTitle.v
        </span>
    </div>
    <ul class="dropdown-menu" style="@(ScrollBar?$"max-height:{MaxHeight}px; overflow-y: auto;":"")">
        @foreach ((int v, string t) item in StatusValueItems())
        {
            (string v, string c) sValue = eZeroCore.Web.Stk.ClientManager.StockClient.GetStatusItemName((ClientManager.StockClient.StatusEnumCn)item.v, NoneName);
            <li @key=item.GetHashCode()>
                <button class="dropdown-item @(item.v == Convert.ToInt32(SelectedStatus)?"Active":"") v@(item.v)" type="button" @onclick="()=>{_=StatusSelected(item.v);}">
                    <span class="@(sValue.c)">
                        @sValue.v
                    </span>
                    <sub class="opacity-25">
                        @(item.v)
                    </sub>
                </button>
            </li>
        }
    </ul>
</div>
