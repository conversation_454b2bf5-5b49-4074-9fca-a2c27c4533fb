﻿@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using eZeroCore
@using eZeroCore.Web.BlazorServerUI
@inject NavigationManager NavManager
@inject AuthenticationStateProvider AuthStateProvider
@code {
    private string HtmlLang = "zh-CN"; // 动态设置语言
    private string Theme = "dark"; // 例如：支持暗黑模式
    //private string BodyClass = "default-theme";

    bool IsStk;
    /// <summary>
    ///
    /// </summary>
    string htmlClassName { get; set; } = "";    
    
    private bool IsLoggedIn { get; set; }

    eZeroCore.Settings webConfig = new eZeroCore.Settings();
    string sVer = BlazorApp_KzApp.Utils.Properties.GetVersion();    

    protected override async Task OnInitializedAsync()
    {
        IsStk = Settings.GetSysMode == Settings.SysMode.Stk;
        if (IsStk)
        {
            htmlClassName = "h-100 height-full";
        }       
        await Task.CompletedTask;
    }    
    
}

<!DOCTYPE html>
<html lang="@HtmlLang" data-theme="@Theme" class="" data-bs-theme="@(IsStk ? "dark" : "")" data-navigation-type="combo">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />    

    <meta http-equiv="Cross-Origin-Opener-Policy" content="same-origin-allow-popups">

    <link href="/_content/MudBlazor/MudBlazor.min.css" rel="stylesheet" />
    <script src="/_content/MudBlazor/MudBlazor.min.js"></script>


    <!-- JavaScript 资源 -->

    <script src="/lib/jquery-3.7.0.min.js"></script>

    <script src="/lib/Ph117/public/vendors/simplebar/simplebar.min.js"></script>
    <script src="/lib/Ph117/public/assets/js/config.js"></script>

    <!-- CSS 资源 -->
    <link href="/lib/Ph117/public/vendors/simplebar/simplebar.min.css" rel="stylesheet">
    <link href="https://unicons.iconscout.com/release/v4.0.8/css/line.css" type="text/css" rel="stylesheet">
    <link href="/lib/Ph117/public/assets/css/theme.min.css" type="text/css" rel="stylesheet" id="style-default">
    <link href="/lib/Ph117/public/assets/css/user.min.css" type="text/css" rel="stylesheet" id="user-style-default">
    
    <link href="/_content/Radzen.Blazor/css/standard-dark.css" type="text/css" rel="stylesheet">
    <script src="/_content/Radzen.Blazor/Radzen.Blazor.js?v=@(typeof(Radzen.Colors).Assembly.GetName().Version)"></script>
    @*
        <link href="/_content/MudBlazor/MudBlazor.min.css" type="text/css" rel="stylesheet">
        <link href="_content/TinyMCE.Blazor/TinyMce.Blazor.lib.module.js">
    *@        

    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/2.11.6/umd/popper.min.js"></script>
    <script src="https://embed.cloudflarestream.com/embed/sdk.latest.js"></script>

    @* https://github.com/sampotts/plyr *@
    <script src="https://cdn.plyr.io/3.7.8/plyr.js"></script>
    <link rel="stylesheet" href="https://cdn.plyr.io/3.7.8/plyr.css" />

    @switch (Settings.GetSysMode)
    {
        case Settings.SysMode.Stk:
            <script>
                //Google Analytics
                window.dataLayer = window.dataLayer || [];
                function gtag() { dataLayer.push(arguments); }
                gtag('js', new Date());
                gtag('config', 'G-R3PM7LCXK3');
            </script>
            break;                
        default:
            break;
    }    
    @*https://cdn.jsdelivr.net/npm/echarts@@5.5.1/dist/echarts.min.js*@
    <script src="/ph/echarts.min.js"></script>

    <script async src="/lib/jquery-loading-master/dist/jquery.loading.min.js"></script>

    <ImportMap />
    
    <link rel="stylesheet" href="/lib/ckeditor5-builder-45213/ckeditor5/ckeditor5.css">

    <script type="importmap">
        {
            "imports": {
                    "ckeditor5": "/lib/ckeditor5-builder-45213/ckeditor5/ckeditor5.js",
                    "ckeditor5/": "/lib/ckeditor5-builder-45213/ckeditor5/"                    
            }
        }
    </script>
    
    <HeadOutlet />

    <script src="/lib/jquery-loading-master/dist/jquery.loading.min.js?ver=@sVer" type="text/javascript"></script>
    <!-- JavaScript 资源 -->
    <script src="/ph/CleanKy.js?ver=@sVer"></script>
    <script src="/ph/weicity.js?ver=@sVer"></script>
    <script src="/lib/tinymce/tinymce.min.js"></script>
    @if (IsStk)
    {
        <script src="/lib/_StkHome/js/st.js?ver=@sVer" type="text/javascript"></script>
    }

    <!-- CSS 资源 -->
    <link href="/ph/pub.css?ver=@sVer" rel="stylesheet">
    <link href="/ph/@(Settings.GetSysMode.ToString()).css?ver=@sVer" rel="stylesheet">
    <link href="/ph/ChatboxWeiCity.css?ver=@sVer" rel="stylesheet">
    <link href="/ph/Chatbox@(Settings.GetSysMode.ToString()).css?ver=@sVer" rel="stylesheet">

    <link href="/ph/css/Board.css" rel="stylesheet">
    <link href="/ph/css/StockData.css" rel="stylesheet">
    <link href="/ph/css/News.css" rel="stylesheet">
    <link href="/ph/css/Universal.css" rel="stylesheet">
    <base href="/" />

</head>

<body class="nav-slim">

    <a id="eZlogo-overlay" href="javascript:location.reload()">
        <div class="position-absolute top-50 start-50 translate-middle">
            <img width="98" src="@(eZeroCore.Web.PWA.Settings.Get512Logo)" style="--fa-animation-duration: 2s; --fa-fade-opacity: 0.6;" class="fa-fade fa-flip" alt="Logo">
        </div>
    </a>

    <Routes />

    <button id="scrollTopButton" class="contact-social-link w-inline-block" onclick="eSc.scrollHomeToTop()" style="display: none;">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- ===============================================-->
    <!--  defer  JavaScripts-->
    <!-- ===============================================-->

    <script src="/lib/Ph117/public/vendors/popper/popper.min.js" defer></script>
    <script src="/lib/Ph117/public/vendors/bootstrap/bootstrap.min.js" defer></script>
    <script src="/lib/Ph117/public/vendors/anchorjs/anchor.min.js" defer></script>
    <script src="/lib/Ph117/public/vendors/is/is.min.js" defer></script>
    @*
    <script src="/lib/Ph117/public/vendors/fontawesome/all.min.js" defer></script>
    *@
    <script src="https://kit.fontawesome.com/c85fff200d.js" crossorigin="anonymous"></script>

    <script src="/lib/Ph117/public/vendors/lodash/lodash.min.js" defer></script>
    <script src="/lib/Ph117/public/vendors/feather-icons/feather.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.11/clipboard.min.js" defer></script>

    <script type="module">
        import { createPopup } from 'https://unpkg.com/@@picmo/popup-picker@latest/dist/index.js?module';        
        window.picmoPopup = { createPopup };
    </script>

    <script src="/Ph/Chatbox.js?ver=@sVer"></script>

    <script type="module" src="/lib/pdfjs-4.9.155-dist/build/pdfjsInterop.mjs?ver=@sVer"></script>    

    <script src="_framework/blazor.web.js"></script>

    <script src="/lib/eZBlazor.js?ver=@sVer"></script>    

    <script>
            const observer = new MutationObserver(() => {
            const dialog = document.getElementById('components-reconnect-modal');
            if (dialog && dialog.shadowRoot) {
                const root = dialog.shadowRoot;

                // 修改段落文本样式
                const p = root.querySelector('p');
                if (p) {
                    //p.innerHTML = '⚠️ 与服务器连接已断开，请稍候自动重连...';
                    p.style.color = 'white';
                }

                // 修改 modal 容器的背景色
                const modalDiv = root.querySelector('.components-reconnect-dialog');
                if (modalDiv) {
                    modalDiv.style.backgroundColor = 'black';
                    modalDiv.style.padding = '5px';
                    modalDiv.style.borderRadius = '10px';
                }

                // 停止观察，避免重复触发
                observer.disconnect();
            }
        });

        observer.observe(document.body, { childList: true, subtree: true });

    </script>


</body>

</html>
