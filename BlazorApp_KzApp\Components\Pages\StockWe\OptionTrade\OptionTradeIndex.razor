﻿@using <PERSON>la<PERSON>App_KzApp.Components.Pages.ChatGroupMessages
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.Pages.FrontEnd
@using <PERSON><PERSON><PERSON>

@page "/OptionTrade/{param?}"

@code {
    [Parameter]
    public string param { get; set; } = "";

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Inject]
    IJSRuntime JS { get; set; }

    [Inject]
    NavigationManager NavigationManager { get; set; }

    eZeroCore.Settings settings = new eZeroCore.Settings();
    public bool IsLiveMode { get; set; } = true;
    string SearchString { get; set; } = "";
    DateRange? OnSetDateRange { get; set; }
    private bool _isChartPanelReady = false;

    _ChartPanel _ChartPanelObj;
    _InfoCard_Bar _InfoCard;
    _StockOption _StockOption;

    // 初始化，获取symbol参数
    protected override async Task OnInitializedAsync()
    {
        try
        {
            var uri = new Uri(NavigationManager.Uri);
            var query = System.Web.HttpUtility.ParseQueryString(uri.Query);
            var symbol = query["symbol"];
            if (!string.IsNullOrEmpty(symbol))
            {
                SearchString = symbol;
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("console.error", $"OnInitializedAsync异常: {ex.Message}");
        }
    }

    // 首次渲染后刷新数据
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            try
            {
                await Refresh();
                // 如果有symbol参数，等待组件加载完成后，调用_ChartPanelObj的OnSearch方法
                if (!string.IsNullOrEmpty(SearchString))
                {
                    await Task.Delay(500);
                    if (_ChartPanelObj != null)
                    {
                        await _ChartPanelObj.OnSearch(SearchString);
                    }
                    if (_StockOption != null)
                    {
                        await _StockOption.RefreshData(true);
                    }
                    if (_InfoCard != null)
                    {
                        await _InfoCard.Init(string.IsNullOrWhiteSpace(SearchString) ? null : false, searchString: SearchString);
                    }
                }
            }
            catch (Exception ex)
            {
                await JS.InvokeVoidAsync("console.error", $"OnAfterRenderAsync异常: {ex.Message}");
            }
        }
    }

    // 定时刷新数据
    async Task OnSendTickSecEvent()
    {
        try
        {
            if (IsLiveMode)
            {
                await Refresh(true);
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("console.error", $"OnSendTickSecEvent异常: {ex.Message}");
        }
    }

    // 设置日期区间
    async Task SetDataRange(DateRange? dateRange)
    {
        OnSetDateRange = dateRange;
    }

    // 搜索股票并刷新
    async Task OnSearch(string value)
    {
        try
        {
            SearchString = value;
            if (CObj != null && CObj.IsKyrison)
            {
                await JS.InvokeVoidAsync("console.log", $"OnSearch:【{SearchString}】2");
            }
            if (_StockOption is not null)
            {
                _ = _StockOption.RefreshData(true);
            }
            if (_InfoCard is not null)
            {
                await _InfoCard.Init(string.IsNullOrWhiteSpace(SearchString) ? null : false, searchString: SearchString);
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("console.error", $"OnSearch异常: {ex.Message}");
        }
    }

    async Task OnChangePageNumberOrOrder(bool live)
    {
        try
        {
            if (_InfoCard != null)
                await _InfoCard.Init(live, true, searchString: SearchString);
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("console.error", $"OnChangePageNumberOrOrder异常: {ex.Message}");
        }
    }

    // 刷新所有数据
    async Task Refresh(bool update = false)
    {
        try
        {
            if (_InfoCard is not null)
            {
                await _InfoCard.Init(searchString: SearchString);
            }
            if (_ChartPanelObj is not null)
            {
                _ = _ChartPanelObj.Init(update);
            }
            else
            {
                await Task.Delay(800);
                StateHasChanged();
                if (_ChartPanelObj is not null)
                {
                    await _ChartPanelObj.Init(update);
                }
                else
                {
                    await JS.InvokeVoidAsync("console.log", $"【_ChartPanelObj 2】 is null");
                }
            }
            if (_StockOption is not null)
            {
                _ = _StockOption.RefreshData();
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("console.error", $"Refresh异常: {ex.Message}");
        }
    }

    // 图表面板初始化回调
    private async Task OnChartPanelInitialized()
    {
        _isChartPanelReady = true;
        try
        {
            if (!string.IsNullOrEmpty(SearchString) && _ChartPanelObj != null)
            {
                await _ChartPanelObj.OnSearch(SearchString);
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("console.error", $"OnChartPanelInitialized异常: {ex.Message}");
        }
    }
}

<Main_Framework ContentClass="mb-0">

    <SubscriptionV3 PaymentVisable="@(!eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj.UserId)))">

        <_InfoCard_Bar @ref=_InfoCard Live="(bool live)=>{IsLiveMode=live;}" SetDateRange="@SetDataRange" />

        <CascadingValue Value="OnSetDateRange">

            <_ChartPanel @ref=_ChartPanelObj OnSearchStock="@OnSearch" OnInitialized="OnChartPanelInitialized" />

            <_StockOption @ref=_StockOption OnChangePageNumberOrOrder="@OnChangePageNumberOrOrder" SearchString="@SearchString" />

        </CascadingValue>

        <ChatGroupMsgOffcanvas RedirectUrl="/chat" OnSendTickMin="OnSendTickSecEvent" />

    </SubscriptionV3>

    <_FooterMenus />

</Main_Framework>