@using ZycCore.Models
@using ZycCore.Utils
@using MudBlazor

@code {
    [Parameter] public List<StockAnalystRating.Data> Data { get; set; } = new();
    [Parameter] public bool IsLoading { get; set; }
    [Parameter] public int TotalCount { get; set; }
    [Parameter] public int PageSize { get; set; }
    [Parameter] public int CurrentPage { get; set; }
    [Parameter] public bool EnableBlurFeature { get; set; } = false;
    [Parameter] public Func<int, string>? GetAnalystAvatar { get; set; }
    [Parameter] public Func<int, bool>? ShouldBlur { get; set; }
    [Parameter] public EventCallback<int> OnPageChanged { get; set; }
    [Parameter] public EventCallback<int> OnMobileUpgradeClick { get; set; }

    // 内部头像获取方法
    private string GetAvatarUrl(int index)
    {
        // 如果外部传入了头像获取函数，使用它
        if (GetAnalystAvatar != null)
        {
            return GetAnalystAvatar(index);
        }
        
        // 否则使用默认逻辑
        int avatarId = (index % 5) + 1; // 假设有5个头像文件循环使用
        return $"/Ph/images/analyst/{avatarId}.png";
    }
}

<div class="mobile-container">
    @if (IsLoading)
    {
        <div style="display:flex; justify-content:center; margin:12px 0;">
            <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
        </div>
    }
    else if (Data?.Count > 0)
    {
        @foreach (var item in Data)
        {
            var rowIndex = Data.IndexOf(item);
            var shouldBlur = EnableBlurFeature && ShouldBlur?.Invoke(rowIndex) == true;
            var starRating = AnalystUtil.GetStarRating(item.Accurate);

            <div class="mobileDatatable" style="padding:1px 0; border-bottom:1px solid rgba(255,255,255,0.1);">
                <!-- 使用移动端表格头部组件 -->
                <_MobileTableHeader TimeValue="@item.CreateDate" TimeFormat="MM/dd">
                    <CustomContent>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <div style="font-size:12px; font-weight:500; color:white; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; max-width:180px;">
                                @if (shouldBlur)
                                {
                                    <div @onclick="async () => await OnMobileUpgradeClick.InvokeAsync(rowIndex)" @ontouchstart="async () => await OnMobileUpgradeClick.InvokeAsync(rowIndex)"
                                         style="position: relative; cursor: pointer; height: 20px; overflow: hidden; border-radius: 2px;">
                                        <div style="position: absolute; inset: 0; backdrop-filter: blur(8px); background-color: rgba(15,17,26,0.1); border: 1px solid rgba(255,255,255,0.1);"></div>
                                        <div style="position: relative; z-index: 10; display: flex; align-items: center; gap: 2px; padding: 0 2px; height: 100%;">
                                            <img src="@GetAvatarUrl(rowIndex)" alt="@item.Analyst" style="width: 14px; height: 14px; border-radius: 50%; filter: blur(1px); opacity: 0.8;" />
                                            <div style="background-color: rgba(111, 187, 241, 0.4); width: 30px; height: 12px; border-radius: 1px; filter: blur(0.5px); opacity: 0.7;"></div>
                                        </div>
                                        <div style="position: absolute; inset: 0; z-index: 20; display: flex; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.4);">
                                            <i class="fas fa-lock" style="color: #f59c1a; font-size: 9px;"></i>
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div style="display: flex; align-items: center; gap: 2px; height: 20px;">
                                        <img src="@GetAvatarUrl(rowIndex)" alt="@item.Analyst" style="width: 14px; height: 14px; border-radius: 50%; object-fit: cover; border: 1px solid #ddd;" />
                                        <div style="font-size: 13px; font-weight:700; color: white; flex: 1;">@item.Analyst</div>
                                    </div>
                                }
                            </div>
                        </div>
                    </CustomContent>
                    <RightContent>
                        <div style="font-size:12px;">
                            @((MarkupString)AnalystUtil.GetStarHtml(starRating))
                        </div>
                    </RightContent>
                </_MobileTableHeader>

                <!-- 使用左右布局组件 -->
                <_LeftRightLayout LeftWidth="24%">
                    <LeftContent>
                        <!-- 股票代号 -->
                        <div style="text-align:center;">
                            <span style="background-color:#212529; color:white; padding:1px 4px; border-radius:3px; font-size:17px; font-weight:bold;">
                                @item.Stock
                            </span>
                        </div>

                        <!-- 分数 -->
                        <div>
                            <div class="scores-and-amplitude" style="column-gap:5px;">
                                <div class="scores-and-amplitude-label">分数</div>
                                @if (shouldBlur)
                                {
                                    <div @onclick="async () => await OnMobileUpgradeClick.InvokeAsync(rowIndex)" @ontouchstart="async () => await OnMobileUpgradeClick.InvokeAsync(rowIndex)"
                                         class="scores-and-amplitude-value" style="position: relative; cursor: pointer; padding-right:5px; overflow: hidden; border-radius: 2px;">
                                        <div style="position: absolute; inset: 0; backdrop-filter: blur(8px); background-color: rgba(15,17,26,0.1); border: 1px solid rgba(255,255,255,0.1);"></div>
                                        <div style="position: relative; z-index: 10; display: flex; align-items: center; padding: 0 2px; height: 100%;">
                                            <div style="background-color: rgba(75, 192, 192, 0.4); width: 38px; height: 12px; border-radius: 1px; filter: blur(0.5px); opacity: 0.7;"></div>
                                        </div>
                                        <div style="position: absolute; inset: 0; z-index: 20; display: flex; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.4);">
                                            <i class="fas fa-lock" style="color: #f59c1a; font-size: 9px;"></i>
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div class="scores-and-amplitude-value" style="padding-right:5px;">@(item.Score?.ToString("N2"))</div>
                                }
                            </div>
                        </div>
                    </LeftContent>
                    <RightContent>
                        <!-- Grid布局 -->
                        <div style="display: grid; grid-template-columns: 80px 1fr 1fr 1fr; grid-template-rows: auto auto; gap: 5px 4px; margin-left: 8px;">

                            <!-- 第一行：目标价幅度、准确率、新目标价、旧目标价 -->
                            <div style="grid-column: 1; grid-row: 1;">
                                <div class="gd-label">目标价幅度</div>
                                <div class="gd-value" style="color:@(item.UpsideDownside > 0 ? "#28a745" : "#dc3545");">
                                    @AnalystUtil.FormatPercent(item.UpsideDownside, 2, true)
                                </div>
                            </div>

                            <div style="grid-column: 2; grid-row: 1;">
                                <div class="gd-label">准确率</div>
                                @if (shouldBlur)
                                {
                                    <div @onclick="async () => await OnMobileUpgradeClick.InvokeAsync(rowIndex)" @ontouchstart="async () => await OnMobileUpgradeClick.InvokeAsync(rowIndex)"
                                         style="position: relative; cursor: pointer; height: 20px; overflow: hidden; border-radius: 2px;">
                                        <div style="position: absolute; inset: 0; backdrop-filter: blur(8px); background-color: rgba(15,17,26,0.1); border: 1px solid rgba(255,255,255,0.1);"></div>
                                        <div style="position: relative; z-index: 10; display: flex; align-items: center; padding: 0 2px; height: 100%;">
                                            <div style="background-color: rgba(255, 193, 7, 0.4); width: 35px; height: 12px; border-radius: 1px; filter: blur(0.5px); opacity: 0.7;"></div>
                                        </div>
                                        <div style="position: absolute; inset: 0; z-index: 20; display: flex; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.4);">
                                            <i class="fas fa-lock" style="color: #f59c1a; font-size: 9px;"></i>
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div style="font-size:13px; font-weight:700; height: 20px; display: flex; align-items: center;">@AnalystUtil.FormatPercent(item.Accurate)</div>
                                }
                            </div>

                            <div style="grid-column: 3; grid-row: 1;">
                                <div class="gd-label">新目标价</div>
                                <div style="font-size:13px; font-weight:700; color:#28a745; height: 20px; display: flex; align-items: center;">$@AnalystUtil.FormatNumber(item.NewPriceTarget)</div>
                            </div>

                            <div style="grid-column: 4; grid-row: 1;">
                                <div class="gd-label">旧目标价</div>
                                <div style="font-size:13px; font-weight:700; height: 20px; display: flex; align-items: center;">$@AnalystUtil.FormatNumber(item.PrePriceTarget)</div>
                            </div>

                            <!-- 第二行：机构、评级变化、旧评级、新评级 -->
                            <div style="grid-column: 1; grid-row: 2;">
                                <div class="gd-label">机构</div>
                                @if (shouldBlur)
                                {
                                    <div @onclick="async () => await OnMobileUpgradeClick.InvokeAsync(rowIndex)" @ontouchstart="async () => await OnMobileUpgradeClick.InvokeAsync(rowIndex)"
                                         style="position: relative; cursor: pointer; height: 20px; overflow: hidden; border-radius: 2px;">
                                        <div style="position: absolute; inset: 0; backdrop-filter: blur(8px); background-color: rgba(15,17,26,0.1); border: 1px solid rgba(255,255,255,0.1);"></div>
                                        <div style="position: relative; z-index: 10; display: flex; align-items: center; padding: 0 2px; height: 100%;">
                                            <div style="background-color: rgba(111, 187, 241, 0.4); width: 75px; height: 12px; border-radius: 1px; filter: blur(0.5px); opacity: 0.7;"></div>
                                        </div>
                                        <div style="position: absolute; inset: 0; z-index: 20; display: flex; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.4);">
                                            <i class="fas fa-lock" style="color: #f59c1a; font-size: 9px;"></i>
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <div style="font-size:12px; font-weight:700; min-height: 20px; display: flex; align-items: flex-start; word-break: break-all; line-height: 1.2;">@item.AnalystFirm</div>
                                }
                            </div>

                            <div style="grid-column: 2; grid-row: 2;">
                                <div class="gd-label">评级变化</div>
                                @{
                                    var ratingChangeType = AnalystUtil.ParseRatingChangeType(item.RatingChange);
                                }
                                <div style="height: 20px; display: flex; align-items: center;">
                                    <span class="badge @AnalystUtil.GetRatingChangeClass(ratingChangeType)" style="font-size:13px; padding:1px 3px; line-height:1.2;">@AnalystUtil.GetRatingChangeText(ratingChangeType)</span>
                                </div>
                            </div>

                            <div style="grid-column: 3; grid-row: 2;">
                                <div class="gd-label">旧评级</div>
                                @{
                                    var oldRatingType = AnalystUtil.ParseRatingType(item.PreRating);
                                }
                                <div style="height: 20px; display: flex; align-items: center;">
                                    <span class="badge @AnalystUtil.GetRatingClass(oldRatingType)" style="font-size:13px; padding:1px 3px; line-height:1.2;">@AnalystUtil.GetRatingText(oldRatingType)</span>
                                </div>
                            </div>

                            <div style="grid-column: 4; grid-row: 2;">
                                <div class="gd-label">新评级</div>
                                @{
                                    var newRatingType = AnalystUtil.ParseRatingType(item.NewRating);
                                }
                                <div style="height: 20px; display: flex; align-items: center;">
                                    <span class="badge @AnalystUtil.GetRatingClass(newRatingType)" style="font-size:13px; padding:1px 3px; line-height:1.2;">@AnalystUtil.GetRatingText(newRatingType)</span>
                                </div>
                            </div>
                        </div>
                    </RightContent>
                </_LeftRightLayout>
            </div>
        }
        
        <!-- 移动端分页控件 -->
        <div style="display:flex; justify-content:center; margin-top:16px;">
            <MudPagination Count="@((int)Math.Ceiling((double)TotalCount / PageSize))" 
                           SelectedChanged="async (int newPage) => { await OnPageChanged.InvokeAsync(newPage); }" 
                           Selected="CurrentPage" />
        </div>
    }
    else
    {
        <div style="text-align:center; margin:16px 0; color:#6c757d;">
            <span>暂无数据</span>
        </div>
    }
</div> 