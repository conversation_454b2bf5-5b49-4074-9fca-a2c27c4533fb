@page "/EditorTest/{param?}"
@inject IJSRuntime JS
@code {

    [Parameter]
    public string param { get; set; } = "";

    CkEditor5 ckEditor5;
 

    async Task GetData()
    {
        if (ckEditor5 is not null)
        {
            //TestValue = await ckEditor5.GetData();            
        }
    }
    string TestValue = "<p>Test Type your content here...</p>";
}

<Main_Framework Container_Fluid="false" ContainerClass="pt-md-3">

    <CkEditor5 @ref=ckEditor5 @bind-Content=@TestValue />

    <div>
        @TestValue
    </div>

    <Ez_Btn OnBtnClick="@GetData">
        Test
    </Ez_Btn>

</Main_Framework>

