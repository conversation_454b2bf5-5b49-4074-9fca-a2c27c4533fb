﻿@using <PERSON><PERSON><PERSON>
@inject IJSRuntime JS

@code {

    [Parameter]
    public EventCallback<string> OnSearch { get; set; }

    [Parameter]
    public string Placeholder { get; set; } = "搜索";

    [Parameter]
    public string ClassName { get; set; } = "ms-auto mt-n2";

    [Parameter]
    public string Style { get; set; } = "min-width:260px;";

    [Parameter]
    public string DefaultStock { get; set; } = "";

}


<RadzenFormField Style="@Style" class="@ClassName" AllowFloatingLabel="false" Text=@Placeholder Variant="Radzen.Variant.Outlined">
    <ChildContent>
        <RadzenTextBox AutoComplete="false" MaxLength="36" @ref="searchTextBox" @bind-Value="@SearchTextValue" />
    </ChildContent>
    <End>
        @if (SearchMode)
        {
            <Ez_Btn OnBtnClick="CancelSearchMode" ClassName="text-danger">
                &times;
            </Ez_Btn>
        }
    </End>
</Radzen<PERSON><PERSON><PERSON>ield>

@code{
    string SearchTextValue { get; set; } = "";
    private RadzenTextBox searchTextBox;
    bool SearchMode { get; set; }
    [JSInvokable]
    public async Task HandleSearchKeyDown()
    {
        await Task.Delay(200);
        await OnSearch.InvokeAsync(SearchTextValue);
        SearchMode = !string.IsNullOrWhiteSpace(SearchTextValue);
        //await Task.Delay(150);
        // 处理搜索逻辑
        //await JS.InvokeVoidAsync("console.log", $"Enter key pressed!{SearchTextValue}");
        // 清空输入框内容
        //SearchTextValue = string.Empty;
        StateHasChanged();
    }

    async Task CancelSearchMode()
    {
        SearchTextValue = string.Empty;
        /*
        if (statusSelector is not null)
        {
            await statusSelector.Init(true);
        }
        */
        await HandleSearchKeyDown();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            if (searchTextBox is not null)
            {
                // 获取 RadzenTextBox 内部的输入元素并添加事件处理程序
                await JS.InvokeVoidAsync("eSc.inputAddEnterKeyHandler", searchTextBox.Element, DotNetObjectReference.Create(this), nameof(HandleSearchKeyDown));
            }
        }
    }
    
    protected override async Task OnInitializedAsync()
    {
        await Task.Delay(150);
        if (!string.IsNullOrEmpty(DefaultStock))
        {
            DefaultStock = DefaultStock.ToUpper();
            SearchTextValue = DefaultStock;
            SearchMode = true;
        }
        await base.OnInitializedAsync();
    }
}