@inject IJSRuntime JS
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web.Virtualization

@code {    

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public eZeroCore.Web.Column ParentColumn { get; set; } = new();

    string ParentColumnId { get; set; } = "";
    string colUrl = "";

    protected override async Task OnInitializedAsync()
    {
        ParentColumnId = ParentColumn.ColumnId;
        colUrl = ParentColumn.GetColumnUrl();
        DomId = $"{ParentColumnId}_{Math.Abs(ParentColumn.ColumnIdTree.GetHashCode())}";
        Icon = ParentColumn.SettingsEntity.Icon;
        if (!Active)
        {
            Active = CObj.ThisUrl_NotLoginOrRegister.Contains(colUrl, StringComparison.OrdinalIgnoreCase);
        }
        await Task.Delay(0);
    }

    bool Active { get; set; }
    string DomId = "";
    string Icon = "";
}

@if (!ParentColumn.ExistSubColumnWithHidden)
{
    <li>
        <a class="dropdown-item @(Active?"active":"")" href="@(Active?"javascriopt:;":colUrl)">
            <div class="dropdown-item-wrapper">
                <span>
                    @if (!string.IsNullOrWhiteSpace(Icon))
                    {
                        <i class="@Icon fa-fw me-2"></i>
                    }
                    @ParentColumn.ColumnName.Truncate(Main_Framework.Settings.LeftOrTopSubMenusMaxString)
                </span>
            </div>
        </a>
    </li>
}
else
{
    <li class="dropdown @(ParentColumn.ColumnIdTree.Split('/').Length>5?"dropdown-inside":"")">
        <a class="dropdown-item dropdown-toggle" id="@DomId" href="#" data-bs-toggle="dropdown" data-bs-auto-close="outside">
            <div class="dropdown-item-wrapper">
                <span class="uil fs-8 uil-angle-right lh-1 dropdown-indicator-icon"></span>
                <span>
                    @if (!string.IsNullOrWhiteSpace(Icon))
                    {
                        <i class="@Icon fa-fw me-2"></i>
                    }
                    @ParentColumn.ColumnName.Truncate(Main_Framework.Settings.LeftOrTopSubMenusMaxString)
                </span>
            </div>
        </a>
        <ul class="dropdown-menu">

            <Virtualize ItemsProvider="new MudUI.MenusVirtualize(CObj, ParentColumnId).LoadColumnsAsync" Context="column" ItemSize="6">
                <ItemContent>
                    <_MenuLiItem ParentColumn="@column" @key="@($"TopSub_{column.ColumnId}")" />
                </ItemContent>
                <Placeholder>
                    Loading...
                </Placeholder>
            </Virtualize>

        </ul>

    </li>
}

