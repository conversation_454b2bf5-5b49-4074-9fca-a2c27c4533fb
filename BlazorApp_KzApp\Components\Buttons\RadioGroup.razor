
<div class="btn-group" role="group" aria-label="Basic radio toggle button group">
    @for (int i = 0; i < Options.Count; i++)
    {
        var option = Options[i];
        var isChecked = option.Value == Value;

        <input type="radio" class="btn-check" checked="@isChecked" name="name" id="radio_@i" autocomplete="off" @onclick="() => HandleClick(option.Value)">
        <label class="btn btn-outline-primary" for="radio_@i">@(new MarkupString(option.Label))</label>
    }
</div>

@code {

    [Parameter] public int Value { get; set; }
    [Parameter] public EventCallback<int> ValueChanged { get; set; }
    [Parameter] public List<Option> Options { get; set; }    


    public RadioGroup()
    {
        if (Options == null)
        {
            Options = new List<Option>();
        }
    }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    async Task HandleClick(int optionValue)
    {
        Value = optionValue;
        await ValueChanged.InvokeAsync(Value);
    }
}

