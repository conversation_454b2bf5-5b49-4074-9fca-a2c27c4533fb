﻿@inject IJSRuntime JS
@inject NavigationManager NavManagerObj
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public eZeroCore.Web.Document DocObj { get; set; } = new();

    [Parameter]
    public bool CompactMode { get; set; } = false;

    string PriceInfo { get; set; } = "";
    string PriceInfoClass { get; set; } = "";
    protected override async Task OnInitializedAsync()
    {
        if (DocObj.Prices == 0)
        {
            PriceInfo = CObj.UserLang.GetLangValue("免费");
            PriceInfoClass = "success";
        }
        else
        {
            if (DocObj.FreeForMinGroupLv > 0)
            {
                PriceInfo = CObj.UserLang.GetLangValue("会员免费");
                PriceInfoClass = "info";
            }
            else
            {
                PriceInfo = CObj.UserLang.GetLangValue("付费内容");
                PriceInfoClass = "danger";
            }
        }
        await Init();
    }

    string GetPoster(eZeroCore.Web.Document doc)
    {
        string Poster = "";
        if (doc.TitlePhotoUrls is not null)
        {
            Poster = doc.TitlePhotoUrls.Split(",").FirstOrDefault() ?? "";
        }
        if (string.IsNullOrEmpty(Poster))
        {
            Poster = eZeroCore.Web.BlazorUI.WebQuest.GetBingImage();
        }
        return Poster;
    }

    [Parameter]
    public EventCallback<string> OnClickTag { get; set; }

    [Parameter]
    public bool TagsVisible { get; set; } = true;

    List<eZeroCore.Web.Document> Datas = new();

    async Task Init()
    {
        eZeroCore.Web.DocList docList = new()
            {
                PageSize = 4,
                HiddenVisable = CObj.IsBMS,
                ReplyDocOrTargetId = DocObj.DocId,
                OrderBy = eZeroCore.Web.DocList.OrderByEnum.Followorder,
                SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending
            };
        Datas = await docList.ListAsync();
    }

    async Task OpenInNewTab(string url)
    {
        if (!string.IsNullOrWhiteSpace(url))
        {
            await JS.InvokeVoidAsync("open", url, "_blank");
        }
    }
}

<div class="card shadow-none border p-3 mb-2">
    <a href="@DocObj.GetDocUrl()" target="_blank" class="pt-n2 pb-2 d-flex justify-content-start d-block text-decoration-none">
        <div class="flex-fill">
            <div class="h3">
                <i class="fal fa-tv-alt fs-7 text-warning"></i>
                @DocObj.FullTitle
            </div>
            <p class="mb-1 text-body-emphasis Line1">
                <span class="badge badge-phoenix badge-phoenix-@PriceInfoClass ms-n1 me-1">@PriceInfo</span>
                @DocObj.SubTitle
            </p>
        </div>
        <div style="width:@(CompactMode ? "50px" : "100px");">&nbsp;</div>
    </a>
    <div class="row g-2">

        @foreach (eZeroCore.Web.Document doc in Datas)
        {
            <a href="@doc.GetDocUrl()" target="_blank"  @key=doc.DocId class="col-6 col-xl-3 d-block text-decoration-none">
                <div class="card">
                    <div class="card-img-top ratio ratio-4x3 ratio-lg-16x9">
                        <img class="rounded" style="object-fit:cover;" src="@GetPoster(doc)" alt="">
                    </div>
                    <div class="card-body p-2" style="min-height:108px;">
                        <h5 class="Line2 mb-1 @doc.DocId">
                            @doc.FullTitle
                        </h5>
                        <div class="Line2 fs-9 mb-1">
                            @doc.DocContentSummary
                        </div>
                    </div>
                </div>
            </a>
        }
        
    </div>
    <a href="@DocObj.GetDocUrl()" target="_blank" class="d-block text-center btn btn-link text-decoration-none mt-2">
        @CObj.UserLang.GetLangValue("更多")@(DocObj.FullTitle)
        <i class="fa-solid fa-angles-right"></i>
    </a>
</div>