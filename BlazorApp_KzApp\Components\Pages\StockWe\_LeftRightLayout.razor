@code {
    [Parameter] public string LeftWidth { get; set; } = "24%";  // 左侧宽度
    [Parameter] public string Gap { get; set; } = "4px";        // 左右间距

    [Parameter] public string ContainerClass { get; set; } = "";
    [Parameter] public string ContainerStyle { get; set; } = "";

    [Parameter] public string LeftSectionClass { get; set; } = "";
    [Parameter] public string LeftSectionStyle { get; set; } = "";

    [Parameter] public string RightSectionClass { get; set; } = "";
    [Parameter] public string RightSectionStyle { get; set; } = "";

    [Parameter] public RenderFragment? LeftContent { get; set; }
    [Parameter] public RenderFragment? RightContent { get; set; }
}

<div class="@ContainerClass" 
     style="@($"display:flex; gap:{Gap}; {ContainerStyle}".Trim())">
     
    <!-- 左侧区域 -->
    <div class="@LeftSectionClass" 
         style="@($"flex: 0 0 {LeftWidth}; display:flex; flex-direction:column; gap:0px; {LeftSectionStyle}".Trim())">
        @LeftContent
    </div>

    <!-- 右侧区域 -->
    <div class="@RightSectionClass" 
         style="@($"flex: 1; {RightSectionStyle}".Trim())">
        @RightContent
    </div>
</div> 