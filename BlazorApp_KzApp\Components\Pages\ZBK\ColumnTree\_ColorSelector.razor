﻿@inject IJSRuntime JS
@code {
    [Parameter]
    public eZeroCore.Web.UI.Bs5V2.SefeColor SelectedSafecolorClass { get; set; } = eZeroCore.Web.UI.Bs5V2.SefeColor.__phoenix_emphasis_color;

    [Parameter]
    public EventCallback<eZeroCore.Web.UI.Bs5V2.SefeColor> OnSelected { get; set; }


    string? BtnTitleColor;
    int GetSafeColorValue(eZeroCore.Web.UI.Bs5V2.SefeColor color)
    {
        return (int)color;
    }
    string btnTitleClorClassName = "btn-check p-1";

    private async void HandleRadioChange(ChangeEventArgs e)
    {
        int iBtnTitleColor = eZeroCore.ENumeric.ConvertToInt(e.Value?.ToString() ?? "0");
        await OnSelected.InvokeAsync((eZeroCore.Web.UI.Bs5V2.SefeColor)iBtnTitleColor);
    }
}

<div>
    <div class="btn-group btn-group-sm" role="group">
        @{
            int colorvalue = GetSafeColorValue(eZeroCore.Web.UI.Bs5V2.SefeColor.__phoenix_emphasis_color);
            string colorvar = eZeroCore.Web.UI.Bs5V2.GetSafeColorClassName((eZeroCore.Web.UI.Bs5V2.SefeColor)colorvalue);
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleColor)"
               id="@($"{nameof(BtnTitleColor)}{colorvalue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@colorvalue" checked="@((int)SelectedSafecolorClass== colorvalue)">
        <label class="btn btn-outline-light" style="color:var(@colorvar)" for="@($"{nameof(BtnTitleColor)}{colorvalue}")">
            ▇▇▇
        </label>

        @{
            colorvalue = GetSafeColorValue(eZeroCore.Web.UI.Bs5V2.SefeColor.__phoenix_danger);
            colorvar = eZeroCore.Web.UI.Bs5V2.GetSafeColorClassName((eZeroCore.Web.UI.Bs5V2.SefeColor)colorvalue);
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleColor)"
               id="@($"{nameof(BtnTitleColor)}{colorvalue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@colorvalue" checked="@((int)SelectedSafecolorClass== colorvalue)">
        <label class="btn btn-outline-light" style="color:var(@colorvar)" for="@($"{nameof(BtnTitleColor)}{colorvalue}")">
            ▇▇▇
        </label>

        @{
            colorvalue = GetSafeColorValue(eZeroCore.Web.UI.Bs5V2.SefeColor.__phoenix_danger_light);
            colorvar = eZeroCore.Web.UI.Bs5V2.GetSafeColorClassName((eZeroCore.Web.UI.Bs5V2.SefeColor)colorvalue);
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleColor)"
               id="@($"{nameof(BtnTitleColor)}{colorvalue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@colorvalue" checked="@((int)SelectedSafecolorClass== colorvalue)">
        <label class="btn btn-outline-light" style="color:var(@colorvar)" for="@($"{nameof(BtnTitleColor)}{colorvalue}")">
            ▇▇▇
        </label>

        @{
            colorvalue = GetSafeColorValue(eZeroCore.Web.UI.Bs5V2.SefeColor.__phoenix_info_dark);
            colorvar = eZeroCore.Web.UI.Bs5V2.GetSafeColorClassName((eZeroCore.Web.UI.Bs5V2.SefeColor)colorvalue);
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleColor)"
               id="@($"{nameof(BtnTitleColor)}{colorvalue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@colorvalue" checked="@((int)SelectedSafecolorClass== colorvalue)">
        <label class="btn btn-outline-light" style="color:var(@colorvar)" for="@($"{nameof(BtnTitleColor)}{colorvalue}")">
            ▇▇▇
        </label>

        @{
            colorvalue = GetSafeColorValue(eZeroCore.Web.UI.Bs5V2.SefeColor.__mud_palette_tertiary_darken);
            colorvar = eZeroCore.Web.UI.Bs5V2.GetSafeColorClassName((eZeroCore.Web.UI.Bs5V2.SefeColor)colorvalue);
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleColor)"
               id="@($"{nameof(BtnTitleColor)}{colorvalue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@colorvalue" checked="@((int)SelectedSafecolorClass== colorvalue)">
        <label class="btn btn-outline-light" style="color:var(@colorvar)" for="@($"{nameof(BtnTitleColor)}{colorvalue}")">
            ▇▇▇
        </label>

        @{
            colorvalue = GetSafeColorValue(eZeroCore.Web.UI.Bs5V2.SefeColor.__phoenix_warning);
            colorvar = eZeroCore.Web.UI.Bs5V2.GetSafeColorClassName((eZeroCore.Web.UI.Bs5V2.SefeColor)colorvalue);
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleColor)"
               id="@($"{nameof(BtnTitleColor)}{colorvalue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@colorvalue" checked="@((int)SelectedSafecolorClass== colorvalue)">
        <label class="btn btn-outline-light" style="color:var(@colorvar)" for="@($"{nameof(BtnTitleColor)}{colorvalue}")">
            ▇▇▇
        </label>
    </div>
</div>