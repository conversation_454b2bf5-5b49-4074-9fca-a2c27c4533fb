﻿@using <PERSON><PERSON><PERSON>
@inject IJSRuntime JS
@typeparam TItem

<div class="d-flex justify-content-start pageInput" style="width: @(showlastPage?"168":"120")px;">

    @if (showlastPage)
    {
        <span class="ms-n1 me-1 customPageLastButton">...</span>

        <button @onclick="@GoToMaxPageNumber" class="rz-pager-page rz-pager-element customPageLastButton fs-8 me-2" type="button">
            @MaxPageNumber
        </button>
    }

    <input @ref=textBox
           placeholder="@inputPlaceholder"
           class="form-control w-50px px-2 py-0" style="display: inline;" @bind="pageInput" @oninput="HandleInput" @onkeydown="HandleKeyDown" @onfocus="HandleFocus" />
    <RadzenButton Shade="Shade.Darker" ButtonStyle="ButtonStyle.Secondary" Text="@buttonText" Click="GoToPage" class="ms-2 px-1" />
</div>

@code {

    [Parameter]
    public RadzenDataGrid<TItem>? DataGrid { get; set; }

    [Parameter]
    public int PageNumbersCount { get; set; }

    bool showlastPage = true;
    async Task GoToMaxPageNumber()
    {
        DataGrid?.GoToPage(MaxPageNumber - 1);
        ButtonStatus(MaxPageNumber);
        await Task.CompletedTask;
    }

    public void ButtonStatus(int pageNumber)
    {
        _ = Generic.JsConsole(JS, pageNumber, MaxPageNumber);
        showlastPage = MaxPageNumber > PageNumbersCount && pageNumber < MaxPageNumber - 2;
    }

    string inputPlaceholder = "Page";
    string buttonText = "Go";
    int MaxPageNumber { get; set; }
    protected override void OnInitialized()
    {
        if (Settings.GetSysMode == Settings.SysMode.Stk)
        {
            inputPlaceholder = "页码";
            buttonText = "前往";
        }
        /*
        if (DataGrid is not null)
        {
            MaxPageNumber = (int)Math.Ceiling((double)DataGrid.Count / DataGrid.PageSize);
            int currentPage = DataGrid.CurrentPage;
            ButtonStatus(currentPage);
        }
        */
        base.OnInitialized();
    }

    protected override void OnParametersSet()
    {
        if (DataGrid is not null)
        {
            MaxPageNumber = (int)Math.Ceiling((double)DataGrid.Count / DataGrid.PageSize);
            int currentPage = DataGrid.CurrentPage;
            ButtonStatus(currentPage);
        }        
    }



    private ElementReference textBox;

    private void HandleInput(ChangeEventArgs e)
    {
        pageInput = e.Value?.ToString() ?? string.Empty;
    }

    private void HandleKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            GoToPage();
        }
    }

    private async Task HandleFocus()
    {
        await JS.InvokeVoidAsync("eSc.SelectAllText", textBox);
    }

    private string pageInput { get; set; } = "";
    private void GoToPage()
    {
        int pageIndex = eZeroCore.ENumeric.ConvertToInt(pageInput);
        if (DataGrid != null && DataGrid.Count > 1)
        {
            if (pageIndex <= 0 || pageIndex > (DataGrid.Count + DataGrid.PageSize - 1) / DataGrid.PageSize)
            {
                pageIndex = 1;
                pageInput = pageIndex.ToString();
            }
            DataGrid.GoToPage(pageIndex - 1); // Pages are zero-indexed, so subtract 1
        }
    }
}
