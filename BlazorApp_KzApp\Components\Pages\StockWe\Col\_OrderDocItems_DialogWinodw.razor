﻿@using BlazorApp_KzApp.Components.Pages.ZBK.ColumnTree
@using BlazorApp_KzApp.Components.Buttons
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [CascadingParameter] IMudDialogInstance? MudDialog { get; set; }

    [Parameter]
    public List<eZeroCore.Web.Document> Docs { get; set; } = [];

    List<_DropOrdering.DropOrderModel> OrderModels = new();

    protected override async Task OnInitializedAsync()
    {
        if (Docs.Count(item => !string.IsNullOrEmpty(item.ColumnId)) > 0)
        {
            int iCounter = -1;
            foreach (eZeroCore.Web.Document obj in Docs)
            {
                OrderModels.Add(new()
                    {
                        Order = iCounter,
                        Name = obj.FullTitle,
                        Id = obj.DocId
                    });
                iCounter++;
            }
        }
        await Task.CompletedTask;
        StateHasChanged();
    }
    List<string> Ids = [];
    async Task Ok()
    {
        if (MudDialog is not null)
        {
            await eZeroCore.Web.Document.SortingNew2025(Ids);
            MudDialog.Close(DialogResult.Ok<string>("ok"));
            await Task.Delay(0);
        }
    }

    private void Cancel()
    {
        if (MudDialog is not null)
        {
            MudDialog.Cancel();
        }
    }

    async Task GetDropResult(List<_DropOrdering.DropOrderModel> models)
    {
        OrderModels = models;
        Ids = [];
        @foreach (var model in OrderModels.OrderBy(x => x.Order).ToList())
        {
            Ids.Add(model.Id);
        }
        await Task.Delay(0);
    }
}


<MudDialogContent>
    <ChildContent>
        <_DropOrdering Models="OrderModels" UseDrop="@(!CObj.IsMobile)" OnDropOver="@GetDropResult" />
    </ChildContent>
    <MudDialogActions>
        <Ez_Btn Color="General.Color.info" OnBtnClick="Cancel">
            @CObj.UserLang.GetLangValue("取消")
        </Ez_Btn>
        <Ez_Btn IsSubmit="true" Color="General.Color.primary" OnBtnClick="Ok">
            @CObj.UserLang.GetLangValue("确定")
        </Ez_Btn>
    </MudDialogActions>
</MudDialogContent>


