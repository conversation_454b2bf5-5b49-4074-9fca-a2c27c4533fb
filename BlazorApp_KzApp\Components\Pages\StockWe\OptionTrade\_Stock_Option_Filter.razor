@using System.Globalization
@using eZeroCore.Web.Stk.Data
@inject IJSRuntime JS
@code {

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public EventCallback<eZeroCore.Web.Stk.Data.Dt_StockOption.Filters> OnChange { get; set; }

    [Parameter]
    public EventCallback OnSave { get; set; }

    [Parameter]
    public eZeroCore.Web.Stk.Data.Dt_StockOption.Filters Filter { get; set; } = new();

    // 定义最大最小日期
    public DateTime? ExpireDateStart { get; set; } = DateTime.MinValue;
    public DateTime? ExpireDateEnd { get; set; } = DateTime.MaxValue;
    private DateRange _expireDateRange = new();
    DateRange ExpireDateRange
    {
        get => new(Filter.ExpireDateStart, Filter.ExpireDateEnd);
        set
        {
            if (value != null)
            {
                Filter.ExpireDateStart = value.Start;
                Filter.ExpireDateEnd = value.End;
                _ = OnChange.InvokeAsync(Filter);
            }
        }
    }
    private async Task OnExpireDateRangeChange()
    {
        await OnChange.InvokeAsync(Filter);
    }

    private bool _openEnd = false;
    public async Task ToggleEndDrawer()
    {
        //Filter = filters;
        await SetFilterValue();
        _openEnd = !_openEnd;
        StateHasChanged();
    }

    async Task ClickSave()
    {
        //eZeroCore.Users.User user = new(CObj.UserId);
        //await UserObj.UpdateUserSettingsAsync();
        await OnSave.InvokeAsync();
        _openEnd = false;
        await Task.Delay(0);
    }
}

@code {

    async Task SetFilterValue()
    {
        #region...

        /*
        Filter.dayExpireMax = (Filter.dayExpireMax == 0 ? (int)dayExpire_default.e : Filter.dayExpireMax);
        dayExpire = (Filter.dayExpire, Filter.dayExpireMax);
        */

                                        Filter.ivolMax = (Filter.ivolMax == 0 ? (int)ivol_default.e : Filter.ivolMax);
        ivol = (Filter.ivol, Filter.ivolMax);

        Filter.sizeMax = (Filter.sizeMax == 0 ? (int)size_default.e : Filter.sizeMax);
        size = (Filter.size, Filter.sizeMax);

        /*
        Filter.openinterestMax = (Filter.openinterestMax == 0 ? (int)openinterest_default.e : Filter.openinterestMax);
        openinterest = (Filter.openinterest, Filter.openinterestMax);
        */

                                                        Filter.scoreMax = (Filter.scoreMax == 0 ? (int)score_default.e : Filter.scoreMax);
        score = (Filter.score, Filter.scoreMax);

        if (addStocksInput is not null)
        {
            addStocksInput.SetValue(Filter.stocks);
            excludeStocksInput.SetValue(Filter.searchSkip);

            ed_Trend.SetValue(Filter.trend.ToString());
            rd_tendency.SetValues(Filter.Tendencys);
            rd_cP.SetValue(Filter.cP.ToString());
            rd_direction.SetValue(Filter.direction.ToString());
            rd_inoutmoney.SetValue(((int)Filter.inoutmoney).ToString());

        }

        await Task.Delay(0);
        #endregion
    }

    #region Filter

    async Task OnChange_Trend(int s)
    {
        Filter.trend = s;
        await OnChange.InvokeAsync(Filter);
    }

    //趋势
    async Task OnChange_Tendency(int s)
    {
        Filter.tendency = s;
        await OnChange.InvokeAsync(Filter);
    }
    async Task OnChange_TendencyV2(List<string> values)
    {
        List<int> intValues = values
        .Select(s => int.TryParse(s, out int result) ? result : (int?)null)
        .Where(i => i.HasValue)
        .Select(i => i!.Value)
        .ToList();
        Filter.Tendencys = intValues;
        await OnChange.InvokeAsync(Filter);
        //await JS.InvokeVoidAsync("console.log", $"{(string.Join('|', values))}");
        await Task.Delay(0);
    }

    //C/P
    async Task OnChange_cP(int s)
    {
        Filter.cP = s;
        await OnChange.InvokeAsync(Filter);
    }

    //direction 成交价
    async Task OnChange_direction(string? s)
    {
        Filter.direction = s ?? "0";
        await OnChange.InvokeAsync(Filter);
    }

    //价内外
    async Task OnChange_inoutmoney(string s)
    {
        Filter.inoutmoney = (eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum)eZeroCore.ENumeric.ConvertToInt(s);
        await OnChange.InvokeAsync(Filter);
    }

    //unique
    async Task OnChange_unique(int s)
    {
        Filter.unique = s;
        await OnChange.InvokeAsync(Filter);
    }

    //行权价溢价
    //(double s, double e) absStockPrice_default = (0, 20);
    //(double s, double e) absStockPrice { get; set; } = (0, 20);

    double? DbAbsStockPrice
    {
        get { return Filter.absStockPrice; }
        set
        {
            Filter.absStockPrice = value;
            _ = OnChange.InvokeAsync(Filter);
        }
    }
    double? DbAbsStockPriceMax
    {
        get { return Filter.absStockPriceTMax; }
        set
        {
            Filter.absStockPriceTMax = value;
            _ = OnChange.InvokeAsync(Filter);
        }
    }

    int? MinStrike
    {
        get { return Filter.MinStrike == 0 ? null : Filter.MinStrike; }
        set
        {
            Filter.MinStrike = value!.Value;
            _ = OnChange.InvokeAsync(Filter);
        }
    }
    int? MaxStrike
    {
        get { return Filter.MaxStrike == 0 ? null : Filter.MaxStrike; }
        set
        {
            Filter.MaxStrike = value!.Value;
            _ = OnChange.InvokeAsync(Filter);
        }
    }

    int? MinCVolORest
    {
        get { return Filter.MinCVolORest == 0 ? null : Filter.MinCVolORest; }
        set
        {
            if (value is null)
            {
                value = 0;
            }
            Filter.MinCVolORest = value.Value;
            _ = OnChange.InvokeAsync(Filter);
        }
    }

    int? Openinterest
    {
        get { return Filter.openinterest == 0 ? null : Filter.openinterest; }
        set
        {
            Filter.openinterest = value!.Value;
            _ = OnChange.InvokeAsync(Filter);
        }
    }
    string? OpeninterestMax
    {
        get
        {
            if (Filter.openinterestMax is null || Filter.openinterestMax == 0)
            {
                return "∞";
            }
            return Filter.openinterestMax.ToString();
        }
        set
        {
            Filter.openinterestMax = eZeroCore.ENumeric.ConvertToInt(value);
            if (Filter.openinterestMax <= Filter.openinterest)
            {
                //Filter.openinterestMax += 1000;
                Filter.openinterestMax = null;
            }
            _ = OnChange.InvokeAsync(Filter);
        }
    }

    int? intDayExpire
    {
        get { return Filter.dayExpire == 0 ? null : Filter.dayExpire; }
        set
        {
            Filter.dayExpire = value!.Value;
            _ = OnChange.InvokeAsync(Filter);
        }
    }

    string? strDayExpireMax
    {
        get
        {
            if (Filter.dayExpireMax is null || Filter.dayExpireMax == 0)
            {
                return "∞";
            }
            return Filter.dayExpireMax.ToString();
        }
        set
        {
            Filter.dayExpireMax = eZeroCore.ENumeric.ConvertToInt(value);
            if (Filter.dayExpireMax <= Filter.dayExpire)
            {
                //Filter.openinterestMax += 1000;
                Filter.dayExpireMax = null;
            }
            _ = OnChange.InvokeAsync(Filter);
        }
    }

    int? Db_Filter_Premium
    {
        get
        {
            int i = Filter.premium;
            if (i == 0)
            {
                return null;
            }
            else
            {
                return i;
            }
        }
        set
        {
            if (value is null)
            {
                Filter.premium = 0;
            }
            else
            {
                Filter.premium = value.Value;
            }
            _ = OnChange.InvokeAsync(Filter);

        }
    }

    int? Db_Filter_PremiumMax
    {
        get
        {
            int i = Filter.premiumMax;
            if (i == 0)
            {
                return null;
            }
            else
            {
                return i;
            }
        }
        set
        {
            if (value is null)
            {
                Filter.premiumMax = 0;
            }
            else
            {
                Filter.premiumMax = value.Value;
            }
            _ = OnChange.InvokeAsync(Filter);

        }
    }



    //到期天数 dayExpire
    /*
    (double s, double e) dayExpire_default = (0, 999);
    (double s, double e) dayExpire { get; set; } = (0, 999);
    async Task Onchange_dayExpire((double, double) value)
    {
        dayExpire = value;
        Filter.dayExpire = (int)dayExpire.s;
        Filter.dayExpireMax = (int)dayExpire.e;
        await OnChange.InvokeAsync(Filter);
        }
    */

    //隐含波动率 ivol
    (double s, double e) ivol_default = (0, 800);
    (double s, double e) ivol { get; set; } = (0, 800);
    async Task Onchange_ivol((double, double) value)
    {
        ivol = value;
        Filter.ivol = (int)ivol.s;
        Filter.ivolMax = (int)ivol.e;
        await OnChange.InvokeAsync(Filter);
    }

    //合约数 size
    (double s, double e) size_default = (0, 500000);
    (double s, double e) size { get; set; } = (0, 500000);
    async Task Onchange_size((double, double) value)
    {
        size = value;
        Filter.size = (int)size.s;
        Filter.sizeMax = (int)size.e;
        await OnChange.InvokeAsync(Filter);
    }


    //分数 score
    (double s, double e) score_default = (0, 130);
    (double s, double e) score { get; set; } = (0, 130);
    async Task Onchange_score((double, double) value)
    {
        score = value;
        Filter.score = (int)score.s;
        Filter.scoreMax = (int)score.e;
        await OnChange.InvokeAsync(Filter);
    }

    E_InputTags addStocksInput;
    async Task HandleAddStocksInputConfirm(List<string> stocks)
    {
        Filter.stocks = stocks;
        await JS.InvokeVoidAsync("console.log", $"【--筛选的:{string.Join(',', stocks)}--】");
        await OnChange.InvokeAsync(Filter);
    }

    E_InputTags excludeStocksInput;
    async Task HandleexcludeStocksInputConfirm(List<string> stocks)
    {
        Filter.searchSkip = stocks;
        await JS.InvokeVoidAsync("console.log", $"【--黑名单:{string.Join(',', stocks)}--】");
        await OnChange.InvokeAsync(Filter);
    }


    string AddStocks
    {
        get
        {
            return string.Join(',', Filter.stocks);
        }
        set
        {
            Filter.stocks = GetSplit(value).Distinct().ToList();
            _ = OnChange.InvokeAsync(Filter);
        }
    }
    string SearchSkip
    {
        get
        {
            return string.Join(',', Filter.searchSkip);
        }
        set
        {
            Filter.searchSkip = GetSplit(value).Distinct().ToList();
            _ = OnChange.InvokeAsync(Filter);
        }
    }


    //买卖
    E_CkBoxList ed_Trend;
    List<string> GetTrendTitleValue(bool isTitle = true)
    {
        #region...
        List<string> list = [];
        if (isTitle)
        {
            list.Add("全部");
            list.Add("买方");
            list.Add("卖方");
        }
        else
        {
            list.Add("0");
            list.Add("1");
            list.Add("2");
        }
        return list;
        #endregion
    }
    //趋势
    E_CkBoxList rd_tendency;
    List<string> GetTendency_TitleValue(bool isTitle = true)
    {
        #region...
        List<string> list = [];
        if (isTitle)
        {
            list.Add("全部");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方很看涨}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方看涨}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方很看跌}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方看跌}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方很看涨}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方看涨}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方很看跌}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方看跌}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.接近买价成交}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.接近卖价成交}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.中性}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.未知}");
        }
        else
        {
            list.Add("0");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方很看涨)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方看涨)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方很看跌)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方看跌)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方很看涨)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方看涨)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方很看跌)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方看跌)}");

            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.接近买价成交)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.接近卖价成交)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.中性)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.未知)}");
        }
        return list;
        #endregion
    }
    E_CkBoxList rd_cP;
    List<string> GetRd_cP_TitleValue(bool isTitle = true)
    {
        #region...
        List<string> list = [];
        if (isTitle)
        {
            list.Add("全部");
            list.Add("CALL");
            list.Add("PUT");
        }
        else
        {
            list.Add("0");
            list.Add("1");
            list.Add("2");
        }
        return list;
        #endregion
    }

    //成交价
    E_CkBoxList rd_direction;
    List<string> GetRd_direction_TitleValue(bool isTitle = true)
    {
        #region...
        List<string> list = [];
        if (isTitle)
        {
            list.Add("全部");
        }
        else
        {
            list.Add("0");
        }
        list.Add("高于卖价");
        list.Add("就在买价");
        list.Add("就在卖价");
        list.Add("接近买价");
        list.Add("接近卖价");
        list.Add("低于买价");
        list.Add("低于卖价");
        return list;
        #endregion
    }

    E_CkBoxList rd_inoutmoney;
    List<string> GetRd_inoutmoney_TitleValue(bool isTitle = true)
    {
        #region...
        List<string> list = [];
        if (isTitle)
        {
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.全部}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.价内}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.等价}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.价外}");
        }
        else
        {
            list.Add("-2");
            list.Add("-1");
            list.Add("0");
            list.Add("1");
        }

        return list;
        #endregion
    }

    E_CkBoxList rd_unique;
    List<string> GetRd_unique_TitleValue(bool isTitle = true)
    {
        #region...
        List<string> list = [];
        if (isTitle)
        {
            list.Add("全部");
            list.Add("是");
            list.Add("否");
        }
        else
        {
            list.Add("0");
            list.Add("1");
            list.Add("2");
        }
        return list;
        #endregion
    }

    #endregion

    async Task ResetFilter()
    {
        #region...
        /*
        if (addStocksInput is not null)
        {
            addStocksInput.Clear();
            }
        if (excludeStocksInput is not null)
        {
            excludeStocksInput.Clear();
            }
        */
        if (ed_Trend is not null)
        {
            await ed_Trend.ClearSelections();
        }
        if (rd_tendency is not null)
        {
            await rd_tendency.ClearSelections();
        }
        if (rd_cP is not null)
        {
            await rd_cP.ClearSelections();
        }
        if (rd_direction is not null)
        {
            await rd_direction.ClearSelections();
        }
        if (rd_inoutmoney is not null)
        {
            await rd_inoutmoney.ClearSelections();
        }
        if (rd_unique is not null)
        {
            await rd_unique.ClearSelections();
        }
        Filter.trend = 0;
        Filter.tendency = 0;
        Filter.Tendencys = [];
        Filter.premium = 0;
        Filter.cP = 0;
        Filter.direction = "0";
        Filter.inoutmoney = eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.全部;
        Filter.unique = 0;
        Filter.searchSkip = [];
        Filter.stocks = [];

        //清空过滤条件
        // 重置权利金范围
        Filter.premium = 0;
        Filter.premiumMax = 0;
        // 重置行权价溢价范围
        Filter.absStockPrice = null;
        Filter.absStockPriceTMax = null;
        // 重置行权价范围
        Filter.MinStrike = 0;
        Filter.MaxStrike = 0;
        // 重置到期天数范围
        Filter.dayExpire = 0;
        Filter.dayExpireMax = 0;

        //absStockPrice = absStockPrice_default;
        Filter.absStockPrice = null;
        //Filter.absStockPriceMax = absStockPrice_default.e / 100;

        //dayExpire = dayExpire_default;
        //Filter.dayExpire = (int)dayExpire_default.s;
        //Filter.dayExpireMax = (int)dayExpire_default.e;

        ivol = ivol_default;
        Filter.ivol = (int)ivol_default.s;
        Filter.ivolMax = (int)ivol_default.e;

        size = size_default;
        Filter.size = (int)size_default.s;
        Filter.sizeMax = (int)size_default.e;

        //openinterest = openinterest_default;
        //Filter.openinterest = (int)openinterest.s;
        //Filter.openinterestMax = (int)openinterest.e;

        score = score_default;
        Filter.score = (int)score.s;
        Filter.scoreMax = (int)score.e;

        Filter.MinCVolORest = 0;
        Filter.MinStrike = 0;
        eZeroCore.Web.Stk.Data.Dt_StockOption.Filters newfilter = new();
        eZeroCore.Db.DbObject.CopyObject(newfilter, Filter);
        await OnChange.InvokeAsync(newfilter);
        #endregion
    }

    string CssCard = "card m-1 mb-2 border border-warning-subtle d-flex flex-row align-items-center";
    string CssHeader = "flex-shrink-0 w-110px text-end me-2 py-3";
    string CssBody = "card-body flex-grow-1 bg-dark-subtle border-start p-0 px-1";

    //onclick="WeiCity.toggleActive('sidebarFilter')"
}

@* 只有当_openEnd为true时才渲染过滤器 *@
@if (_openEnd)
{
    <!-- 背景遮罩层，用于点击外部关闭过滤器 -->
    <div class="position-fixed top-0 start-0 w-100 h-100" 
         style="z-index: 1299; background: transparent;" 
         @onclick="@(async () => { _openEnd = false; StateHasChanged(); })">
    </div>
    
    <!-- 使用MudDrawer替代自定义fixed定位，确保日期选择器能正常展示 -->
    <MudDrawer @bind-Open="@_openEnd" Fixed="false" Anchor="Anchor.End" Elevation="0" 
               Variant="@DrawerVariant.Temporary" Overlay="false" Class="bg-body" 
               Width="min(450px, 90vw)" >
        <div style="padding:7px 0px 0px 5px;height:40px !important;display:flex; align-items:center;justify-content:space-between">
            <h6 class="mb-0" style="height:30px;">
                <i class="fal fa-filter me-2"></i>
                @(string.IsNullOrWhiteSpace(Filter.FilterName) ? "未命名过滤器" : Filter.FilterName.ToUpper())
                <sub class="opacity-50">
                    Var:1.80
                </sub>
            </h6>
            <!-- 关闭按钮 -->
            <MudIconButton Icon="@Icons.Material.Filled.Close" 
                          Color="Color.Default" 
                          Size="Size.Small"
                          OnClick="@(async () => { _openEnd = false; StateHasChanged(); })" />
        </div>

        <div class="p-0" @onclick:stopPropagation="true">

        <div class="@CssCard">
            <div class="@CssHeader">
                过滤器名称
            </div>
            <div class="@CssBody">
                <input @bind-value=@Filter.FilterName maxlength="8"
                       class="form-control form-control-sm Upper" type="text" placeholder="命名此自定义过滤器">
            </div>
        </div>

        <div class="@CssCard d-none">
            <div class="@CssHeader">
                买卖
            </div>
            <div class="@CssBody">

                <E_CkBoxList UseRadioButtons="true" SetDefaultSelectedValue="@Filter.trend.ToString()" @ref=@ed_Trend OnSelectionChanged="(List<string> o) => { _ = OnChange_Trend(eZeroCore.ENumeric.ConvertToInt(o.FirstOrDefault()!)); }" Titles="GetTrendTitleValue()"
                             Values="@GetTrendTitleValue(false)" />
            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                趋势
            </div>
            <div class="@CssBody">

                <E_CkBoxList SetDefaultSelectedIndexValue="@Filter.Tendencys" @ref=@rd_tendency OnSelectionChanged="@OnChange_TendencyV2" SelectFirstClearOther="true"
                             Titles="GetTendency_TitleValue()"
                             Values="GetTendency_TitleValue(false)" />

            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                C/P
            </div>
            <div class="@CssBody">

                <E_CkBoxList UseRadioButtons="true" SetDefaultSelectedValue="@Filter.cP.ToString()" @ref=@rd_cP OnSelectionChanged="(List<string> o) => { _ = OnChange_cP(eZeroCore.ENumeric.ConvertToInt(o.FirstOrDefault()!)); }"
                             Titles="GetRd_cP_TitleValue()"
                             Values="GetRd_cP_TitleValue(false)" />
            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                成交价
            </div>
            <div class="@CssBody">

                <E_CkBoxList UseRadioButtons="true" SetDefaultSelectedValue="@Filter.direction.ToString()" @ref=@rd_direction OnSelectionChanged="(List<string> o) => { _ = OnChange_direction(o.FirstOrDefault()!); }"
                             Titles="GetRd_direction_TitleValue()"
                             Values="GetRd_direction_TitleValue(false)" />
            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                价内外
            </div>
            <div class="@CssBody">

                <E_CkBoxList UseRadioButtons="true" SetDefaultSelectedValue="@(((int)Filter.inoutmoney).ToString())" @ref=@rd_inoutmoney OnSelectionChanged="(List<string> o) => { _ = OnChange_inoutmoney(o.FirstOrDefault()!); }"
                             Titles="GetRd_inoutmoney_TitleValue()"
                             Values="GetRd_inoutmoney_TitleValue(false)" />

            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                是否平仓
            </div>
            <div class="@CssBody">

                <E_CkBoxList UseRadioButtons="true" SetDefaultSelectedValue="@Filter.unique.ToString()" @ref=@rd_unique OnSelectionChanged="(List<string> o) => { _ = OnChange_unique(eZeroCore.ENumeric.ConvertToInt(o.FirstOrDefault()!)); }"
                             Titles="GetRd_unique_TitleValue()"
                             Values="GetRd_unique_TitleValue(false)" />

            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                权利金
            </div>
            <div class="@CssBody">

                @* <input @bind-value=@Db_Filter_Premium maxlength="5" class="form-control form-control-sm" type="number" placeholder="输入数值，例如：5"> *@
                <div class="input-group input-group-sm">
	                <span class="input-group-text">最小值</span>
                    <input @bind-value=@Db_Filter_Premium type="text" class="form-control" placeholder="0">
	                <span class="input-group-text">最大值</span>
                    <input @bind-value=@Db_Filter_PremiumMax type="text" class="form-control">
                </div>
            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                行权价溢价
            </div>
            <div class="@CssBody">
	            @* <input @bind-value=@DbAbsStockPrice maxlength="5" class="form-control form-control-sm" type="number" placeholder="输入数值，例如：0.3"> *@
	            <div class="input-group input-group-sm">
		            <span class="input-group-text">最小值</span>
                    <input @bind-value=@DbAbsStockPrice type="text" class="form-control" placeholder="0.0">
		            <span class="input-group-text">最大值</span>
                    <input @bind-value=@DbAbsStockPriceMax type="text" class="form-control">
	            </div>
            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                最小行权价
            </div>
            <div class="@CssBody">
	            @* <input @bind-value=@MinStrike maxlength="4" class="form-control form-control-sm" type="number" placeholder="输入数值，例如：1000"> *@
	            <div class="input-group input-group-sm">
		            <span class="input-group-text">最小值</span>
		            <input @bind-value=@MinStrike type="text" class="form-control" placeholder="0">
		            <span class="input-group-text">最大值</span>
		            <input @bind-value=@MaxStrike type="text" class="form-control">
	            </div>
            </div>
        </div>

        @*
<div class="@CssCard">
            <div class="@CssHeader">
                成交量/合约数
            </div>
            <div class="@CssBody">
                <input @bind-value=@MinCVolORest maxlength="4" class="form-control form-control-sm" type="number" placeholder="输入数值（最小值）">
            </div>
        </div>*@



        <div class="@CssCard">
            <div class="@CssHeader">
                未平仓数量
            </div>
            <div class="@CssBody">
                <div class="input-group input-group-sm">
                    <span class="input-group-text">最小值</span>
                    <input @bind-value=@Openinterest type="text" class="form-control" placeholder="0">
                    <span class="input-group-text">最大值</span>
                    <input @bind-value=@OpeninterestMax type="text" class="form-control">
                </div>
                @*
                EZSilder MinMaxValue=openinterest_default
                SetValue=openinterest
                Step="10"
                OnChangeAfter=Onchange_openinterest
                *@
            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                到期天数
            </div>
            <div class="@CssBody">
                @* <div class="input-group input-group-sm"> *@
                @*     <span class="input-group-text">最小值</span> *@
                @*     <input @bind-value=@intDayExpire type="text" class="form-control" placeholder="0"> *@
                @*     <span class="input-group-text">最大值</span> *@
                @*     <input @bind-value=@strDayExpireMax type="text" class="form-control"> *@
                @* </div> *@
				
                <MudDateRangePicker Culture="@CultureInfo.GetCultureInfo("zh-Hans")"
                                    PickerVariant="PickerVariant.Dialog"
                                    ShowToolbar="false"
                                    TitleDateFormat="yyyy,MM,dd"
                                    FirstDayOfWeek="DayOfWeek.Sunday"
                                    Clearable="true"
                                    DateFormat="MM/dd/yy"
                                    @bind-DateRange="ExpireDateRange" />

                @*
                EZSilder MinMaxValue=openinterest_default
                SetValue=openinterest
                Step="10"
                OnChangeAfter=Onchange_openinterest
                *@
            </div>
        </div>        

        <div class="@CssCard">
            <div class="@CssHeader">
                隐含波动率
            </div>
            <div class="@CssBody">
                <EZSilder MinMaxValue=ivol_default
                          SetValue=ivol
                          Step="10"
                          OnChangeAfter=Onchange_ivol />

            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                合约数
            </div>
            <div class="@CssBody">
                <EZSilder MinMaxValue=size_default
                          SetValue=size
                          Step="10"
                          OnChangeAfter=Onchange_size />
            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                分数
            </div>
            <div class="@CssBody">
                <EZSilder MinMaxValue=score_default
                          SetValue=score
                          Step="1"
                          OnChangeAfter=Onchange_score />
            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                包含股票
            </div>
            <div class="@CssBody">


                <input @bind-value=@AddStocks class="form-control" type="text" placeholder="包含代号（用美式,分割）">

            </div>
        </div>

        <div class="@CssCard">
            <div class="@CssHeader">
                排除股票
            </div>
            <div class="@CssBody">

                <input @bind-value=@SearchSkip class="form-control" type="text" placeholder="排除代号（用美式,分割）">

            </div>
        </div>

        <div class="text-center p-2">
            <div class="btn-group w-100">
                <Ez_Btn ClassName="btn btn-phoenix-success" Style="min-width:70%;" WaitTimeMs="600" OnBtnClick="@ClickSave">
                    <i class="far fa-save me-2"></i>
                    保存此过滤器
                </Ez_Btn>
                <Ez_Btn ClassName="btn btn-phoenix-warning" OnBtnClick="@ResetFilter" WaitTimeMs="600" Style="min-width:30%;">
                    <i class="fas fa-redo-alt me-2"></i>
                    清空
                </Ez_Btn>
            </div>
        </div>

        </div>

    </MudDrawer>
}

@code
{
    string[] GetSplit(string currentInput)
    {
        if (!string.IsNullOrWhiteSpace(currentInput))
        {
            // 拆分输入，假设使用逗号、分号、空格作为分隔符
            currentInput = currentInput.Replace("，", ",");
            currentInput = currentInput.Replace("；", ";");
            return currentInput
                .Split(new[] { ',', ';', ' ' }, StringSplitOptions.RemoveEmptyEntries);
        }
        return [];
    }
}