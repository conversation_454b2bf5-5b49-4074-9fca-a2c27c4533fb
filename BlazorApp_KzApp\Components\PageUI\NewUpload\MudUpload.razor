﻿@using <PERSON><PERSON><PERSON>
@inject IJSRuntime JS
@inject ISnackbar SnackbarObj
@inject IDialogService DialogService
@inject NavigationManager NavigationManagerObj

@code {

    [Parameter]
    public bool ThinProgress { get; set; }
    [Parameter]
    public string ChooseTip { get; set; } = "Select or Drag and drop a file";

    [Parameter]
    public EventCallback<string> OnUploadCallback { get; set; }

    [Parameter]
    public EventCallback<eZeroCore.Azure.UploadFile.Result> OnUploadResult { get; set; }

    [Parameter]
    public eZeroCore.Azure.UploadResult.FileType FileType { get; set; } = eZeroCore.Azure.UploadResult.FileType.Image;

    //https://weicity.blob.core.windows.net/test0-web-202502-weicity/1225238908altar_of_sacrifice_by_phoenixreturns_din697o-pre.jpg
    [Parameter]
    public string ImageUrl { get; set; } = "";

    /*
    * Recommended 2560x1080（21:9）
    * Recommended 1920x480（4:1）
    * Recommended 2880x1600 16:9
   */
    /// <summary>
    /// 16x9 21x9 4x1
    /// </summary>
    [Parameter]
    public string Ratio { get; set; } = "16x9";

    string sDomId = "";
    string AcceptFileType = "";
    protected override Task OnInitializedAsync()
    {
        sDomId = eZeroCore.EString.GetBase64HashID("UploadDocPhoto" + ChooseTip) + Math.Abs(Guid.NewGuid().ToString().GetHashCode());
        _backgroundImageUrl = ImageUrl;
        return base.OnInitializedAsync();
    }

    public void SetImage(string url)
    {
        _backgroundImageUrl = url;
        StateHasChanged();
    }
}

<MudStack @key=@sDomId Class="@($"position-relative {_dragClass}")">
    <MudFileUpload T="IBrowserFile"
                   @ref="_fileUpload"
                   OnFilesChanged="OnInputFileChanged"
                   Hidden="false"
                   InputClass="start-0 absolute mud-width-full mud-height-full overflow-hidden z-10"
                   InputStyle="opacity:0"
                   tabindex="-1"
                   @ondrop="ClearDragClass"
                   @ondragover="SetDragClass"
                   @ondragenter="SetDragClass"
                   @ondragleave="ClearDragClass"
                   @ondragend="ClearDragClass"
                   Accept="@(eZeroCore.Azure.UploadResult.GetAccept(FileType))"
                   MaxFiles="1">       
        <ActivatorContent>
            <MudPaper Outlined="true"
                      Class="@($"card ratio ratio-{Ratio}")"
                      Style="@(_backgroundImageUrl is not null ? $"background: url('{_backgroundImageUrl}') no-repeat top center;background-size: cover;" : "")">

                <div class="position-relative position-absolute top-50 start-50 translate-middle">
                    <div class="position-absolute top-50 start-50 translate-middle">
                        <div class="mb-5 text-shadow">
                            <i class="fa-solid fa-cloud-arrow-up fa-lg me-2"></i>
                            @(new MarkupString(ChooseTip))
                        </div>
                        <div class="progress @(_isUploading ? "" : "d-none")" style="min-width:168px;min-height:10px;">
                            <div class="progress-bar @(ThinProgress ? "progressThin" : "")" role="progressbar" style="width:@(_uploadProgress)%">
                                <small class="fs-10">
                                     @(_uploadProgress.ToString("F2"))%
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

            </MudPaper>
        </ActivatorContent>
    </MudFileUpload>
</MudStack>

@code {
    private const string DefaultDragClass = "relative rounded-lg border-1 border-dashed border-secondary-subtle mud-width-full mud-height-full";
    private string _dragClass = DefaultDragClass;
    private string? _fileName;
    private MudFileUpload<IBrowserFile>? _fileUpload;
    private bool _isUploading = false; // 控制上传进度指示
    private readonly HttpClient _httpClient = new HttpClient();
    private string? _backgroundImageUrl;
    private double _uploadProgress = 0;


    private async Task ClearAsync()
    {
        await (_fileUpload?.ClearAsync() ?? Task.CompletedTask);
        _fileName = null;
        ClearDragClass();
    }

    private void SetDragClass()
    {
        _dragClass = $"{DefaultDragClass} mud-border-primary";
        _ = Generic.JsConsole(JS, _dragClass);
    }
    private void ClearDragClass() => _dragClass = DefaultDragClass;

    private async void OnInputFileChanged(InputFileChangeEventArgs e)
    {
        ClearDragClass();
        var file = e.File;
        if (file is not null)
        {
            _fileName = file.Name;
            await UploadFile(file); // 立即上传文件
        }
    }

    private async Task UploadFile(IBrowserFile file)
    {
        try
        {
            _isUploading = true;
            _uploadProgress = 0;
            StateHasChanged(); // 触发 UI 更新

            using var content = new MultipartFormDataContent();
            var fileStream = file.OpenReadStream(file.Size);

            // 使用 ProgressStream 包装文件流，传入总字节数和回调方法更新进度
            var progressStream = new ProgressStream(fileStream, file.Size, progress =>
            {
                _uploadProgress = progress;  // progress 为 0~100 的百分比
                _ = Generic.JsConsole(JS, _uploadProgress);
                InvokeAsync(StateHasChanged);
            });
            var streamContent = new StreamContent(progressStream);
            streamContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(file.ContentType);
            content.Add(streamContent, "file", file.Name);

            var response = await _httpClient.PostAsync($"{NavigationManagerObj.ToAbsoluteUri("/api/upload")}", content);

            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync();
                var jsonResponse = System.Text.Json.JsonDocument.Parse(responseContent);

                if (jsonResponse.RootElement.TryGetProperty("location", out var filePathElement) &&
                    jsonResponse.RootElement.TryGetProperty("size", out var sizeElement))
                {
                    string fileUrl = filePathElement.GetString() ?? "";
                    long fileSize = sizeElement.GetInt64();

                    _backgroundImageUrl = fileUrl; // 保存图片地址
                    await OnUploadCallback.InvokeAsync(fileUrl);                    
                    await OnUploadResult.InvokeAsync(new eZeroCore.Azure.UploadFile.Result()
                    {
                        FileUrl=fileUrl,
                        Size= fileSize
                    });
                    await Generic.JsConsole(JS, fileUrl);
                    //SnackbarObj.Add($"Upload successful! File URL: {fileUrl}, Size: {fileSize} bytes", Severity.Success);
                }
                else
                {
                   //SnackbarObj.Add("Upload successful, but no file path or size returned.", Severity.Warning);
                }
            }
            else
            {
                SnackbarObj.Add($"Image upload failed: {response.ReasonPhrase}", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            SnackbarObj.Add($"Error: {ex.Message}", Severity.Error);
        }
        finally
        {
            _isUploading = false;
            StateHasChanged(); // 更新 UI
        }
    }

    // 自定义的流包装类，用于报告读取进度
    private class ProgressStream : Stream
    {
        private readonly Stream _innerStream;
        private readonly long _totalBytes;
        private readonly Action<double> _progressCallback;
        private long _bytesRead;

        public ProgressStream(Stream innerStream, long totalBytes, Action<double> progressCallback)
        {
            _innerStream = innerStream;
            _totalBytes = totalBytes;
            _progressCallback = progressCallback;
            _bytesRead = 0;
        }

        public override bool CanRead => _innerStream.CanRead;
        public override bool CanSeek => _innerStream.CanSeek;
        public override bool CanWrite => _innerStream.CanWrite;
        public override long Length => _innerStream.Length;
        public override long Position { get => _innerStream.Position; set => _innerStream.Position = value; }

        public override void Flush() => _innerStream.Flush();

        public override int Read(byte[] buffer, int offset, int count)
        {
            int read = _innerStream.Read(buffer, offset, count);
            ReportProgress(read);
            return read;
        }

        public override async Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken)
        {
            int read = await _innerStream.ReadAsync(buffer, offset, count, cancellationToken);
            ReportProgress(read);
            return read;
        }

        private void ReportProgress(int readBytes)
        {
            if (readBytes > 0)
            {
                _bytesRead += readBytes;
                double progress = ((double)_bytesRead / _totalBytes) * 100;
                _progressCallback?.Invoke(progress);
            }
        }

        public override long Seek(long offset, SeekOrigin origin) => _innerStream.Seek(offset, origin);
        public override void SetLength(long value) => _innerStream.SetLength(value);
        public override void Write(byte[] buffer, int offset, int count) => _innerStream.Write(buffer, offset, count);
    }

    private class UploadResponse
    {
        public string? Location { get; set; }
        public long Size { get; set; }
    }
}
