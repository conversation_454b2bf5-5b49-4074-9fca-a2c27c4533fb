﻿@using System.Text.Json
@using System.Text.Json.Serialization
@using <PERSON><PERSON><PERSON>

@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public EventCallback OnInitialized { get; set; }

    DateRange? _OnSetDateRange = null;
    [CascadingParameter]
    public DateRange? OnSetDateRange
    {
        get { return _OnSetDateRange; }
        set
        {
            if (_OnSetDateRange is null && value is not null ||
                (_OnSetDateRange is not null && !_OnSetDateRange.Equals(value)))
            {
                _OnSetDateRange = value;
                _ = Init();
            }
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await Init(); // 首次渲染后自动初始化
        }
    }

    [Parameter]
    public EventCallback<string> OnSearchStock { get; set; }

    string _SearchString = "";
    string SearchString
    {
        get { return _SearchString; }
        set
        {
            _SearchString = value;
            OnSearchStock.InvokeAsync(value);
        }
    }

    Settings settings = new();

    int SelectedChartIndex = 0;

    async Task SwitchChart(int index = 0)
    {
        SelectedChartIndex = index;
        await Init(false);
        await Task.Delay(0);
    }

    RzSearchInputBox inputBox;

    async Task SearchButtonClick()
    {
        await inputBox.HandleSearchKeyDown();
    }

}

<_UIFramework FullWidth="true" HeaderContentClass="d-xl-flex justify-content-between align-items-center">

    <HeaderContent>

        <div class="py-2 text-center">
            <b>
                实时期权 Call vs Put 总权利金
            </b>
            <span class="badge badge-phoenix badge-phoenix-info py-1">
                @(string.IsNullOrEmpty(SearchString) ? "整个市场" : $"搜索：{SearchString}")
                <span>
                    &nbsp;
                </span>
                @if (OnSetDateRange is not null)
                {
                    if (OnSetDateRange.Start is not null)
                    {
                        @OnSetDateRange.Start.Value.ToString(settings.SettingsEntity.ShortDateFormat)
                    }
                    if (OnSetDateRange.End is not null)
                    {
                        if (OnSetDateRange.Start is not null)
                        {
                            <span>
                                -
                            </span>
                        }
                        @OnSetDateRange.End.Value.ToString(settings.SettingsEntity.ShortDateFormat)
                    }
                }

            </span>
        </div>
        <div class="py-2 text-center">
            <div class="btn-group">
                <button @onclick="()=>SwitchChart(0)" type=" button"
                        class="btn fs-9 py-1 mb-0 @(SelectedChartIndex==0?"btn-warning":"btn-phoenix-warning")">
                    Call VS Put
                </button>
                <button @onclick="()=>SwitchChart(1)" type="button"
                        class="btn fs-9 py-1 mb-0 @(SelectedChartIndex==1?"btn-warning":"btn-phoenix-warning")">
                    看涨 VS 看跌
                </button>
            </div>
        </div>

        <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert=true>
            <div class="py-2 text-center">
                <RzSearchInputBox @ref=inputBox OnSearch="@OnSearch" Placeholder="搜索代号" DefaultStock="@SearchString" />
            </div>
        </MudHidden>

        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <div class="py-2 text-center">
                <RzSearchInputBox @ref=inputBox OnSearch="@OnSearch" Placeholder="搜索代号" DefaultStock="@SearchString" />
                <_SearchButton Text="搜索" OnClick="@SearchButtonClick" />
            </div>
        </MudHidden>

    </HeaderContent>

    <ChildContent>

        <div id="@DomId" style="width: 100%; height:360px;" class="eChartDom"></div>

    </ChildContent>

</_UIFramework>

@code {

    string DomId = $"StockOptionIntradayMinuteChart{nameof(_ChartPanel)}";

    // 搜索并刷新图表
    public async Task OnSearch(string value)
    {
        SearchString = value.ToUpper();
        if (AppSettings.IsTestModeAll)
        {
            await JS.InvokeVoidAsync("console.log", $"OnSearch:【{SearchString}】1");
        }
        _ = Init();
        //StateHasChanged();
        await Task.Delay(0);
    }

    bool hasLoad = false;
    // 初始化图表数据
    public async Task Init(bool update = false)
    {
        #region...
        if (hasLoad)
        {
            return;
        }
        hasLoad = true;
        if (!update)
        {
            await Generic.LoadingDom(JS, DomId);
        }

        DateTime start = OnSetDateRange?.End ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime();
        DateTime endDate = OnSetDateRange?.End ?? eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().AddDays(1);

        //eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime()
        List<eZeroCore.Web.Stk.Data.StockOptionIntradayMinute> lists = [];
        eZeroCore.Web.Stk.Data.StockOptionIntradayMinute.DataList dataList = new()
            {
                SetDateTime = start,
                EndDateTime = endDate,
                StockSearch = SearchString.ToUpper()
            };
        lists = await dataList.ListAsync();

        string y1Json = "";
        string y2Json = "";
        string xJson = "";
        string lineJson = "";
        string sLabs = "";

        if (SelectedChartIndex == 0)
        {
            sLabs = "Put总权利金,Call总权利金,代号走势（默认标普500）";
            y1Json = JsonSerializer.Serialize(lists.Select(item => item.PutPremium).ToList());
            y2Json = JsonSerializer.Serialize(lists.Select(item => item.CallPremium).ToList());
        }
        else
        {
            sLabs = "看跌总权利金,看涨总权利金,代号走势（默认标普500）";
            y1Json = JsonSerializer.Serialize(lists.Select(item => item.DownPremium).ToList());
            y2Json = JsonSerializer.Serialize(lists.Select(item => item.UpPremium).ToList());
        }
        xJson = JsonSerializer.Serialize(lists.Select(item => item.CreateTime.ToString("MM月dd HH:mm")).ToList());
        lineJson = JsonSerializer.Serialize(lists.Select(item => item.StockPrice).ToList());

        if (CObj.UserIsAdmin || AppSettings.IsTestModeAll)
        {
            await JS.InvokeVoidAsync("console.log", $"【_ChartPanel_SqlCommandText】", dataList.SqlCommandText);
        }

        if (lists.Count > 0)
        {
            await JS.InvokeVoidAsync($"StockOptionIntradayMinuteChart.init", DomId, y1Json, y2Json, xJson, lineJson, sLabs, update, SelectedChartIndex);

            if (AppSettings.IsTestModeAll)
            {
                await JS.InvokeVoidAsync("console.log", $"【_ChartPanel_{update}】.Init({start})", dataList.SqlCommandText);
            }
        }
        else
        {
            if (CObj.UserIsAdmin || AppSettings.IsTestModeAll)
            {
                await JS.InvokeVoidAsync("console.log", $"【_ChartPanel_{update}】", dataList.SqlCommandText, y1Json, y2Json, xJson, lineJson, sLabs);
            }
        }

        // 图表渲染完成后，触发初始化完成事件
        if (OnInitialized.HasDelegate) // 检查是否有父组件订阅
        {
            await OnInitialized.InvokeAsync(); // 通知父组件：当前组件已初始化完成
        }

        await Generic.LoadingDom(JS, DomId, false);

        await Task.Delay(200);
        hasLoad = false;

        #endregion
    }

}