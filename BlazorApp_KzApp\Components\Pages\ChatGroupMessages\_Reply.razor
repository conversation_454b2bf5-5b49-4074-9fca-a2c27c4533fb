@using eZeroSignalRMsg.SignalR;
@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; } 

    [Parameter]
    public UserMessage ReplyToMsg { get; set; } = new();

    [Parameter]
    public EventCallback OnCancelReply { get; set; }

    string oldMsgId { get; set; } = "";
    string sReplyContent { get; set; } = "";
    protected override Task OnParametersSetAsync()
    {
        if (!oldMsgId.Equals(ReplyToMsg.MsgId,StringComparison.OrdinalIgnoreCase))
        {
            if (AppSettings.IsTestModeAll)
            {
                _ = JS.InvokeVoidAsync("eval", $"console.log('_Reply.razor_OnParametersSetAsync')");
            }
            oldMsgId = ReplyToMsg.MsgId;
            sReplyContent = "";
            if (!string.IsNullOrWhiteSpace(ReplyToMsg.MsgContent))
            {
                sReplyContent = ReplyToMsg.MsgContent;
            }
            else if (!string.IsNullOrEmpty(ReplyToMsg.IncludeUrls) && !string.IsNullOrEmpty(ReplyToMsg.IncludeUrlFirstTitle))
            {
                sReplyContent = ReplyToMsg.IncludeUrlFirstTitle;
            }
            else if (ReplyToMsg.Images.Count > 0)
            {
                sReplyContent = "Photo";
            }
            StateHasChanged();
        }
        return base.OnParametersSetAsync();
    }

    async Task CancelReply()
    {
        await OnCancelReply.InvokeAsync();
    }
}

@if (!string.IsNullOrWhiteSpace(sReplyContent))
{
    <div class="ezMsgBoxReply position-relative p-2 px-7 mb-n1 border-top">

        <div class="position-absolute top-50 start-0 translate-middle-y ps-3">
            <i class="fa-solid fa-reply fa-xl text-info-light"></i>
        </div>

        <div>
            <label class="d-block text-info-light titleHeader fw-normal name text-nowrap Nickname me-2">
                @ReplyToMsg.UserNickname
            </label>
            <div class="w-100 text-nowrap text-truncate truncate-container mb-0 contentHtml">
                @EString.DropHtmlAllTag(sReplyContent)
            </div>
        </div>

        <button type="button" class="position-absolute top-50 end-0 translate-middle-y pe-4 fs-6 hand" @onclick=@CancelReply>
            <i class="fa-solid fa-xmark"></i>
        </button>

    </div>
}
