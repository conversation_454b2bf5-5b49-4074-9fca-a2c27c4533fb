﻿@using <PERSON><PERSON><PERSON>App_KzApp.Components.Pages.ChatGroupMessages
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using eZeroCore.Web.Stk
@using BlazorApp_KzApp.Components.Pages.FrontEnd
@using BlazorApp_KzApp.Components.Pages.ZBK
@inject IJSRuntime JS

@page "/AIModelStock/{param?}"

@code {
    [Parameter]
    public string param { get; set; } = "";

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    string SelectedStock { get; set; } = "";

    DateTime? DateTimeStart { get; set; }

    protected override Task OnInitializedAsync()
    {
        DateTimeStart = WebDefaultValue.GetLastOpenMarketDateTime();
        return base.OnInitializedAsync();
    }
    string LeftDomId = $"{nameof(AIModelStockIndex)}";
    bool LiveStatus { get; set; } = true;
    async void GetDateTimeStart(DateTime? newDateTime)
    {
        await Generic.LoadingDom(JS, LeftDomId);
        DateTimeStart = newDateTime;
        if (_AIModel is not null)
        {
            await _Candlestick.SecTickEvent(newDateTime.Value.AddDays(1));
            await _Candlestick.Init(SelectedStock, true, DateTimeStart);
            await _AIModel.Init(newDateTime: newDateTime);
            LiveStatus = false;
            //await OnSendTickMinEvent();
        }
        await Generic.LoadingDom(JS, LeftDomId, false);
    }

    /*
    async Task Refresh()
    { 
        await Generic.LoadingDom(JS, LeftDomId);
        await Task.Delay(100);
        await _AIModel.Init(newDateTime: DateTimeStart);
        await Generic.LoadingDom(JS, LeftDomId, false);
    }
    */

    _CandlestickChart _Candlestick;
    _AIModelSelector _AIModel;
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !string.IsNullOrEmpty(SelectedStock))
        {
            await OnSelectStock(SelectedStock);
        }
    }

    async Task OnSendTickMinEvent()
    {
        if (LiveStatus)
        {
            await _AIModel.Init();
            await _Candlestick.Init(SelectedStock, true, DateTimeStart);
            await _Candlestick.SecTickEvent();
            await JS.InvokeVoidAsync("console.log", $"{nameof(OnSendTickMinEvent)}:{DateTime.Now:f}");
            await Task.Delay(0);
        }
    }

    async Task OnSelectStock(string stock)
    {
        SelectedStock = stock.ToUpper();
        if (_Candlestick is not null && !string.IsNullOrEmpty(SelectedStock))
        {
            await _Candlestick.Init(SelectedStock, start: DateTimeStart);
        }
        if (left_Right_Framework is not null)
        {
            await left_Right_Framework.CloseLeftContent();
        }
    }
    _UI_Left_Right_Framework left_Right_Framework;
    //StkSubVisbile="!CObj.BlazorRefresh && !eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new(CObj.UserId))"
}

<Main_Framework ContentClass="mb-0">

    @if (!CObj.BlazorRefresh && !eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj.UserId)))
    {
        <SubscriptionV3 />
    }
    else
    {
        <_UI_Left_Right_Framework LeftContentClass="p-xl-3" ChildContentClass="position-sticky" ChildContentStyle="top:30px" LeftContentStyle="" LeftContentDomId="@LeftDomId" @ref=@left_Right_Framework>
            <LeftContent>
                <_AIModelSelector DateTimeStart="@DateTimeStart" OnSelectedStock="@OnSelectStock" @ref=@_AIModel />
                @if (CObj.UserIsAdministrator)
                {
                    <DataInputSelector SetDate="@DateTimeStart" CallbackDate="GetDateTimeStart" CObj="@CObj" />
                }
            </LeftContent>
            <ChildContent>
                <_CandlestickChart NoDataInfoColumnId="COL5E97C5E301" @ref=@_Candlestick />
            </ChildContent>
        </_UI_Left_Right_Framework>

        <_FooterMenus ActivateIndex="0" />

        <ChatGroupMsgOffcanvas RedirectUrl="/chat" OnSendTickMin="OnSendTickMinEvent" />
    }

</Main_Framework>