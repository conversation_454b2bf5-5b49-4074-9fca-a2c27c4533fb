@using TinyMCE.Blazor
@using <PERSON><PERSON><PERSON>
@using BlazorApp_KzApp.Components.Pages.ZBK.Docs.DoctEditor_Component
@inject NavigationManager NavigationManagerObj
@inject IJSRuntime JS

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }
    [Parameter]
    public eZeroCore.Web.Document DocObj { get; set; } = new();

    [Parameter]
    public EventCallback OnSaveDone { get; set; }

    //public bool IsVideo { get; set; }

    string docId = "";
    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(DocObj.ColumnId) &&
        !DocObj.DocId.Equals(docId, StringComparison.OrdinalIgnoreCase))
        {
            docId = DocObj.DocId;
            //IsVideo = DocObj.ContentTypeEnum == eZeroCore.Web.Column.ContentType.Video;
        }
        await Task.CompletedTask;
    }

    async Task OnSaveDoneCallBack(eZeroCore.Web.Document newDoc)
    {
        DocObj = newDoc;
        await OnSaveDone.InvokeAsync();
        await Task.CompletedTask;
    }

}

@if (!string.IsNullOrEmpty(docId))
{
    <_DocEditorWC_Events DocObj="@DocObj" OnSaveDone=@OnSaveDoneCallBack />
}