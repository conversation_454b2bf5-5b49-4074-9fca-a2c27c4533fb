{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"BlazorApp_KzApp/1.0.0": {"dependencies": {"Microsoft.AspNetCore.Authentication.Google": "9.0.6", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.6", "Microsoft.Azure.SignalR": "1.30.3", "Microsoft.CognitiveServices.Speech": "1.44.0", "Microsoft.CognitiveServices.Speech.Remoteconversation": "1.44.0", "Microsoft.EntityFrameworkCore.SqlServer": "9.0.6", "Microsoft.Extensions.Caching.SqlServer": "9.0.6", "Microsoft.VisualStudio.Web.CodeGeneration.Design": "9.0.0", "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore": "9.0.0", "MimeKit": "4.13.0", "MudBlazor": "8.9.0", "NAudio": "2.2.1", "Radzen.Blazor": "7.1.3", "TinyMCE.Blazor": "2.1.0", "eZeroCore": "1.2.1", "jose-jwt": "5.2.0"}, "runtime": {"BlazorApp_KzApp.dll": {}}}, "AngleSharp/0.17.1": {"dependencies": {"System.Buffers": "4.5.1", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/netstandard2.0/AngleSharp.dll": {"assemblyVersion": "0.17.1.0", "fileVersion": "0.17.1.0"}}}, "AngleSharp.Css/0.17.0": {"dependencies": {"AngleSharp": "0.17.1"}, "runtime": {"lib/net7.0/AngleSharp.Css.dll": {"assemblyVersion": "0.17.0.0", "fileVersion": "0.17.0.0"}}}, "Azure.Core/1.44.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "System.ClientModel": "1.1.0", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "6.0.0", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "6.0.0", "System.Text.Json": "9.0.6", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net6.0/Azure.Core.dll": {"assemblyVersion": "1.44.1.0", "fileVersion": "1.4400.124.50905"}}}, "Azure.Identity/1.11.4": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.Identity.Client": "4.61.3", "Microsoft.Identity.Client.Extensions.Msal": "4.61.3", "System.Memory": "4.5.5", "System.Security.Cryptography.ProtectedData": "9.0.3", "System.Text.Json": "9.0.6", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.11.4.0", "fileVersion": "1.1100.424.31005"}}}, "Azure.Storage.Blobs/12.24.0": {"dependencies": {"Azure.Storage.Common": "12.23.0"}, "runtime": {"lib/net8.0/Azure.Storage.Blobs.dll": {"assemblyVersion": "12.24.0.0", "fileVersion": "12.2400.25.16105"}}}, "Azure.Storage.Common/12.23.0": {"dependencies": {"Azure.Core": "1.44.1", "System.IO.Hashing": "6.0.0"}, "runtime": {"lib/net8.0/Azure.Storage.Common.dll": {"assemblyVersion": "12.23.0.0", "fileVersion": "12.2300.25.16105"}}}, "BouncyCastle.Cryptography/2.5.1": {"runtime": {"lib/net6.0/BouncyCastle.Cryptography.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.5.1.28965"}}}, "CHTCHSConv/1.0.0": {"runtime": {"lib/netstandard2.0/ChineseConverter.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Dapper/2.1.66": {"runtime": {"lib/net8.0/Dapper.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.1.66.48463"}}}, "DeepL.net/1.14.0": {"dependencies": {"Microsoft.Extensions.Http.Polly": "5.0.1", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net5.0/DeepL.net.dll": {"assemblyVersion": "*******", "fileVersion": "1.14.0.0"}}}, "DocumentFormat.OpenXml/3.3.0": {"dependencies": {"DocumentFormat.OpenXml.Framework": "3.3.0"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "DocumentFormat.OpenXml.Framework/3.3.0": {"dependencies": {"System.IO.Packaging": "8.0.1"}, "runtime": {"lib/net8.0/DocumentFormat.OpenXml.Framework.dll": {"assemblyVersion": "3.3.0.0", "fileVersion": "3.3.0.0"}}}, "HtmlAgilityPack/1.12.0": {"runtime": {"lib/net8.0/HtmlAgilityPack.dll": {"assemblyVersion": "1.12.0.0", "fileVersion": "1.12.0.0"}}}, "HtmlSanitizer/9.0.876": {"dependencies": {"AngleSharp": "0.17.1", "AngleSharp.Css": "0.17.0", "System.Collections.Immutable": "9.0.1"}, "runtime": {"lib/netstandard2.0/HtmlSanitizer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.876.0"}}}, "Humanizer/2.14.1": {"dependencies": {"Humanizer.Core.af": "2.14.1", "Humanizer.Core.ar": "2.14.1", "Humanizer.Core.az": "2.14.1", "Humanizer.Core.bg": "2.14.1", "Humanizer.Core.bn-BD": "2.14.1", "Humanizer.Core.cs": "2.14.1", "Humanizer.Core.da": "2.14.1", "Humanizer.Core.de": "2.14.1", "Humanizer.Core.el": "2.14.1", "Humanizer.Core.es": "2.14.1", "Humanizer.Core.fa": "2.14.1", "Humanizer.Core.fi-FI": "2.14.1", "Humanizer.Core.fr": "2.14.1", "Humanizer.Core.fr-BE": "2.14.1", "Humanizer.Core.he": "2.14.1", "Humanizer.Core.hr": "2.14.1", "Humanizer.Core.hu": "2.14.1", "Humanizer.Core.hy": "2.14.1", "Humanizer.Core.id": "2.14.1", "Humanizer.Core.is": "2.14.1", "Humanizer.Core.it": "2.14.1", "Humanizer.Core.ja": "2.14.1", "Humanizer.Core.ko-KR": "2.14.1", "Humanizer.Core.ku": "2.14.1", "Humanizer.Core.lv": "2.14.1", "Humanizer.Core.ms-MY": "2.14.1", "Humanizer.Core.mt": "2.14.1", "Humanizer.Core.nb": "2.14.1", "Humanizer.Core.nb-NO": "2.14.1", "Humanizer.Core.nl": "2.14.1", "Humanizer.Core.pl": "2.14.1", "Humanizer.Core.pt": "2.14.1", "Humanizer.Core.ro": "2.14.1", "Humanizer.Core.ru": "2.14.1", "Humanizer.Core.sk": "2.14.1", "Humanizer.Core.sl": "2.14.1", "Humanizer.Core.sr": "2.14.1", "Humanizer.Core.sr-Latn": "2.14.1", "Humanizer.Core.sv": "2.14.1", "Humanizer.Core.th-TH": "2.14.1", "Humanizer.Core.tr": "2.14.1", "Humanizer.Core.uk": "2.14.1", "Humanizer.Core.uz-Cyrl-UZ": "2.14.1", "Humanizer.Core.uz-Latn-UZ": "2.14.1", "Humanizer.Core.vi": "2.14.1", "Humanizer.Core.zh-CN": "2.14.1", "Humanizer.Core.zh-Hans": "2.14.1", "Humanizer.Core.zh-Hant": "2.14.1"}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "Humanizer.Core.af/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/af/Humanizer.resources.dll": {"locale": "af"}}}, "Humanizer.Core.ar/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ar/Humanizer.resources.dll": {"locale": "ar"}}}, "Humanizer.Core.az/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/az/Humanizer.resources.dll": {"locale": "az"}}}, "Humanizer.Core.bg/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/bg/Humanizer.resources.dll": {"locale": "bg"}}}, "Humanizer.Core.bn-BD/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/bn-BD/Humanizer.resources.dll": {"locale": "bn-BD"}}}, "Humanizer.Core.cs/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/cs/Humanizer.resources.dll": {"locale": "cs"}}}, "Humanizer.Core.da/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/da/Humanizer.resources.dll": {"locale": "da"}}}, "Humanizer.Core.de/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/de/Humanizer.resources.dll": {"locale": "de"}}}, "Humanizer.Core.el/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/el/Humanizer.resources.dll": {"locale": "el"}}}, "Humanizer.Core.es/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/es/Humanizer.resources.dll": {"locale": "es"}}}, "Humanizer.Core.fa/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/fa/Humanizer.resources.dll": {"locale": "fa"}}}, "Humanizer.Core.fi-FI/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/fi-FI/Humanizer.resources.dll": {"locale": "fi-FI"}}}, "Humanizer.Core.fr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/fr/Humanizer.resources.dll": {"locale": "fr"}}}, "Humanizer.Core.fr-BE/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/fr-BE/Humanizer.resources.dll": {"locale": "fr-BE"}}}, "Humanizer.Core.he/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/he/Humanizer.resources.dll": {"locale": "he"}}}, "Humanizer.Core.hr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/hr/Humanizer.resources.dll": {"locale": "hr"}}}, "Humanizer.Core.hu/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/hu/Humanizer.resources.dll": {"locale": "hu"}}}, "Humanizer.Core.hy/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/hy/Humanizer.resources.dll": {"locale": "hy"}}}, "Humanizer.Core.id/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/id/Humanizer.resources.dll": {"locale": "id"}}}, "Humanizer.Core.is/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/is/Humanizer.resources.dll": {"locale": "is"}}}, "Humanizer.Core.it/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/it/Humanizer.resources.dll": {"locale": "it"}}}, "Humanizer.Core.ja/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ja/Humanizer.resources.dll": {"locale": "ja"}}}, "Humanizer.Core.ko-KR/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/netstandard2.0/ko-KR/Humanizer.resources.dll": {"locale": "ko-KR"}}}, "Humanizer.Core.ku/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ku/Humanizer.resources.dll": {"locale": "ku"}}}, "Humanizer.Core.lv/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/lv/Humanizer.resources.dll": {"locale": "lv"}}}, "Humanizer.Core.ms-MY/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/netstandard2.0/ms-MY/Humanizer.resources.dll": {"locale": "ms-MY"}}}, "Humanizer.Core.mt/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/netstandard2.0/mt/Humanizer.resources.dll": {"locale": "mt"}}}, "Humanizer.Core.nb/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/nb/Humanizer.resources.dll": {"locale": "nb"}}}, "Humanizer.Core.nb-NO/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/nb-NO/Humanizer.resources.dll": {"locale": "nb-NO"}}}, "Humanizer.Core.nl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/nl/Humanizer.resources.dll": {"locale": "nl"}}}, "Humanizer.Core.pl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/pl/Humanizer.resources.dll": {"locale": "pl"}}}, "Humanizer.Core.pt/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/pt/Humanizer.resources.dll": {"locale": "pt"}}}, "Humanizer.Core.ro/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ro/Humanizer.resources.dll": {"locale": "ro"}}}, "Humanizer.Core.ru/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/ru/Humanizer.resources.dll": {"locale": "ru"}}}, "Humanizer.Core.sk/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sk/Humanizer.resources.dll": {"locale": "sk"}}}, "Humanizer.Core.sl/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sl/Humanizer.resources.dll": {"locale": "sl"}}}, "Humanizer.Core.sr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sr/Humanizer.resources.dll": {"locale": "sr"}}}, "Humanizer.Core.sr-Latn/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sr-Latn/Humanizer.resources.dll": {"locale": "sr-Latn"}}}, "Humanizer.Core.sv/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/sv/Humanizer.resources.dll": {"locale": "sv"}}}, "Humanizer.Core.th-TH/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/netstandard2.0/th-TH/Humanizer.resources.dll": {"locale": "th-TH"}}}, "Humanizer.Core.tr/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/tr/Humanizer.resources.dll": {"locale": "tr"}}}, "Humanizer.Core.uk/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/uk/Humanizer.resources.dll": {"locale": "uk"}}}, "Humanizer.Core.uz-Cyrl-UZ/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/uz-Cyrl-UZ/Humanizer.resources.dll": {"locale": "uz-Cyrl-UZ"}}}, "Humanizer.Core.uz-Latn-UZ/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/uz-Latn-UZ/Humanizer.resources.dll": {"locale": "uz-Latn-UZ"}}}, "Humanizer.Core.vi/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/vi/Humanizer.resources.dll": {"locale": "vi"}}}, "Humanizer.Core.zh-CN/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/zh-CN/Humanizer.resources.dll": {"locale": "zh-CN"}}}, "Humanizer.Core.zh-Hans/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/zh-Hans/Humanizer.resources.dll": {"locale": "zh-Hans"}}}, "Humanizer.Core.zh-Hant/2.14.1": {"dependencies": {"Humanizer.Core": "2.14.1"}, "resources": {"lib/net6.0/zh-Hant/Humanizer.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "jose-jwt/5.2.0": {"runtime": {"lib/net6.0/jose-jwt.dll": {"assemblyVersion": "5.2.0.0", "fileVersion": "5.2.0.0"}}}, "JWT/11.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/JWT.dll": {"assemblyVersion": "1*******", "fileVersion": "1*******"}}}, "Kvyk.Telegraph/1.1.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net5.0/Telegraph.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "LitJson/0.19.0": {"runtime": {"lib/net8.0/LitJSON.dll": {"assemblyVersion": "0.19.0.0", "fileVersion": "0.19.0.0"}}}, "MailKit/3.2.0": {"dependencies": {"MimeKit": "4.13.0"}, "runtime": {"lib/net6.0/MailKit.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "Microsoft.AspNetCore.Authentication.Google/9.0.6": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.Google.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.6": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Authorization/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Metadata": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authorization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Authorization": "9.0.6", "Microsoft.AspNetCore.Components.Analyzers": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.Analyzers/9.0.6": {}, "Microsoft.AspNetCore.Components.Forms/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Forms.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Components.Web/9.0.6": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.6", "Microsoft.AspNetCore.Components.Forms": "9.0.6", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6", "Microsoft.JSInterop": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Components.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Features": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Connections.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.3": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Cryptography.Internal.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.DataProtection/9.0.3": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "9.0.3", "Microsoft.AspNetCore.DataProtection.Abstractions": "9.0.3", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Hosting.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Security.Cryptography.Xml": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.DataProtection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.DataProtection.Abstractions/9.0.3": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.DataProtection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.DataProtection.EntityFrameworkCore/9.0.3": {"dependencies": {"Microsoft.AspNetCore.DataProtection": "9.0.3", "Microsoft.EntityFrameworkCore": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.DataProtection.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Http.Connections.Client/9.0.3": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Common": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "System.Net.ServerSentEvents": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Http.Connections.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Http.Connections.Common/9.0.3": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Http.Connections.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.Metadata/9.0.6": {"runtime": {"lib/net9.0/Microsoft.AspNetCore.Metadata.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.AspNetCore.Razor.Language/6.0.24": {"runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Razor.Language.dll": {"assemblyVersion": "********", "fileVersion": "6.0.2423.51812"}}}, "Microsoft.AspNetCore.SignalR.Client/9.0.3": {"dependencies": {"Microsoft.AspNetCore.Http.Connections.Client": "9.0.3", "Microsoft.AspNetCore.SignalR.Client.Core": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Client.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.SignalR.Client.Core/9.0.3": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.3", "Microsoft.AspNetCore.SignalR.Protocols.Json": "9.0.3", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "System.Threading.Channels": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Client.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.SignalR.Common/9.0.3": {"dependencies": {"Microsoft.AspNetCore.Connections.Abstractions": "9.0.3", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Common.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.AspNetCore.SignalR.Protocols.Json/9.0.3": {"dependencies": {"Microsoft.AspNetCore.SignalR.Common": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.Azure.SignalR/1.30.3": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Azure.SignalR.Protocols": "1.30.3", "Microsoft.Extensions.Http": "5.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net8.0/Microsoft.Azure.SignalR.Common.dll": {"assemblyVersion": "1.30.3.0", "fileVersion": "1.30.3.0"}, "lib/net8.0/Microsoft.Azure.SignalR.dll": {"assemblyVersion": "1.30.3.0", "fileVersion": "1.30.3.0"}}}, "Microsoft.Azure.SignalR.Protocols/1.30.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6", "System.Buffers": "4.5.1", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Azure.SignalR.Protocols.dll": {"assemblyVersion": "1.30.3.0", "fileVersion": "1.30.3.0"}}}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "Microsoft.Bcl.Cryptography/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Bcl.Cryptography.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Build/17.10.4": {"dependencies": {"Microsoft.Build.Framework": "17.10.4", "Microsoft.NET.StringTools": "17.10.4", "System.Collections.Immutable": "9.0.1", "System.Configuration.ConfigurationManager": "9.0.3", "System.Reflection.Metadata": "8.0.0", "System.Reflection.MetadataLoadContext": "8.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Threading.Tasks.Dataflow": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Build.dll": {"assemblyVersion": "********", "fileVersion": "17.10.4.21802"}}}, "Microsoft.Build.Framework/17.10.4": {"runtime": {"lib/net8.0/Microsoft.Build.Framework.dll": {"assemblyVersion": "********", "fileVersion": "17.10.4.21802"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {}, "Microsoft.CodeAnalysis.AnalyzerUtilities/3.3.0": {"runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll": {"assemblyVersion": "3.3.2.30504", "fileVersion": "3.3.2.30504"}}}, "Microsoft.CodeAnalysis.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.4", "System.Collections.Immutable": "9.0.1", "System.Reflection.Metadata": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Features/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Elfie/1.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.3", "System.Data.DataSetExtensions": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Elfie.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "Microsoft.CodeAnalysis.Features/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.AnalyzerUtilities": "3.3.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Elfie": "1.0.0", "Microsoft.CodeAnalysis.Scripting.Common": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DiaSymReader": "2.0.0", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Features.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor/6.0.24": {"dependencies": {"Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Razor.dll": {"assemblyVersion": "********", "fileVersion": "6.0.2423.51812"}}}, "Microsoft.CodeAnalysis.Scripting.Common/4.8.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.8.0"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Scripting.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "System.Composition": "7.0.0", "System.IO.Pipelines": "7.0.0", "System.Threading.Channels": "9.0.3"}, "runtime": {"lib/net7.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.800.23.55801"}}, "resources": {"lib/net7.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net7.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net7.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net7.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net7.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net7.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net7.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net7.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net7.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net7.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net7.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net7.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net7.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CognitiveServices.Speech/1.44.0": {"dependencies": {"Azure.Core": "1.44.1"}, "runtime": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"assemblyVersion": "1.44.0.28", "fileVersion": "1.44.0.28"}}, "runtimeTargets": {"runtimes/linux-arm/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "linux-arm", "assetType": "runtime", "assemblyVersion": "1.44.0.28", "fileVersion": "1.44.0.28"}, "runtimes/linux-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "linux-arm64", "assetType": "runtime", "assemblyVersion": "1.44.0.28", "fileVersion": "1.44.0.28"}, "runtimes/linux-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "linux-x64", "assetType": "runtime", "assemblyVersion": "1.44.0.28", "fileVersion": "1.44.0.28"}, "runtimes/osx-arm64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "osx-arm64", "assetType": "runtime", "assemblyVersion": "1.44.0.28", "fileVersion": "1.44.0.28"}, "runtimes/osx-x64/lib/netstandard2.0/Microsoft.CognitiveServices.Speech.csharp.dll": {"rid": "osx-x64", "assetType": "runtime", "assemblyVersion": "1.44.0.28", "fileVersion": "1.44.0.28"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/android-x86/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/ios-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "ios-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/iossimulator-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "iossimulator-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/iossimulator-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "iossimulator-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libpal_azure_c_shared.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libpal_azure_c_shared_openssl3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libpal_azure_c_shared.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libpal_azure_c_shared_openssl3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.core.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.audio.sys.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.codec.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libMicrosoft.CognitiveServices.Speech.extension.lu.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpal_azure_c_shared.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libpal_azure_c_shared_openssl3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libMicrosoft.CognitiveServices.Speech.core.a": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.core.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libMicrosoft.CognitiveServices.Speech.extension.kws.ort.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-arm/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.core.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.audio.sys.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.codec.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.44.0.28"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.kws.ort.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/Microsoft.CognitiveServices.Speech.extension.lu.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "1.44.0.28"}}}, "Microsoft.CognitiveServices.Speech.Remoteconversation/1.44.0": {"dependencies": {"Azure.Core": "1.44.1", "Microsoft.CognitiveServices.Speech": "1.44.0", "System.Text.Json": "9.0.6"}, "runtime": {"lib/netstandard2.0/Microsoft.CognitiveServices.Speech.Remoteconversation.dll": {"assemblyVersion": "1.44.0.0", "fileVersion": "1.44.0.0"}}}, "Microsoft.Data.SqlClient/6.0.1": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Bcl.Cryptography": "9.0.0", "Microsoft.Data.SqlClient.SNI.runtime": "6.0.2", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.IdentityModel.JsonWebTokens": "8.6.1", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "9.0.3", "System.Security.Cryptography.Pkcs": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "6.1.25023.1"}}, "resources": {"lib/net9.0/cs/Microsoft.Data.SqlClient.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.Data.SqlClient.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.Data.SqlClient.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.Data.SqlClient.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.Data.SqlClient.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.Data.SqlClient.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.Data.SqlClient.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.Data.SqlClient.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.Data.SqlClient.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.Data.SqlClient.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.Data.SqlClient.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.Data.SqlClient.resources.dll": {"locale": "zh-Han<PERSON>"}}, "runtimeTargets": {"runtimes/unix/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.1.25023.1"}, "runtimes/win/lib/net9.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.1.25023.1"}}}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"runtimeTargets": {"runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "6.2.0.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "6.2.0.0"}}}, "Microsoft.DiaSymReader/2.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.DiaSymReader.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.23.22804"}}}, "Microsoft.DotNet.Scaffolding.Shared/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyModel": "9.0.0-rc.2.24473.5", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.DotNet.Scaffolding.Shared.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.EntityFrameworkCore/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "9.0.6", "Microsoft.EntityFrameworkCore.Analyzers": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"dependencies": {"Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.6": {"dependencies": {"Microsoft.Data.SqlClient": "6.0.1", "Microsoft.EntityFrameworkCore.Relational": "9.0.6", "Microsoft.Extensions.Caching.Memory": "9.0.6", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "System.Formats.Asn1": "9.0.6", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26607"}}}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Caching.SqlServer/9.0.6": {"dependencies": {"Azure.Identity": "1.11.4", "Microsoft.Data.SqlClient": "6.0.1", "Microsoft.Extensions.Caching.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Caching.SqlServer.dll": {"assemblyVersion": "9.0.6.0", "fileVersion": "9.0.625.26701"}}}, "Microsoft.Extensions.Configuration/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6"}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Physical": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Configuration.Json/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.3", "Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.DependencyModel/9.0.0-rc.2.24473.5": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.47305"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Features/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.Features.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11220"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.3": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.FileSystemGlobbing": "9.0.3", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"runtime": {"lib/net9.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.3": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.3", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.3", "Microsoft.Extensions.Logging.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "Microsoft.Extensions.Http/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Logging": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}}, "Microsoft.Extensions.Http.Polly/5.0.1": {"dependencies": {"Microsoft.Extensions.Http": "5.0.0", "Polly": "7.1.0", "Polly.Extensions.Http": "3.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Http.Polly.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.120.58002"}}}, "Microsoft.Extensions.Localization/9.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Localization.Abstractions": "9.0.1", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Localization.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Extensions.Localization.Abstractions/9.0.1": {"runtime": {"lib/net9.0/Microsoft.Extensions.Localization.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.124.61009"}}}, "Microsoft.Extensions.Logging/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options/9.0.6": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}, "runtime": {"lib/net9.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.6", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.6", "Microsoft.Extensions.Options": "9.0.6", "Microsoft.Extensions.Primitives": "9.0.6"}}, "Microsoft.Extensions.Primitives/9.0.6": {"runtime": {"lib/net9.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "Microsoft.Identity.Client/4.61.3": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.6.1", "System.Diagnostics.DiagnosticSource": "6.0.1"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"dependencies": {"Microsoft.Identity.Client": "4.61.3", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net6.0/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "4.61.3.0", "fileVersion": "4.61.3.0"}}}, "Microsoft.IdentityModel.Abstractions/8.6.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.6.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Logging/8.6.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.6.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.6.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.6.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "8.0.1.0", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.6.1": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.6", "Microsoft.IdentityModel.Logging": "8.6.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "Microsoft.JSInterop/9.0.6": {"runtime": {"lib/net9.0/Microsoft.JSInterop.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26701"}}}, "Microsoft.NET.StringTools/17.10.4": {"runtime": {"lib/net8.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "*******", "fileVersion": "17.10.4.21802"}}}, "Microsoft.NETCore.Platforms/3.1.0": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.VisualStudio.Web.CodeGeneration/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.0-rc.2.24473.5", "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.0-rc.2.24473.5", "Microsoft.VisualStudio.Web.CodeGeneration.Templating": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Core.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.0-rc.2.24473.5", "Microsoft.VisualStudio.Web.CodeGenerators.Mvc": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net8.0/dotnet-aspnet-codegenerator-design.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.0-rc.2.24473.5", "Microsoft.VisualStudio.Web.CodeGeneration.Core": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.Extensions.DependencyModel": "9.0.0-rc.2.24473.5", "Microsoft.VisualStudio.Web.CodeGeneration.Utils": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Templating.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyModel": "9.0.0-rc.2.24473.5", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGeneration.Utils.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/9.0.0": {"dependencies": {"Humanizer": "2.14.1", "Microsoft.AspNetCore.Razor.Language": "6.0.24", "Microsoft.Build": "17.10.4", "Microsoft.CodeAnalysis.CSharp": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Features": "4.8.0", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.8.0", "Microsoft.CodeAnalysis.Common": "4.8.0", "Microsoft.CodeAnalysis.Features": "4.8.0", "Microsoft.CodeAnalysis.Razor": "6.0.24", "Microsoft.CodeAnalysis.Workspaces.Common": "4.8.0", "Microsoft.DotNet.Scaffolding.Shared": "9.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.6", "Microsoft.Extensions.DependencyModel": "9.0.0-rc.2.24473.5", "Microsoft.VisualStudio.Web.CodeGeneration": "9.0.0", "Mono.TextTemplating": "3.0.0", "Newtonsoft.Json": "13.0.3", "NuGet.Packaging": "6.11.0", "NuGet.ProjectModel": "6.11.0", "System.Formats.Asn1": "9.0.6", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net8.0/Microsoft.VisualStudio.Web.CodeGenerators.Mvc.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.55703"}}}, "Microsoft.Win32.Registry/4.7.0": {"dependencies": {"System.Security.AccessControl": "4.7.0", "System.Security.Principal.Windows": "5.0.0"}}, "MimeKit/4.13.0": {"dependencies": {"BouncyCastle.Cryptography": "2.5.1", "System.Security.Cryptography.Pkcs": "9.0.3"}, "runtime": {"lib/net8.0/MimeKit.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Mono.TextTemplating/3.0.0": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/Mono.TextTemplating.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "MudBlazor/8.9.0": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.6", "Microsoft.AspNetCore.Components.Web": "9.0.6", "Microsoft.Extensions.Localization": "9.0.1"}, "runtime": {"lib/net9.0/MudBlazor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio/2.2.1": {"dependencies": {"NAudio.Asio": "2.2.1", "NAudio.Core": "2.2.1", "NAudio.Midi": "2.2.1", "NAudio.Wasapi": "2.2.1", "NAudio.WinMM": "2.2.1"}, "runtime": {"lib/net6.0/NAudio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Asio/2.2.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Asio.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Core/2.2.1": {"runtime": {"lib/netstandard2.0/NAudio.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Midi/2.2.1": {"dependencies": {"NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Midi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.Vorbis/1.5.0": {"dependencies": {"NAudio.Core": "2.2.1", "NVorbis": "0.10.5"}, "runtime": {"lib/netstandard2.0/NAudio.Vorbis.dll": {"assemblyVersion": "1.5.0.0", "fileVersion": "1.5.0.0"}}}, "NAudio.Wasapi/2.2.1": {"dependencies": {"NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.Wasapi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NAudio.WinMM/2.2.1": {"dependencies": {"Microsoft.Win32.Registry": "4.7.0", "NAudio.Core": "2.2.1"}, "runtime": {"lib/netstandard2.0/NAudio.WinMM.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "NETCore.MailKit/2.1.0": {"dependencies": {"MailKit": "3.2.0", "Microsoft.Extensions.DependencyInjection": "9.0.6"}, "runtime": {"lib/netstandard2.1/NETCore.MailKit.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "1*******", "fileVersion": "13.0.3.27908"}}}, "NodaTime/3.2.1": {"runtime": {"lib/net8.0/NodaTime.dll": {"assemblyVersion": "3.2.1.0", "fileVersion": "3.2.1.0"}}}, "NuGet.Common/6.11.0": {"dependencies": {"NuGet.Frameworks": "6.11.0"}, "runtime": {"lib/netstandard2.0/NuGet.Common.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Configuration/6.11.0": {"dependencies": {"NuGet.Common": "6.11.0", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/netstandard2.0/NuGet.Configuration.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.DependencyResolver.Core/6.11.0": {"dependencies": {"NuGet.Configuration": "6.11.0", "NuGet.LibraryModel": "6.11.0", "NuGet.Protocol": "6.11.0"}, "runtime": {"lib/net5.0/NuGet.DependencyResolver.Core.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Frameworks/6.11.0": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.LibraryModel/6.11.0": {"dependencies": {"NuGet.Common": "6.11.0", "NuGet.Versioning": "6.11.0"}, "runtime": {"lib/netstandard2.0/NuGet.LibraryModel.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Packaging/6.11.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "NuGet.Configuration": "6.11.0", "NuGet.Versioning": "6.11.0", "System.Security.Cryptography.Pkcs": "9.0.3"}, "runtime": {"lib/net5.0/NuGet.Packaging.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.ProjectModel/6.11.0": {"dependencies": {"NuGet.DependencyResolver.Core": "6.11.0"}, "runtime": {"lib/net5.0/NuGet.ProjectModel.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Protocol/6.11.0": {"dependencies": {"NuGet.Packaging": "6.11.0"}, "runtime": {"lib/net5.0/NuGet.Protocol.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NuGet.Versioning/6.11.0": {"runtime": {"lib/netstandard2.0/NuGet.Versioning.dll": {"assemblyVersion": "6.11.0.119", "fileVersion": "6.11.0.119"}}}, "NVorbis/0.10.5": {"dependencies": {"System.Memory": "4.5.5", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.0/NVorbis.dll": {"assemblyVersion": "0.10.5.0", "fileVersion": "0.10.5.0"}}}, "OpenAI-DotNet/8.6.2": {"runtime": {"lib/net8.0/OpenAI-DotNet.dll": {"assemblyVersion": "8.6.2.0", "fileVersion": "8.6.2.0"}}}, "Polly/7.1.0": {"runtime": {"lib/netstandard2.0/Polly.dll": {"assemblyVersion": "*******", "fileVersion": "7.1.0.0"}}}, "Polly.Extensions.Http/3.0.0": {"dependencies": {"Polly": "7.1.0"}, "runtime": {"lib/netstandard2.0/Polly.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Portable.BouncyCastle/*******": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "1.8.1.0", "fileVersion": "1.8.1.146"}}}, "PublicHoliday/3.2.0": {"runtime": {"lib/net6.0/PublicHoliday.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "QRCoder-ImageSharp/0.10.0": {"dependencies": {"SixLabors.ImageSharp": "3.1.7", "System.Text.Encoding.CodePages": "8.0.0"}, "runtime": {"lib/net8.0/QRCoder.dll": {"assemblyVersion": "0.10.0.0", "fileVersion": "0.10.0.0"}}}, "Radzen.Blazor/7.1.3": {"dependencies": {"Microsoft.AspNetCore.Components": "9.0.6", "Microsoft.AspNetCore.Components.Web": "9.0.6"}, "runtime": {"lib/net9.0/Radzen.Blazor.dll": {"assemblyVersion": "7.1.3.0", "fileVersion": "7.1.3.0"}}}, "RestSharp/112.1.0": {"runtime": {"lib/net8.0/RestSharp.dll": {"assemblyVersion": "11*******", "fileVersion": "11*******"}}}, "SixLabors.ImageSharp/3.1.7": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "3.1.7.0"}}}, "SkiaSharp/3.116.1": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "3.116.1", "SkiaSharp.NativeAssets.macOS": "3.116.1"}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"assemblyVersion": "3.116.0.0", "fileVersion": "3.116.1.0"}}}, "SkiaSharp.NativeAssets.macOS/3.116.1": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/3.116.1": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Stripe.net/47.4.0": {"dependencies": {"Newtonsoft.Json": "13.0.3", "System.Configuration.ConfigurationManager": "9.0.3"}, "runtime": {"lib/net8.0/Stripe.net.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "System.Buffers/4.5.1": {}, "System.ClientModel/1.1.0": {"dependencies": {"System.Memory.Data": "6.0.0", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net6.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.100.24.46703"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Collections.Immutable/9.0.1": {}, "System.Composition/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Convention": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0", "System.Composition.TypedParts": "7.0.0"}}, "System.Composition.AttributedModel/7.0.0": {"runtime": {"lib/net7.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Convention/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Hosting/7.0.0": {"dependencies": {"System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.Runtime/7.0.0": {"runtime": {"lib/net7.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Composition.TypedParts/7.0.0": {"dependencies": {"System.Composition.AttributedModel": "7.0.0", "System.Composition.Hosting": "7.0.0", "System.Composition.Runtime": "7.0.0"}, "runtime": {"lib/net7.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Configuration.ConfigurationManager/9.0.3": {"dependencies": {"System.Diagnostics.EventLog": "9.0.3", "System.Security.Cryptography.ProtectedData": "9.0.3"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Data.DataSetExtensions/4.5.0": {}, "System.Diagnostics.DiagnosticSource/6.0.1": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.EventLog/9.0.3": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Formats.Asn1/9.0.6": {"runtime": {"lib/net9.0/System.Formats.Asn1.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.IdentityModel.Tokens.Jwt/8.6.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.6.1", "Microsoft.IdentityModel.Tokens": "8.6.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.6.1.60307"}}}, "System.IO.Hashing/6.0.0": {"runtime": {"lib/net6.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.IO.Packaging/8.0.1": {"runtime": {"lib/net8.0/System.IO.Packaging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.IO.Pipelines/7.0.0": {}, "System.Memory/4.5.5": {}, "System.Memory.Data/6.0.0": {"dependencies": {"System.Text.Json": "9.0.6"}, "runtime": {"lib/net6.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Net.ServerSentEvents/9.0.3": {"runtime": {"lib/net9.0/System.Net.ServerSentEvents.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection.Metadata/8.0.0": {"dependencies": {"System.Collections.Immutable": "9.0.1"}}, "System.Reflection.MetadataLoadContext/8.0.0": {"dependencies": {"System.Collections.Immutable": "9.0.1", "System.Reflection.Metadata": "8.0.0"}, "runtime": {"lib/net8.0/System.Reflection.MetadataLoadContext.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Runtime.Caching/9.0.3": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.3"}, "runtime": {"lib/net9.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/4.7.0": {"dependencies": {"Microsoft.NETCore.Platforms": "3.1.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.Pkcs/9.0.3": {"runtime": {"lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Security.Cryptography.Pkcs.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Security.Cryptography.ProtectedData/9.0.3": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Security.Cryptography.Xml/9.0.3": {"dependencies": {"System.Security.Cryptography.Pkcs": "9.0.3"}, "runtime": {"lib/net9.0/System.Security.Cryptography.Xml.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.325.11113"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding.CodePages/8.0.0": {}, "System.Text.Encodings.Web/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Json/9.0.6": {"runtime": {"lib/net9.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.625.26613"}}}, "System.Threading.Channels/9.0.3": {}, "System.Threading.Tasks.Dataflow/8.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "System.ValueTuple/4.5.0": {}, "Telegram.Bot/22.4.4": {"dependencies": {"Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "System.Text.Json": "9.0.6"}, "runtime": {"lib/net6.0/Telegram.Bot.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "TinyMCE.Blazor/2.1.0": {"dependencies": {"Microsoft.AspNetCore.Components.Web": "9.0.6"}, "runtime": {"lib/net8.0/TinyMCE.Blazor.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "WebPush/1.0.12": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Portable.BouncyCastle": "*******"}, "runtime": {"lib/net5.0/WebPush.dll": {"assemblyVersion": "1.0.11.0", "fileVersion": "1.0.11.0"}}}, "eZeroCore/1.2.1": {"dependencies": {"Azure.Storage.Blobs": "12.24.0", "CHTCHSConv": "1.0.0", "Dapper": "2.1.66", "DeepL.net": "1.14.0", "DocumentFormat.OpenXml": "3.3.0", "HtmlAgilityPack": "1.12.0", "HtmlSanitizer": "9.0.876", "JWT": "11.0.0", "Kvyk.Telegraph": "1.1.0", "LitJson": "0.19.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.6", "Microsoft.AspNetCore.DataProtection.EntityFrameworkCore": "9.0.3", "Microsoft.AspNetCore.SignalR.Client": "9.0.3", "Microsoft.CognitiveServices.Speech": "1.44.0", "Microsoft.CognitiveServices.Speech.Remoteconversation": "1.44.0", "Microsoft.Data.SqlClient": "6.0.1", "Microsoft.EntityFrameworkCore": "9.0.6", "Microsoft.Extensions.Configuration.Json": "9.0.3", "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore": "9.0.0", "MimeKit": "4.13.0", "NAudio": "2.2.1", "NAudio.Vorbis": "1.5.0", "NETCore.MailKit": "2.1.0", "NVorbis": "0.10.5", "NodaTime": "3.2.1", "OpenAI-DotNet": "8.6.2", "PublicHoliday": "3.2.0", "QRCoder-ImageSharp": "0.10.0", "Radzen.Blazor": "7.1.3", "RestSharp": "112.1.0", "SixLabors.ImageSharp": "3.1.7", "SkiaSharp": "3.116.1", "Stripe.net": "47.4.0", "System.IdentityModel.Tokens.Jwt": "8.6.1", "System.Runtime.Caching": "9.0.3", "System.Text.Json": "9.0.6", "Telegram.Bot": "22.4.4", "WebPush": "1.0.12", "jose-jwt": "5.2.0"}, "runtime": {"eZeroCore.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"BlazorApp_KzApp/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AngleSharp/0.17.1": {"type": "package", "serviceable": true, "sha512": "sha512-5MPI4bbixlwxb0W/smOMeIR+QlxMy5/5jD+WnIAw4pBC+7AhLPe5bS3cLgQMJyvd6q0A48sG+uYOt/ep406GLA==", "path": "anglesharp/0.17.1", "hashPath": "anglesharp.0.17.1.nupkg.sha512"}, "AngleSharp.Css/0.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-bg0AcugmX6BFEi/DHG61QrwRU8iuiX4H8LZehdIzYdqOM/dgb3BsCTzNIcc1XADn4+xfQEdVwJYTSwUxroL4vg==", "path": "anglesharp.css/0.17.0", "hashPath": "anglesharp.css.0.17.0.nupkg.sha512"}, "Azure.Core/1.44.1": {"type": "package", "serviceable": true, "sha512": "sha512-YyznXLQZCregzHvioip07/BkzjuWNXogJEVz9T5W6TwjNr17ax41YGzYMptlo2G10oLCuVPoyva62y0SIRDixg==", "path": "azure.core/1.44.1", "hashPath": "azure.core.1.44.1.nupkg.sha512"}, "Azure.Identity/1.11.4": {"type": "package", "serviceable": true, "sha512": "sha512-Sf4BoE6Q3jTgFkgBkx7qztYOFELBCo+wQgpYDwal/qJ1unBH73ywPztIJKXBXORRzAeNijsuxhk94h0TIMvfYg==", "path": "azure.identity/1.11.4", "hashPath": "azure.identity.1.11.4.nupkg.sha512"}, "Azure.Storage.Blobs/12.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-0SWiMtEYcemn5U69BqVPdqGDwcbl+lsF9L3WFPpqk1Db5g+ytr3L3GmUxMbvvdPNuFwTf03kKtWJpW/qW33T8A==", "path": "azure.storage.blobs/12.24.0", "hashPath": "azure.storage.blobs.12.24.0.nupkg.sha512"}, "Azure.Storage.Common/12.23.0": {"type": "package", "serviceable": true, "sha512": "sha512-X/pe1LS3lC6s6MSL7A6FzRfnB6P72rNBt5oSuyan6Q4Jxr+KiN9Ufwqo32YLHOVfPcB8ESZZ4rBDketn+J37Rw==", "path": "azure.storage.common/12.23.0", "hashPath": "azure.storage.common.12.23.0.nupkg.sha512"}, "BouncyCastle.Cryptography/2.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-zy8TMeTP+1FH2NrLaNZtdRbBdq7u5MI+NFZQOBSM69u5RFkciinwzV2eveY6Kjf5MzgsYvvl6kTStsj3JrXqkg==", "path": "bouncycastle.cryptography/2.5.1", "hashPath": "bouncycastle.cryptography.2.5.1.nupkg.sha512"}, "CHTCHSConv/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-A6G2FO6AZ6MHNY3YbA98YF2Cty1O74+hffIJN6vKONUhyeXtd+kpZumYLFW0Q8tT3SToXNQoLuMFqScQF+FY8g==", "path": "chtchsconv/1.0.0", "hashPath": "chtchsconv.1.0.0.nupkg.sha512"}, "Dapper/2.1.66": {"type": "package", "serviceable": true, "sha512": "sha512-/q77jUgDOS+bzkmk3Vy9SiWMaetTw+NOoPAV0xPBsGVAyljd5S6P+4RUW7R3ZUGGr9lDRyPKgAMj2UAOwvqZYw==", "path": "dapper/2.1.66", "hashPath": "dapper.2.1.66.nupkg.sha512"}, "DeepL.net/1.14.0": {"type": "package", "serviceable": true, "sha512": "sha512-mZX3D0DG+vNDTNMX0lK2dHb4keSunF7mM7r3PazGl7oxOjSb5WdZNWh+X+QHn7rfGNGfKAeu3wBXrGCyMU5X0w==", "path": "deepl.net/1.14.0", "hashPath": "deepl.net.1.14.0.nupkg.sha512"}, "DocumentFormat.OpenXml/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JogRPJNiE6kKvbuCqVRX691pPWeGMqdQgjrUwRYkdpfkMmtElfqAgcRR73geYj7OtBeEpstldZXXzJw27LUI9w==", "path": "documentformat.openxml/3.3.0", "hashPath": "documentformat.openxml.3.3.0.nupkg.sha512"}, "DocumentFormat.OpenXml.Framework/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-R5CLzEoeyr7XDB7g3NTxRobcU19agaxVAhGZm+fZUShJGiU4bw8oUgnA2BNFepigJckfFMayOBMAbV3kDXNInA==", "path": "documentformat.openxml.framework/3.3.0", "hashPath": "documentformat.openxml.framework.3.3.0.nupkg.sha512"}, "HtmlAgilityPack/1.12.0": {"type": "package", "serviceable": true, "sha512": "sha512-VHtVZmfoYhQyA/POvZRLuTpCz1zhzIDrdYRJIRV73e9wKAzjW71biYNOHOWx8MxEX3TE4TWVfx1QDRoZcj2AWw==", "path": "htmlagilitypack/1.12.0", "hashPath": "htmlagilitypack.1.12.0.nupkg.sha512"}, "HtmlSanitizer/9.0.876": {"type": "package", "serviceable": true, "sha512": "sha512-aJWuzb561EaDYliXlp2dr3Xd9qOCsj9mOdic488Un6iTX0NVlKmarpIkdrfgigYJT9Gzjm5kjP7WMZHeASvKeg==", "path": "htmlsanitizer/9.0.876", "hashPath": "htmlsanitizer.9.0.876.nupkg.sha512"}, "Humanizer/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-/FUTD3cEceAAmJSCPN9+J+VhGwmL/C12jvwlyM1DFXShEMsBzvLzLqSrJ2rb+k/W2znKw7JyflZgZpyE+tI7lA==", "path": "humanizer/2.14.1", "hashPath": "humanizer.2.14.1.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "Humanizer.Core.af/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-BoQHyu5le+xxKOw+/AUM7CLXneM/Bh3++0qh1u0+D95n6f9eGt9kNc8LcAHLIOwId7Sd5hiAaaav0Nimj3peNw==", "path": "humanizer.core.af/2.14.1", "hashPath": "humanizer.core.af.2.14.1.nupkg.sha512"}, "Humanizer.Core.ar/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-3d1V10LDtmqg5bZjWkA/EkmGFeSfNBcyCH+TiHcHP+HGQQmRq3eBaLcLnOJbVQVn3Z6Ak8GOte4RX4kVCxQlFA==", "path": "humanizer.core.ar/2.14.1", "hashPath": "humanizer.core.ar.2.14.1.nupkg.sha512"}, "Humanizer.Core.az/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-8Z/tp9PdHr/K2Stve2Qs/7uqWPWLUK9D8sOZDNzyv42e20bSoJkHFn7SFoxhmaoVLJwku2jp6P7HuwrfkrP18Q==", "path": "humanizer.core.az/2.14.1", "hashPath": "humanizer.core.az.2.14.1.nupkg.sha512"}, "Humanizer.Core.bg/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-S+hIEHicrOcbV2TBtyoPp1AVIGsBzlarOGThhQYCnP6QzEYo/5imtok6LMmhZeTnBFoKhM8yJqRfvJ5yqVQKSQ==", "path": "humanizer.core.bg/2.14.1", "hashPath": "humanizer.core.bg.2.14.1.nupkg.sha512"}, "Humanizer.Core.bn-BD/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-U3bfj90tnUDRKlL1ZFlzhCHoVgpTcqUlTQxjvGCaFKb+734TTu3nkHUWVZltA1E/swTvimo/aXLtkxnLFrc0EQ==", "path": "humanizer.core.bn-bd/2.14.1", "hashPath": "humanizer.core.bn-bd.2.14.1.nupkg.sha512"}, "Humanizer.Core.cs/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-jWrQkiCTy3L2u1T86cFkgijX6k7hoB0pdcFMWYaSZnm6rvG/XJE40tfhYyKhYYgIc1x9P2GO5AC7xXvFnFdqMQ==", "path": "humanizer.core.cs/2.14.1", "hashPath": "humanizer.core.cs.2.14.1.nupkg.sha512"}, "Humanizer.Core.da/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-5o0rJyE/2wWUUphC79rgYDnif/21MKTTx9LIzRVz9cjCIVFrJ2bDyR2gapvI9D6fjoyvD1NAfkN18SHBsO8S9g==", "path": "humanizer.core.da/2.14.1", "hashPath": "humanizer.core.da.2.14.1.nupkg.sha512"}, "Humanizer.Core.de/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-9JD/p+rqjb8f5RdZ3aEJqbjMYkbk4VFii2QDnnOdNo6ywEfg/A5YeOQ55CaBJmy7KvV4tOK4+qHJnX/tg3Z54A==", "path": "humanizer.core.de/2.14.1", "hashPath": "humanizer.core.de.2.14.1.nupkg.sha512"}, "Humanizer.Core.el/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-Xmv6sTL5mqjOWGGpqY7bvbfK5RngaUHSa8fYDGSLyxY9mGdNbDcasnRnMOvi0SxJS9gAqBCn21Xi90n2SHZbFA==", "path": "humanizer.core.el/2.14.1", "hashPath": "humanizer.core.el.2.14.1.nupkg.sha512"}, "Humanizer.Core.es/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-e//OIAeMB7pjBV1HqqI4pM2Bcw3Jwgpyz9G5Fi4c+RJvhqFwztoWxW57PzTnNJE2lbhGGLQZihFZjsbTUsbczA==", "path": "humanizer.core.es/2.14.1", "hashPath": "humanizer.core.es.2.14.1.nupkg.sha512"}, "Humanizer.Core.fa/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-nzDOj1x0NgjXMjsQxrET21t1FbdoRYujzbmZoR8u8ou5CBWY1UNca0j6n/PEJR/iUbt4IxstpszRy41wL/BrpA==", "path": "humanizer.core.fa/2.14.1", "hashPath": "humanizer.core.fa.2.14.1.nupkg.sha512"}, "Humanizer.Core.fi-FI/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vnxxx4LUhp3AzowYi6lZLAA9Lh8UqkdwRh4IE2qDXiVpbo08rSbokATaEzFS+o+/jCNZBmoyyyph3vgmcSzhhQ==", "path": "humanizer.core.fi-fi/2.14.1", "hashPath": "humanizer.core.fi-fi.2.14.1.nupkg.sha512"}, "Humanizer.Core.fr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-2p4g0BYNzFS3u9SOIDByp2VClYKO0K1ecDV4BkB9EYdEPWfFODYnF+8CH8LpUrpxL2TuWo2fiFx/4Jcmrnkbpg==", "path": "humanizer.core.fr/2.14.1", "hashPath": "humanizer.core.fr.2.14.1.nupkg.sha512"}, "Humanizer.Core.fr-BE/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-o6R3SerxCRn5Ij8nCihDNMGXlaJ/1AqefteAssgmU2qXYlSAGdhxmnrQAXZUDlE4YWt/XQ6VkNLtH7oMqsSPFQ==", "path": "humanizer.core.fr-be/2.14.1", "hashPath": "humanizer.core.fr-be.2.14.1.nupkg.sha512"}, "Humanizer.Core.he/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-FPsAhy7Iw6hb+ZitLgYC26xNcgGAHXb0V823yFAzcyoL5ozM+DCJtYfDPYiOpsJhEZmKFTM9No0jUn1M89WGvg==", "path": "humanizer.core.he/2.14.1", "hashPath": "humanizer.core.he.2.14.1.nupkg.sha512"}, "Humanizer.Core.hr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-chnaD89yOlST142AMkAKLuzRcV5df3yyhDyRU5rypDiqrq2HN8y1UR3h1IicEAEtXLoOEQyjSAkAQ6QuXkn7aw==", "path": "humanizer.core.hr/2.14.1", "hashPath": "humanizer.core.hr.2.14.1.nupkg.sha512"}, "Humanizer.Core.hu/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-hAfnaoF9LTGU/CmFdbnvugN4tIs8ppevVMe3e5bD24+tuKsggMc5hYta9aiydI8JH9JnuVmxvNI4DJee1tK05A==", "path": "humanizer.core.hu/2.14.1", "hashPath": "humanizer.core.hu.2.14.1.nupkg.sha512"}, "Humanizer.Core.hy/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-sVIKxOiSBUb4gStRHo9XwwAg9w7TNvAXbjy176gyTtaTiZkcjr9aCPziUlYAF07oNz6SdwdC2mwJBGgvZ0Sl2g==", "path": "humanizer.core.hy/2.14.1", "hashPath": "humanizer.core.hy.2.14.1.nupkg.sha512"}, "Humanizer.Core.id/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-4Zl3GTvk3a49Ia/WDNQ97eCupjjQRs2iCIZEQdmkiqyaLWttfb+cYXDMGthP42nufUL0SRsvBctN67oSpnXtsg==", "path": "humanizer.core.id/2.14.1", "hashPath": "humanizer.core.id.2.14.1.nupkg.sha512"}, "Humanizer.Core.is/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-R67A9j/nNgcWzU7gZy1AJ07ABSLvogRbqOWvfRDn4q6hNdbg/mjGjZBp4qCTPnB2mHQQTCKo3oeCUayBCNIBCw==", "path": "humanizer.core.is/2.14.1", "hashPath": "humanizer.core.is.2.14.1.nupkg.sha512"}, "Humanizer.Core.it/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-jYxGeN4XIKHVND02FZ+Woir3CUTyBhLsqxu9iqR/9BISArkMf1Px6i5pRZnvq4fc5Zn1qw71GKKoCaHDJBsLFw==", "path": "humanizer.core.it/2.14.1", "hashPath": "humanizer.core.it.2.14.1.nupkg.sha512"}, "Humanizer.Core.ja/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-TM3ablFNoYx4cYJybmRgpDioHpiKSD7q0QtMrmpsqwtiiEsdW5zz/q4PolwAczFnvrKpN6nBXdjnPPKVet93ng==", "path": "humanizer.core.ja/2.14.1", "hashPath": "humanizer.core.ja.2.14.1.nupkg.sha512"}, "Humanizer.Core.ko-KR/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-CtvwvK941k/U0r8PGdEuBEMdW6jv/rBiA9tUhakC7Zd2rA/HCnDcbr1DiNZ+/tRshnhzxy/qwmpY8h4qcAYCtQ==", "path": "humanizer.core.ko-kr/2.14.1", "hashPath": "humanizer.core.ko-kr.2.14.1.nupkg.sha512"}, "Humanizer.Core.ku/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-vHmzXcVMe+LNrF9txpdHzpG7XJX65SiN9GQd/Zkt6gsGIIEeECHrkwCN5Jnlkddw2M/b0HS4SNxdR1GrSn7uCA==", "path": "humanizer.core.ku/2.14.1", "hashPath": "humanizer.core.ku.2.14.1.nupkg.sha512"}, "Humanizer.Core.lv/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-E1/KUVnYBS1bdOTMNDD7LV/jdoZv/fbWTLPtvwdMtSdqLyRTllv6PGM9xVQoFDYlpvVGtEl/09glCojPHw8ffA==", "path": "humanizer.core.lv/2.14.1", "hashPath": "humanizer.core.lv.2.14.1.nupkg.sha512"}, "Humanizer.Core.ms-MY/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-vX8oq9HnYmAF7bek4aGgGFJficHDRTLgp/EOiPv9mBZq0i4SA96qVMYSjJ2YTaxs7Eljqit7pfpE2nmBhY5Fnw==", "path": "humanizer.core.ms-my/2.14.1", "hashPath": "humanizer.core.ms-my.2.14.1.nupkg.sha512"}, "Humanizer.Core.mt/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-pEgTBzUI9hzemF7xrIZigl44LidTUhNu4x/P6M9sAwZjkUF0mMkbpxKkaasOql7lLafKrnszs0xFfaxQyzeuZQ==", "path": "humanizer.core.mt/2.14.1", "hashPath": "humanizer.core.mt.2.14.1.nupkg.sha512"}, "Humanizer.Core.nb/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-mbs3m6JJq53ssLqVPxNfqSdTxAcZN3njlG8yhJVx83XVedpTe1ECK9aCa8FKVOXv93Gl+yRHF82Hw9T9LWv2hw==", "path": "humanizer.core.nb/2.14.1", "hashPath": "humanizer.core.nb.2.14.1.nupkg.sha512"}, "Humanizer.Core.nb-NO/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-AsJxrrVYmIMbKDGe8W6Z6//wKv9dhWH7RsTcEHSr4tQt/80pcNvLi0hgD3fqfTtg0tWKtgch2cLf4prorEV+5A==", "path": "humanizer.core.nb-no/2.14.1", "hashPath": "humanizer.core.nb-no.2.14.1.nupkg.sha512"}, "Humanizer.Core.nl/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-24b0OUdzJxfoqiHPCtYnR5Y4l/s4Oh7KW7uDp+qX25NMAHLCGog2eRfA7p2kRJp8LvnynwwQxm2p534V9m55wQ==", "path": "humanizer.core.nl/2.14.1", "hashPath": "humanizer.core.nl.2.14.1.nupkg.sha512"}, "Humanizer.Core.pl/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-17mJNYaBssENVZyQHduiq+bvdXS0nhZJGEXtPKoMhKv3GD//WO0mEfd9wjEBsWCSmWI7bjRqhCidxzN+YtJmsg==", "path": "humanizer.core.pl/2.14.1", "hashPath": "humanizer.core.pl.2.14.1.nupkg.sha512"}, "Humanizer.Core.pt/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-8HB8qavcVp2la1GJX6t+G9nDYtylPKzyhxr9LAooIei9MnQvNsjEiIE4QvHoeDZ4weuQ9CsPg1c211XUMVEZ4A==", "path": "humanizer.core.pt/2.14.1", "hashPath": "humanizer.core.pt.2.14.1.nupkg.sha512"}, "Humanizer.Core.ro/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-psXNOcA6R8fSHoQYhpBTtTTYiOk8OBoN3PKCEDgsJKIyeY5xuK81IBdGi77qGZMu/OwBRQjQCBMtPJb0f4O1+A==", "path": "humanizer.core.ro/2.14.1", "hashPath": "humanizer.core.ro.2.14.1.nupkg.sha512"}, "Humanizer.Core.ru/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-zm245xUWrajSN2t9H7BTf84/2APbUkKlUJpcdgsvTdAysr1ag9fi1APu6JEok39RRBXDfNRVZHawQ/U8X0pSvQ==", "path": "humanizer.core.ru/2.14.1", "hashPath": "humanizer.core.ru.2.14.1.nupkg.sha512"}, "Humanizer.Core.sk/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ncw24Vf3ioRnbU4MsMFHafkyYi8JOnTqvK741GftlQvAbULBoTz2+e7JByOaasqeSi0KfTXeegJO+5Wk1c0Mbw==", "path": "humanizer.core.sk/2.14.1", "hashPath": "humanizer.core.sk.2.14.1.nupkg.sha512"}, "Humanizer.Core.sl/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-l8sUy4ciAIbVThWNL0atzTS2HWtv8qJrsGWNlqrEKmPwA4SdKolSqnTes9V89fyZTc2Q43jK8fgzVE2C7t009A==", "path": "humanizer.core.sl/2.14.1", "hashPath": "humanizer.core.sl.2.14.1.nupkg.sha512"}, "Humanizer.Core.sr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-rnNvhpkOrWEymy7R/MiFv7uef8YO5HuXDyvojZ7JpijHWA5dXuVXooCOiA/3E93fYa3pxDuG2OQe4M/olXbQ7w==", "path": "humanizer.core.sr/2.14.1", "hashPath": "humanizer.core.sr.2.14.1.nupkg.sha512"}, "Humanizer.Core.sr-Latn/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-nuy/ykpk974F8ItoQMS00kJPr2dFNjOSjgzCwfysbu7+gjqHmbLcYs7G4kshLwdA4AsVncxp99LYeJgoh1JF5g==", "path": "humanizer.core.sr-latn/2.14.1", "hashPath": "humanizer.core.sr-latn.2.14.1.nupkg.sha512"}, "Humanizer.Core.sv/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-E53+tpAG0RCp+cSSI7TfBPC+NnsEqUuoSV0sU+rWRXWr9MbRWx1+Zj02XMojqjGzHjjOrBFBBio6m74seFl0AA==", "path": "humanizer.core.sv/2.14.1", "hashPath": "humanizer.core.sv.2.14.1.nupkg.sha512"}, "Humanizer.Core.th-TH/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-eSevlJtvs1r4vQarNPfZ2kKDp/xMhuD00tVVzRXkSh1IAZbBJI/x2ydxUOwfK9bEwEp+YjvL1Djx2+kw7ziu7g==", "path": "humanizer.core.th-th/2.14.1", "hashPath": "humanizer.core.th-th.2.14.1.nupkg.sha512"}, "Humanizer.Core.tr/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-rQ8N+o7yFcFqdbtu1mmbrXFi8TQ+uy+fVH9OPI0CI3Cu1om5hUU/GOMC3hXsTCI6d79y4XX+0HbnD7FT5khegA==", "path": "humanizer.core.tr/2.14.1", "hashPath": "humanizer.core.tr.2.14.1.nupkg.sha512"}, "Humanizer.Core.uk/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-2uEfujwXKNm6bdpukaLtEJD+04uUtQD65nSGCetA1fYNizItEaIBUboNfr3GzJxSMQotNwGVM3+nSn8jTd0VSg==", "path": "humanizer.core.uk/2.14.1", "hashPath": "humanizer.core.uk.2.14.1.nupkg.sha512"}, "Humanizer.Core.uz-Cyrl-UZ/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-TD3ME2sprAvFqk9tkWrvSKx5XxEMlAn1sjk+cYClSWZlIMhQQ2Bp/w0VjX1Kc5oeKjxRAnR7vFcLUFLiZIDk9Q==", "path": "humanizer.core.uz-cyrl-uz/2.14.1", "hashPath": "humanizer.core.uz-cyrl-uz.2.14.1.nupkg.sha512"}, "Humanizer.Core.uz-Latn-UZ/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-/kHAoF4g0GahnugZiEMpaHlxb+W6jCEbWIdsq9/I1k48ULOsl/J0pxZj93lXC3omGzVF1BTVIeAtv5fW06Phsg==", "path": "humanizer.core.uz-latn-uz/2.14.1", "hashPath": "humanizer.core.uz-latn-uz.2.14.1.nupkg.sha512"}, "Humanizer.Core.vi/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-rsQNh9rmHMBtnsUUlJbShMsIMGflZtPmrMM6JNDw20nhsvqfrdcoDD8cMnLAbuSovtc3dP+swRmLQzKmXDTVPA==", "path": "humanizer.core.vi/2.14.1", "hashPath": "humanizer.core.vi.2.14.1.nupkg.sha512"}, "Humanizer.Core.zh-CN/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-uH2dWhrgugkCjDmduLdAFO9w1Mo0q07EuvM0QiIZCVm6FMCu/lGv2fpMu4GX+4HLZ6h5T2Pg9FIdDLCPN2a67w==", "path": "humanizer.core.zh-cn/2.14.1", "hashPath": "humanizer.core.zh-cn.2.14.1.nupkg.sha512"}, "Humanizer.Core.zh-Hans/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-WH6IhJ8V1UBG7rZXQk3dZUoP2gsi8a0WkL8xL0sN6WGiv695s8nVcmab9tWz20ySQbuzp0UkSxUQFi5jJHIpOQ==", "path": "humanizer.core.zh-hans/2.14.1", "hashPath": "humanizer.core.zh-hans.2.14.1.nupkg.sha512"}, "Humanizer.Core.zh-Hant/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-VIXB7HCUC34OoaGnO3HJVtSv2/wljPhjV7eKH4+TFPgQdJj2lvHNKY41Dtg0Bphu7X5UaXFR4zrYYyo+GNOjbA==", "path": "humanizer.core.zh-hant/2.14.1", "hashPath": "humanizer.core.zh-hant.2.14.1.nupkg.sha512"}, "jose-jwt/5.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-wCpQXj+i7qJpS6QC/dlNJM2AtJ0OGL1hbml/Lo0K5JzN1+HtvUVKCP4f9KvcwE9vLLQdaeSPjuRe+LPDja6xsA==", "path": "jose-jwt/5.2.0", "hashPath": "jose-jwt.5.2.0.nupkg.sha512"}, "JWT/11.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jOT1PA95jgf78CqWsKt8hz9Uzv4J39vukzf5nYtfky9N6wl1W4vIXT1CdkQI7zG20HnApEMtmpC/pMtCKr4rvw==", "path": "jwt/11.0.0", "hashPath": "jwt.11.0.0.nupkg.sha512"}, "Kvyk.Telegraph/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-pn6s/rouVGbEEMMjSDv4idaq3OFU6SpGArc7k+cxCRcroICiNfyvH29FbgxcutIBBSy31kLOvs3Rn5taCejdZA==", "path": "kvyk.telegraph/1.1.0", "hashPath": "kvyk.telegraph.1.1.0.nupkg.sha512"}, "LitJson/0.19.0": {"type": "package", "serviceable": true, "sha512": "sha512-7hUCUSkNJBwr+K6uqTOsQSbd5AIDrCMg/ya1BPxrSOlPNoPCyDx+0PyXU61FjbRPl7WU2HavV2r89djdJnlV/g==", "path": "litjson/0.19.0", "hashPath": "litjson.0.19.0.nupkg.sha512"}, "MailKit/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-5MTpTqmjqT7HPvYbP3HozRZMth5vSaT0ReN0iM3rAM4CgLI/R1qqtLDDNWGnFFIlcNzeJkZQRJJMkv8cgzWBbA==", "path": "mailkit/3.2.0", "hashPath": "mailkit.3.2.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Google/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0QP8shaYg/wPDmw7ogQdfJhAKbYKoZGuIBQoeOqVBAuBrW2iRyAzIP6fXWfwJMFUzUG25VxkARYteB1tP1ouGA==", "path": "microsoft.aspnetcore.authentication.google/9.0.6", "hashPath": "microsoft.aspnetcore.authentication.google.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-nH1mrzr77pk+n1E5+A/0KlzkNhqy3LS3gUGEjJf0PQE6PZAc3pr8rLwUATcaJMr/12qsxHT+kcvRZMxc4bxFpA==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.6", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Authorization/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-FG/fHZAg2J9NKt+y2BH+fMjc+TqA7wlri/3PNtoSgvclMlr5iPshDLXHQgvS9l7IUzzfkqQ3/DWPYuA4OJu75A==", "path": "microsoft.aspnetcore.authorization/9.0.6", "hashPath": "microsoft.aspnetcore.authorization.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-4WzUBnpxqm0Gvv3XX47h5g09LN4dtQpJjYW2LAfoaVeho5TLM00uDj2YHzjxQwcRTdrG7tZDCyf4V4wwRpjGcA==", "path": "microsoft.aspnetcore.components/9.0.6", "hashPath": "microsoft.aspnetcore.components.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-UXMjbezwMMoL21L20OB1BLGF9M1BFPzdWiezaiZSBG55z36BEXvs8hetKw7PiuytOdUqWFI1wkPd3MD9jpeKAw==", "path": "microsoft.aspnetcore.components.analyzers/9.0.6", "hashPath": "microsoft.aspnetcore.components.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Forms/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-GHdFdrU+9ePkuE7S2g7iu+Lg8FPBY9qGaK+FPSBHNx8ulEdrOHJhufb1FZH/m++NoJgcikOzJ14+eL96HfBr8g==", "path": "microsoft.aspnetcore.components.forms/9.0.6", "hashPath": "microsoft.aspnetcore.components.forms.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Components.Web/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-c+2B1DdgAOKXt+mMcZgeQPbdoW0Ro+783m+yPK/QQHhjV8cbkPBWXg6OMJC2Vzum5tfAC3coNhlmcLUUBwc3ZQ==", "path": "microsoft.aspnetcore.components.web/9.0.6", "hashPath": "microsoft.aspnetcore.components.web.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Connections.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-MWkNy/Yhv2q5ZVYPHjHN6pEE5Ya1r4opqSpnsW60bgpDOT54zZ6Kpqub4Tcat8ENsR5PZcTZ3eeSAthweUb/KA==", "path": "microsoft.aspnetcore.connections.abstractions/9.0.3", "hashPath": "microsoft.aspnetcore.connections.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tKXLFMMefKRyDbbVJM+5Gtd1CgPS6vwc9yTLnaStT9+DmkI6iFrEOdKMjZt5q3ijqQVjWew0/EcB9EbEKeP0DA==", "path": "microsoft.aspnetcore.cryptography.internal/9.0.3", "hashPath": "microsoft.aspnetcore.cryptography.internal.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-iOS5p2MQQ0B1JLD2k+nmpwk6gmZY4w4KnKW/J+dpfqxc7N7iccYocee5BH5Y2KlPsFSlgNP1zhtdR/l/od1n8w==", "path": "microsoft.aspnetcore.dataprotection/9.0.3", "hashPath": "microsoft.aspnetcore.dataprotection.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cz8/avBwCEd92X7xkK7gGJkOJJfTTb6uJzZltyCr329GLekD4aiLLwRGYhh/3Z0KNor9IujBlFJOW3e6cUMeHg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/9.0.3", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.EntityFrameworkCore/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-yD0BT2eP7zdZyQxSEzoh9/UwZfQ7e8jynN/87wIdgQqHRTwc6uhganmg/nDVSdmrfMfxVPAV0qnJ6/h+U23XZg==", "path": "microsoft.aspnetcore.dataprotection.entityframeworkcore/9.0.3", "hashPath": "microsoft.aspnetcore.dataprotection.entityframeworkcore.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Client/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-bLoLX67FBeYK1KKGfXrmBki/F9EAK8EKCNkyADtfFjQkJ1Qhhw1sjBlcL8TbVnZxk+FaFsyCeBPmSHgOwNIJ/A==", "path": "microsoft.aspnetcore.http.connections.client/9.0.3", "hashPath": "microsoft.aspnetcore.http.connections.client.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Connections.Common/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-GYDAXEmaG/q9UgixPchsLAVbBUbdgG3hd8J7Af4k4GIKLsibAhos7QY7hHicyULJvRtl03totiRi5Z+JIKEnUA==", "path": "microsoft.aspnetcore.http.connections.common/9.0.3", "hashPath": "microsoft.aspnetcore.http.connections.common.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Metadata/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-SZAmFKGsQPP/xyDir96J5UNTAdCsfeCpPwFO9EewIcgQ8gUr3KHmn4CTC2HmHIDdiTujGC2T3VBcSxmrY+3n/g==", "path": "microsoft.aspnetcore.metadata/9.0.6", "hashPath": "microsoft.aspnetcore.metadata.9.0.6.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.Language/6.0.24": {"type": "package", "serviceable": true, "sha512": "sha512-kBL6ljTREp/3fk8EKN27mrPy3WTqWUjiqCkKFlCKHUKRO3/9rAasKizX3vPWy4ZTcNsIPmVWUHwjDFmiW4MyNA==", "path": "microsoft.aspnetcore.razor.language/6.0.24", "hashPath": "microsoft.aspnetcore.razor.language.6.0.24.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-V8K94AN9ADbpP2jxwt8Y++g7t/XZ7oEV+GZizNvLnR8dpCYWeveIZ/tItO54jfZJ5jmt5YyideOc+ErZbr1IZg==", "path": "microsoft.aspnetcore.signalr.client/9.0.3", "hashPath": "microsoft.aspnetcore.signalr.client.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Client.Core/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-R2N03AK5FH8KIENfJGER4SgjJFMJTBiYuLbovbRunp5R4knO+iysfbYMfEFO3kn98ElWr/747dS4AeWQOEEQsg==", "path": "microsoft.aspnetcore.signalr.client.core/9.0.3", "hashPath": "microsoft.aspnetcore.signalr.client.core.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Common/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-/568tq8YVas1mDgeScmQdQV4ZDRjdyqDS3rAo17R5Bs4puMaNM80wQSwcvsmN5gSwH6L/XRTmD1J1uRIyKXrCg==", "path": "microsoft.aspnetcore.signalr.common/9.0.3", "hashPath": "microsoft.aspnetcore.signalr.common.9.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.SignalR.Protocols.Json/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-jvOdsquqrbWMP3/Aq4s8/yVeCxBkjvxarv/2WgubKkQT8nZ46aKY3Rvj1uolp4N3TuaMGlnd6mhK/tF7jCat2Q==", "path": "microsoft.aspnetcore.signalr.protocols.json/9.0.3", "hashPath": "microsoft.aspnetcore.signalr.protocols.json.9.0.3.nupkg.sha512"}, "Microsoft.Azure.SignalR/1.30.3": {"type": "package", "serviceable": true, "sha512": "sha512-p2dWnrpVI7uSq9xo1YBi7F1Dtq8TBvi/XA5dFD3MPPQaUtoNTb9G0uprt5gPCW9Cuipid4jb21GqJ0Eu9+6GpQ==", "path": "microsoft.azure.signalr/1.30.3", "hashPath": "microsoft.azure.signalr.1.30.3.nupkg.sha512"}, "Microsoft.Azure.SignalR.Protocols/1.30.3": {"type": "package", "serviceable": true, "sha512": "sha512-OPkIylG1MF4/uA05IADl8HZR6dLzt9uEGY19ACT0u6ARyvesJMNZzRI/mhAiXHUyxfvlIXewAeyIFMIu5wLa4A==", "path": "microsoft.azure.signalr.protocols/1.30.3", "hashPath": "microsoft.azure.signalr.protocols.1.30.3.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg==", "path": "microsoft.bcl.asyncinterfaces/7.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.7.0.0.nupkg.sha512"}, "Microsoft.Bcl.Cryptography/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tjfuEv+QOznFL1bEPa7svmjpbNvDIrwdinMNy/HhrToQQpONW4hdp0Sans55Rcy9KB3z60duBeey89JY1VQOvg==", "path": "microsoft.bcl.cryptography/9.0.0", "hashPath": "microsoft.bcl.cryptography.9.0.0.nupkg.sha512"}, "Microsoft.Build/17.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-ZmGA8vhVXFzC4oo48ybQKlEybVKd0Ntfdr+Enqrn5ES1R6e/krIK9hLk0W33xuT0/G6QYd3YdhJZh+Xle717Ag==", "path": "microsoft.build/17.10.4", "hashPath": "microsoft.build.17.10.4.nupkg.sha512"}, "Microsoft.Build.Framework/17.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-4qXCwNOXBR1dyCzuks9SwTwFJQO/xmf2wcMislotDWJu7MN/r3xDNoU8Ae5QmKIHPaLG1xmfDkYS7qBVzxmeKw==", "path": "microsoft.build.framework/17.10.4", "hashPath": "microsoft.build.framework.17.10.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.4": {"type": "package", "serviceable": true, "sha512": "sha512-AxkxcPR+rheX0SmvpLVIGLhOUXAKG56a64kV9VQZ4y9gR9ZmPXnqZvHJnmwLSwzrEP6junUF11vuc+aqo5r68g==", "path": "microsoft.codeanalysis.analyzers/3.3.4", "hashPath": "microsoft.codeanalysis.analyzers.3.3.4.nupkg.sha512"}, "Microsoft.CodeAnalysis.AnalyzerUtilities/3.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-gyQ70pJ4T7hu/s0+QnEaXtYfeG/JrttGnxHJlrhpxsQjRIUGuRhVwNBtkHHYOrUAZ/l47L98/NiJX6QmTwAyrg==", "path": "microsoft.codeanalysis.analyzerutilities/3.3.0", "hashPath": "microsoft.codeanalysis.analyzerutilities.3.3.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-/jR+e/9aT+BApoQJABlVCKnnggGQbvGh7BKq2/wI1LamxC+LbzhcLj4Vj7gXCofl1n4E521YfF9w0WcASGg/KA==", "path": "microsoft.codeanalysis.common/4.8.0", "hashPath": "microsoft.codeanalysis.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-+3+qfdb/aaGD8PZRCrsdobbzGs1m9u119SkkJt8e/mk3xLJz/udLtS2T6nY27OTXxBBw10HzAbC8Z9w08VyP/g==", "path": "microsoft.codeanalysis.csharp/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Features/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-Gpas3l8PE1xz1VDIJNMkYuoFPXtuALxybP04caXh9avC2a0elsoBdukndkJXVZgdKPwraf0a98s7tjqnEk5QIQ==", "path": "microsoft.codeanalysis.csharp.features/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.features.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-3amm4tq4Lo8/BGvg9p3BJh3S9nKq2wqCXfS7138i69TUpo/bD+XvD0hNurpEBtcNZhi1FyutiomKJqVF39ugYA==", "path": "microsoft.codeanalysis.csharp.workspaces/4.8.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Elfie/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-r12elUp4MRjdnRfxEP+xqVSUUfG3yIJTBEJGwbfvF5oU4m0jb9HC0gFG28V/dAkYGMkRmHVi3qvrnBLQSw9X3Q==", "path": "microsoft.codeanalysis.elfie/1.0.0", "hashPath": "microsoft.codeanalysis.elfie.1.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Features/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-sCVzMtSETGE16KeScwwlVfxaKRbUMSf/cgRPRPMJuou37SLT7XkIBzJu4e7mlFTzpJbfalV5tOcKpUtLO3eJAg==", "path": "microsoft.codeanalysis.features/4.8.0", "hashPath": "microsoft.codeanalysis.features.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Razor/6.0.24": {"type": "package", "serviceable": true, "sha512": "sha512-xIAjR6l/1PO2ILT6/lOGYfe8OzMqfqxh1lxFuM4Exluwc2sQhJw0kS7pEyJ0DE/UMYu6Jcdc53DmjOxQUDT2Pg==", "path": "microsoft.codeanalysis.razor/6.0.24", "hashPath": "microsoft.codeanalysis.razor.6.0.24.nupkg.sha512"}, "Microsoft.CodeAnalysis.Scripting.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-ysiNNbAASVhV9wEd5oY2x99EwaVYtB13XZRjHsgWT/R1mQkxZF8jWsf7JWaZxD1+jNoz1QCQ6nbe+vr+6QvlFA==", "path": "microsoft.codeanalysis.scripting.common/4.8.0", "hashPath": "microsoft.codeanalysis.scripting.common.4.8.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-LXyV+MJKsKRu3FGJA3OmSk40OUIa/dQCFLOnm5X8MNcujx7hzGu8o+zjXlb/cy5xUdZK2UKYb9YaQ2E8m9QehQ==", "path": "microsoft.codeanalysis.workspaces.common/4.8.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.8.0.nupkg.sha512"}, "Microsoft.CognitiveServices.Speech/1.44.0": {"type": "package", "serviceable": true, "sha512": "sha512-pUGHPjxsv72SYw2Fh0rQV8NehZcDqzlUMLZ1keOSWaX8Yqs6QcANIu+xopdRnI2V8rY0x+8GJ8Ern/i4+D6Hdw==", "path": "microsoft.cognitiveservices.speech/1.44.0", "hashPath": "microsoft.cognitiveservices.speech.1.44.0.nupkg.sha512"}, "Microsoft.CognitiveServices.Speech.Remoteconversation/1.44.0": {"type": "package", "serviceable": true, "sha512": "sha512-WcGS6LrX0YtQiP+VNqncPCfVSLj42SQTJDXIPHe65oe97A4z40HeyAT1uwxwyU4OP/pkIqAfWC2qEbtMeXoD5g==", "path": "microsoft.cognitiveservices.speech.remoteconversation/1.44.0", "hashPath": "microsoft.cognitiveservices.speech.remoteconversation.1.44.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-v7HxnYYXGGCJilxeQ4Pdks+popVuGajBpHmau0RU4ACIcbfs5qCNUnCogGpZ+CJ//8Qafhxq7vc5a8L9d6O8Eg==", "path": "microsoft.data.sqlclient/6.0.1", "hashPath": "microsoft.data.sqlclient.6.0.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-f+pRODTWX7Y67jXO3T5S2dIPZ9qMJNySjlZT/TKmWVNWe19N8jcWmHaqHnnchaq3gxEKv1SWVY5EFzOD06l41w==", "path": "microsoft.data.sqlclient.sni.runtime/6.0.2", "hashPath": "microsoft.data.sqlclient.sni.runtime.6.0.2.nupkg.sha512"}, "Microsoft.DiaSymReader/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QcZrCETsBJqy/vQpFtJc+jSXQ0K5sucQ6NUFbTNVHD4vfZZOwjZ/3sBzczkC4DityhD3AVO/+K/+9ioLs1AgRA==", "path": "microsoft.diasymreader/2.0.0", "hashPath": "microsoft.diasymreader.2.0.0.nupkg.sha512"}, "Microsoft.DotNet.Scaffolding.Shared/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9pfRsTzUANgI6J7nFjYip50ifcvmORjMmFByXmdYa//x8toziydhbg0cMylP1S2mRf4/96VKSAfpayYrO3m4Ww==", "path": "microsoft.dotnet.scaffolding.shared/9.0.0", "hashPath": "microsoft.dotnet.scaffolding.shared.9.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-r5hzM6Bhw4X3z28l5vmsaCPjk9VsQP4zaaY01THh1SAYjgTMVadYIvpNkCfmrv/Klks6aIf2A9eY7cpGZab/hg==", "path": "microsoft.entityframeworkcore/9.0.6", "hashPath": "microsoft.entityframeworkcore.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-7MkhPK8emb8hfOx/mFVvHuIHxQ+mH2YdlK4sFUXgsGlvR0A44vsmd2wcHavZOTTzaKhN+aFUVy3zmkztKmTo+A==", "path": "microsoft.entityframeworkcore.abstractions/9.0.6", "hashPath": "microsoft.entityframeworkcore.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-VKggHNQC5FCn3/vooaIM/4aEjGmrmWm78IrdRLz9lLV0Rm9bVHEr/jiWApDkU0U9ec2xGAilvQqJ5mMX7QC2cw==", "path": "microsoft.entityframeworkcore.analyzers/9.0.6", "hashPath": "microsoft.entityframeworkcore.analyzers.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-Ht6OT17sYnO31Dx+hX72YHrc5kZt53g5napaw0FpyIekXCvb+gUVvufEG55Fa7taFm8ccy0Vzs+JVNR9NL0JlA==", "path": "microsoft.entityframeworkcore.relational/9.0.6", "hashPath": "microsoft.entityframeworkcore.relational.9.0.6.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-hr8vJSL1KXkXdgvNYY5peSygSZkoKQ+r6umXGMLoggBQ9NMbf0jo8p13Hy0biON2IS03ixOl0g4Mgw0hjgTksw==", "path": "microsoft.entityframeworkcore.sqlserver/9.0.6", "hashPath": "microsoft.entityframeworkcore.sqlserver.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-bL/xQsVNrdVkzjP5yjX4ndkQ03H3+Bk3qPpl+AMCEJR2RkfgAYmoQ/xXffPV7is64+QHShnhA12YAaFmNbfM+A==", "path": "microsoft.extensions.caching.abstractions/9.0.6", "hashPath": "microsoft.extensions.caching.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-qPW2d798tBPZcRmrlaBJqyChf2+0odDdE+0Lxvrr0ywkSNl1oNMK8AKrOfDwyXyjuLCv0ua7p6nrUExCeXhCcg==", "path": "microsoft.extensions.caching.memory/9.0.6", "hashPath": "microsoft.extensions.caching.memory.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Caching.SqlServer/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-EWeJ+Lyj2NqP7SbJah3ebowR9MNwayYdAvZ7AJUwITMTYmBsBVDYcEHAv49QLaSfqLsBKOrqjuFxG6ke5qUPWw==", "path": "microsoft.extensions.caching.sqlserver/9.0.6", "hashPath": "microsoft.extensions.caching.sqlserver.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-RIEeZxWYm77+OWLwgik7DzSVSONjqkmcbuCb1koZdGAV7BgOUWnLz80VMyHZMw3onrVwFCCMHBBdruBPuQTvkg==", "path": "microsoft.extensions.configuration/9.0.3", "hashPath": "microsoft.extensions.configuration.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-3GgMIi2jP8g1fBW93Z9b9Unamc0SIsgyhiCmC91gq4loTixK9vQMuxxUsfJ1kRGwn+/FqLKwOHqmn0oYWn3Fvw==", "path": "microsoft.extensions.configuration.abstractions/9.0.6", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-tBNMSDJ2q7WQK2zwPhHY5I/q95t7sf6dT079mGrNm0yOZF/gM9JvR/LtCb/rwhRmh7A6XMnzv5WbpCh9KLq9EQ==", "path": "microsoft.extensions.configuration.fileextensions/9.0.3", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mjkp3ZwynNacZk4uq93I0DyCY48FZmi3yRV0xlfeDuWh44KcDunPXHwt8IWr4kL7cVM6eiFVe6YTJg97KzUAUA==", "path": "microsoft.extensions.configuration.json/9.0.3", "hashPath": "microsoft.extensions.configuration.json.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-vS65HMo5RS10DD543fknsyVDxihMcVxVn3/hNaILgBxWYnOLxWIeCIO9X0QFuCvPRNjClvXe9Aj8KaQNx7vFkQ==", "path": "microsoft.extensions.dependencyinjection/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-0Zn6nR/6g+90MxskZyOOMPQvnPnrrGu6bytPwkV+azDcTtCSuQ1+GJUrg8Klmnrjk1i6zMpw2lXijl+tw7Q3kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.6", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/9.0.0-rc.2.24473.5": {"type": "package", "serviceable": true, "sha512": "sha512-Y+L5r4bqbSbhTVVQ8wt7AaO8vtB2ZgEfzI1gN3Ia/vBIsfECOQK8bDItAi7ikb1kk9c0unDnJnM08BuO59ydEQ==", "path": "microsoft.extensions.dependencymodel/9.0.0-rc.2.24473.5", "hashPath": "microsoft.extensions.dependencymodel.9.0.0-rc.2.24473.5.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-/fn0Xe8t+3YbMfwyTk4hFirWyAG1pBA5ogVYsrKAuuD2gbqOWhFuSA28auCmS3z8Y2eq3miDIKq4pFVRWA+J6g==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.3", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Features/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-jZuO3APLh0ePwtT9PDxiMdPwpDdct/kuExlXLCZZ+XFje/Xt815MM827EFJuxTBAbL148ywyfJyjIZ92osP5WA==", "path": "microsoft.extensions.features/9.0.3", "hashPath": "microsoft.extensions.features.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-umczZ3+QPpzlrW/lkvy+IB0p52+qZ5w++aqx2lTCMOaPKzwcbVdrJgiQ3ajw5QWBp7gChLUiCYkSlWUpfjv24g==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.3", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-th2+tQBV5oWjgKhip9GjiIv2AEK3QvfAO3tZcqV3F3dEt5D6Gb411RntCj1+8GS9HaRRSxjSGx/fCrMqIjkb1Q==", "path": "microsoft.extensions.fileproviders.physical/9.0.3", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Rec77KHk4iNpFznHi5/6wF3MlUDcKqg26t8gRYbUm1PSukZ4B6mrXpZsJSNOiwyhhQVkjYbaoZxi5XJgRQ5lFg==", "path": "microsoft.extensions.filesystemglobbing/9.0.3", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-rHabYVhQsGYNfgnfnYLqZRx/hLe85i6jW5rnDjA9pjt3x7yjPv8T/EXcgN5T9T38FAVwZRA+RMGUkEHbxvCOBQ==", "path": "microsoft.extensions.hosting.abstractions/9.0.3", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.3.nupkg.sha512"}, "Microsoft.Extensions.Http/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-kT1ijDKZuSUhBtYoC1sXrmVKP7mA08h9Xrsr4VrS/QOtiKCEtUTTd7dd3XI9dwAb46tZSak13q/zdIcr4jqbyg==", "path": "microsoft.extensions.http/5.0.0", "hashPath": "microsoft.extensions.http.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http.Polly/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tzf+x8HVDhri9dkIGCGOP5RVjgXnTZuBcwZHr0sW6KAtVGPDU0xEDXPDpoW5vwq/K12dMI75vuapUtMw0d/pIw==", "path": "microsoft.extensions.http.polly/5.0.1", "hashPath": "microsoft.extensions.http.polly.5.0.1.nupkg.sha512"}, "Microsoft.Extensions.Localization/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UgvX4Yb2T3tEsKT30ktZr0H7kTRPapCgEH0bdTwxiEGSdA39/hAQMvvb+vgHpqmevDU5+puyI9ujRkmmbF946w==", "path": "microsoft.extensions.localization/9.0.1", "hashPath": "microsoft.extensions.localization.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Localization.Abstractions/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-CABog43lyaZQMjmlktuImCy6zmAzRBaXqN81uPaMQjlp//ISDVYItZPh6KWpWRF4MY/B67X5oDc3JTUpfdocZw==", "path": "microsoft.extensions.localization.abstractions/9.0.1", "hashPath": "microsoft.extensions.localization.abstractions.9.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-XBzjitTFaQhF8EbJ645vblZezV1p52ePTxKHoVkRidHF11Xkjxg94qr0Rvp2qyxK2vBJ4OIZ41NB15YUyxTGMQ==", "path": "microsoft.extensions.logging/9.0.6", "hashPath": "microsoft.extensions.logging.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-LFnyBNK7WtFmKdnHu3v0HOYQ8BcjYuy0jdC9pgCJ/rbLKoJEG9/dBzSKMEeeWDbDeoWS0TIxOC8a9CM5ufca3A==", "path": "microsoft.extensions.logging.abstractions/9.0.6", "hashPath": "microsoft.extensions.logging.abstractions.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-wUPhNM1zsI58Dy10xRdF2+pnsisiUuETg5ZBncyAEEUm/CQ9Q1vmivyUWH8RDbAlqyixf2dJNQ2XZb7HsKUEQw==", "path": "microsoft.extensions.options/9.0.6", "hashPath": "microsoft.extensions.options.9.0.6.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-BHniU24QV67qp1pJknqYSofAPYGmijGI8D+ci9yfw33iuFdyOeB9lWTg78ThyYLyQwZw3s0vZ36VMb0MqbUuLw==", "path": "microsoft.extensions.primitives/9.0.6", "hashPath": "microsoft.extensions.primitives.9.0.6.nupkg.sha512"}, "Microsoft.Identity.Client/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-naJo/Qm35Caaoxp5utcw+R8eU8ZtLz2ALh8S+gkekOYQ1oazfCQMWVT4NJ/FnHzdIJlm8dMz0oMpMGCabx5odA==", "path": "microsoft.identity.client/4.61.3", "hashPath": "microsoft.identity.client.4.61.3.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/4.61.3": {"type": "package", "serviceable": true, "sha512": "sha512-PWnJcznrSGr25MN8ajlc2XIDW4zCFu0U6FkpaNLEWLgd1NgFCp5uDY3mqLDgM8zCN8hqj8yo5wHYfLB2HjcdGw==", "path": "microsoft.identity.client.extensions.msal/4.61.3", "hashPath": "microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-OwmvCXYTttrxV3qT7QKDkoQP4/DB4RWjTwEqV+dNfb2opHn29WGDzoF+r4BVFQVy+BDYMhRlhIp8g3jSyJd+4Q==", "path": "microsoft.identitymodel.abstractions/8.6.1", "hashPath": "microsoft.identitymodel.abstractions.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-CAu9DWsPZVtnyE3bOJ83rlPWpahY37sP/0bIOdRlxS90W88zSI4V3FyoCDlXxV8+gloT+a247pwPXfSNjYyAxw==", "path": "microsoft.identitymodel.jsonwebtokens/8.6.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-BdWlVgJYdmcR9TMUOhaZ3vJyaRO7zr7xgK+cRT4R2q59Xl7JMmTB4ctb/VOsyDhxXb497jDNNvLwldp+2ZVBEg==", "path": "microsoft.identitymodel.logging/8.6.1", "hashPath": "microsoft.identitymodel.logging.8.6.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-FvED2com8LIFl9yFXneiX0uxNf9fuf8jKDFcvxC93qXOAfFa8fdLkCiur1vWF+PvgQHhsHVBe6CtDZHzsN8nCQ==", "path": "microsoft.identitymodel.tokens/8.6.1", "hashPath": "microsoft.identitymodel.tokens.8.6.1.nupkg.sha512"}, "Microsoft.JSInterop/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-IS9tI2pnpeoX+d4CO4FuTsm+6uymanPGSpBhHefTfMNIwBUvwLIcXch4su0QEW4MSslYdDJ9yzz2+OuIUuI5lw==", "path": "microsoft.jsinterop/9.0.6", "hashPath": "microsoft.jsinterop.9.0.6.nupkg.sha512"}, "Microsoft.NET.StringTools/17.10.4": {"type": "package", "serviceable": true, "sha512": "sha512-wyABaqY+IHCMMSTQmcc3Ca6vbmg5BaEPgicnEgpll+4xyWZWlkQqUwafweUd9VAhBb4jqplMl6voUHQ6yfdUcg==", "path": "microsoft.net.stringtools/17.10.4", "hashPath": "microsoft.net.stringtools.17.10.4.nupkg.sha512"}, "Microsoft.NETCore.Platforms/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-z7aeg8oHln2CuNulfhiLYxCVMPEwBl3rzicjvIX+4sUuCwvXw5oXQEtbiU2c0z4qYL5L3Kmx0mMA/+t/SbY67w==", "path": "microsoft.netcore.platforms/3.1.0", "hashPath": "microsoft.netcore.platforms.3.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-W9ho78o/92MUDz04r7Al4dMx7djaqtSJE1cR7fMjy+Mm0StL5pVKXF24qnAFWJlip7KEpAa1QP35davXvuis9w==", "path": "microsoft.visualstudio.web.codegeneration/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Core/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-1VIEZs8DNnefMa0eVDZucz/dk28Sg0QRiNiRJj7SdU8E6UiNJxnkzA748aqA6Qqi8OMTHTBKhzx0Hj9ykIi6/Q==", "path": "microsoft.visualstudio.web.codegeneration.core/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.core.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-nO5MUL3iC0WjtAVea5d4v6kVcoL9ae/PnkC6NeEJhWazHKdKj7xfv6D2QvBx8uCIj8FUu9QpvvdN6m/xMp//EQ==", "path": "microsoft.visualstudio.web.codegeneration.design/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.design.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.EntityFrameworkCore/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-F4+A6CaXmof/QoeWpqaMMeoVinfUSIMKa5xLOrwsZxGfYl6Qryhb06bkJ8yJaF05WefMM/wnj73oI3Ms2bBh7g==", "path": "microsoft.visualstudio.web.codegeneration.entityframeworkcore/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.entityframeworkcore.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Templating/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-euoX0M4JnbzSUcFXfDq+GSSdXNRbKGUBTK+8gcnzHmhY3sHgHn9bgeeZDp+LGuoUQaP+WrWA8Nq92gCTcZLWSA==", "path": "microsoft.visualstudio.web.codegeneration.templating/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.templating.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGeneration.Utils/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-O8uehWLzgQhq3H2f+dxEkuYF8wWoBrT7iKtQXnHAc96qlVdLSARSxt3hlxqFSzK3ZkHp2P6lHt76LRH6J0PDrw==", "path": "microsoft.visualstudio.web.codegeneration.utils/9.0.0", "hashPath": "microsoft.visualstudio.web.codegeneration.utils.9.0.0.nupkg.sha512"}, "Microsoft.VisualStudio.Web.CodeGenerators.Mvc/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WJhdsFXkpA0XR6PCjoxe9pRIqT8NV8Ggojv2cwaeCwxApzTAbLnglwADteeF7WlgHnr1VmJ+xdgzzNAAcJ9+Rg==", "path": "microsoft.visualstudio.web.codegenerators.mvc/9.0.0", "hashPath": "microsoft.visualstudio.web.codegenerators.mvc.9.0.0.nupkg.sha512"}, "Microsoft.Win32.Registry/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-KSrRMb5vNi0CWSGG1++id2ZOs/1QhRqROt+qgbEAdQuGjGrFcl4AOl4/exGPUYz2wUnU42nvJqon1T3U0kPXLA==", "path": "microsoft.win32.registry/4.7.0", "hashPath": "microsoft.win32.registry.4.7.0.nupkg.sha512"}, "MimeKit/4.13.0": {"type": "package", "serviceable": true, "sha512": "sha512-oa4JuhAzJydHnPCc/XWeyBUGd3uiVyWW0NXqOVgkXEHjbHlPVBssklK3mpw9sokjzAaBGdj0bceFsr+NXvAukA==", "path": "mimekit/4.13.0", "hashPath": "mimekit.4.13.0.nupkg.sha512"}, "Mono.TextTemplating/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YqueG52R/Xej4VVbKuRIodjiAhV0HR/XVbLbNrJhCZnzjnSjgMJ/dCdV0akQQxavX6hp/LC6rqLGLcXeQYU7XA==", "path": "mono.texttemplating/3.0.0", "hashPath": "mono.texttemplating.3.0.0.nupkg.sha512"}, "MudBlazor/8.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-KdjXMXiSvl6uNr+S2YDhGI1aX/TULMQejQSniMlp6QTss35NKFrJyrMAmN082HNvOObuKdnTtLpntJ7rM5Op/A==", "path": "mudblazor/8.9.0", "hashPath": "mudblazor.8.9.0.nupkg.sha512"}, "NAudio/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-c0DzwiyyklM0TP39Y7RObwO3QkWecgM6H60ikiEnsV/aEAJPbj5MFCLaD8BSfKuZe0HGuh9GRGWWlJmSxDc9MA==", "path": "naudio/2.2.1", "hashPath": "naudio.2.2.1.nupkg.sha512"}, "NAudio.Asio/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-hQglyOT5iT3XuGpBP8ZG0+aoqwRfidHjTNehpoWwX0g6KJEgtH2VaqM2nuJ2mheKZa/IBqB4YQTZVvrIapzfOA==", "path": "naudio.asio/2.2.1", "hashPath": "naudio.asio.2.2.1.nupkg.sha512"}, "NAudio.Core/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-GgkdP6K/7FqXFo7uHvoqGZTJvW4z8g2IffhOO4JHaLzKCdDOUEzVKtveoZkCuUX8eV2HAINqi7VFqlFndrnz/g==", "path": "naudio.core/2.2.1", "hashPath": "naudio.core.2.2.1.nupkg.sha512"}, "NAudio.Midi/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-6r23ylGo5aeP02WFXsPquz0T0hFJWyh+7t++tz19tc3Kr38NHm+Z9j+FiAv+xkH8tZqXJqus9Q8p6u7bidIgbw==", "path": "naudio.midi/2.2.1", "hashPath": "naudio.midi.2.2.1.nupkg.sha512"}, "NAudio.Vorbis/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-llJMmUnFf0FGf4G1FKNftxt+Vc1CvBCSul0Ugbv/RahGbpKF2p+ujSEJHybPjl9b4FjbK8O2vRFIcRGcVfAooA==", "path": "naudio.vorbis/1.5.0", "hashPath": "naudio.vorbis.1.5.0.nupkg.sha512"}, "NAudio.Wasapi/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-lFfXoqacZZe0WqNChJgGYI+XV/n/61LzPHT3C1CJp4khoxeo2sziyX5wzNYWeCMNbsWxFvT3b3iXeY1UYjBhZw==", "path": "naudio.wasapi/2.2.1", "hashPath": "naudio.wasapi.2.2.1.nupkg.sha512"}, "NAudio.WinMM/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-xFHRFwH4x6aq3IxRbewvO33ugJRvZFEOfO62i7uQJRUNW2cnu6BeBTHUS0JD5KBucZbHZaYqxQG8dwZ47ezQuQ==", "path": "naudio.winmm/2.2.1", "hashPath": "naudio.winmm.2.2.1.nupkg.sha512"}, "NETCore.MailKit/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-+1C0sg4YJQczp/2RVrVbKLIntg7Mou7Ie5M01UBiAsxmIarjfBRlCgKK4sEbPyGask52VRKV29/Zi+41B6LPXA==", "path": "netcore.mailkit/2.1.0", "hashPath": "netcore.mailkit.2.1.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NodaTime/3.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-D1aHhUfPQUxU2nfDCVuSLahpp0xCYZTmj/KNH3mSK/tStJYcx9HO9aJ0qbOP3hzjGPV/DXOqY2AHe27Nt4xs4g==", "path": "nodatime/3.2.1", "hashPath": "nodatime.3.2.1.nupkg.sha512"}, "NuGet.Common/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-T3bCiKUSx8wdYpcqr6Dbx93zAqFp689ee/oa1tH22XI/xl7EUzQ7No/WlE1FUqvEX1+Mqar3wRNAn2O/yxo94g==", "path": "nuget.common/6.11.0", "hashPath": "nuget.common.6.11.0.nupkg.sha512"}, "NuGet.Configuration/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-73QprQqmumFrv3Ooi4YWpRYeBj8jZy9gNdOaOCp4pPInpt41SJJAz/aP4je+StwIJvi5HsgPPecLKekDIQEwKg==", "path": "nuget.configuration/6.11.0", "hashPath": "nuget.configuration.6.11.0.nupkg.sha512"}, "NuGet.DependencyResolver.Core/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoiPKPooA+IF+iCsX1ykwi3M0e+yBL34QnwIP3ujhQEn1dhlP/N1XsYAnKkJPxV15EZCahuuS4HtnBsZx+CHKA==", "path": "nuget.dependencyresolver.core/6.11.0", "hashPath": "nuget.dependencyresolver.core.6.11.0.nupkg.sha512"}, "NuGet.Frameworks/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ew/mrfmLF5phsprysHbph2+tdZ10HMHAURavsr/Kx1WhybDG4vmGuoNLbbZMZOqnPRdpyCTc42OKWLoedxpYtA==", "path": "nuget.frameworks/6.11.0", "hashPath": "nuget.frameworks.6.11.0.nupkg.sha512"}, "NuGet.LibraryModel/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-KUV2eeMICMb24OPcICn/wgncNzt6+W+lmFVO5eorTdo1qV4WXxYGyG1NTPiCY+Nrv5H/Ilnv9UaUM2ozqSmnjw==", "path": "nuget.librarymodel/6.11.0", "hashPath": "nuget.librarymodel.6.11.0.nupkg.sha512"}, "NuGet.Packaging/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-VmUv2LedVuPY1tfNybORO2I9IuqOzeV7I5JBD+PwNvJq2bAqovi4FCw2cYI0g+kjOJXBN2lAJfrfnqtUOlVJdQ==", "path": "nuget.packaging/6.11.0", "hashPath": "nuget.packaging.6.11.0.nupkg.sha512"}, "NuGet.ProjectModel/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0KtmDH6fas97WsN73yV2h1F5JT9o6+Y0wlPK+ij9YLKaAXaF6+1HkSaQMMJ+xh9/jCJG9G6nau6InOlb1g48g==", "path": "nuget.projectmodel/6.11.0", "hashPath": "nuget.projectmodel.6.11.0.nupkg.sha512"}, "NuGet.Protocol/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-p5B8oNLLnGhUfMbcS16aRiegj11pD6k+LELyRBqvNFR/pE3yR1XT+g1XS33ME9wvoU+xbCGnl4Grztt1jHPinw==", "path": "nuget.protocol/6.11.0", "hashPath": "nuget.protocol.6.11.0.nupkg.sha512"}, "NuGet.Versioning/6.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-v/GGlIj2dd7svplFmASWEueu62veKW0MrMtBaZ7QG8aJTSGv2yE+pgUGhXRcQ4nxNOEq/wLBrz1vkth/1SND7A==", "path": "nuget.versioning/6.11.0", "hashPath": "nuget.versioning.6.11.0.nupkg.sha512"}, "NVorbis/0.10.5": {"type": "package", "serviceable": true, "sha512": "sha512-o+IptCG4Avze39HrGeztC+xIp6fOOwGVAwkoa1J++4Ji1WmZ+KIKlFl5wsgxsXqBkmdpfs/vFSUproiLKYa2bw==", "path": "nvorbis/0.10.5", "hashPath": "nvorbis.0.10.5.nupkg.sha512"}, "OpenAI-DotNet/8.6.2": {"type": "package", "serviceable": true, "sha512": "sha512-U/TlcKjBhb+BaoZkpQKB0An+iXkSk/vGEb3NK+dYQA3vzBBfz1Uq4fRgmi53feIsip4Kt373vo0f2JyYCCEtSg==", "path": "openai-dotnet/8.6.2", "hashPath": "openai-dotnet.8.6.2.nupkg.sha512"}, "Polly/7.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-mpQsvlEn4ipgICh5CGyVLPV93RFVzOrBG7wPZfzY8gExh7XgWQn0GCDY9nudbUEJ12UiGPP9i4+CohghBvzhmg==", "path": "polly/7.1.0", "hashPath": "polly.7.1.0.nupkg.sha512"}, "Polly.Extensions.Http/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drrG+hB3pYFY7w1c3BD+lSGYvH2oIclH8GRSehgfyP5kjnFnHKQuuBhuHLv+PWyFuaTDyk/vfRpnxOzd11+J8g==", "path": "polly.extensions.http/3.0.0", "hashPath": "polly.extensions.http.3.0.0.nupkg.sha512"}, "Portable.BouncyCastle/*******": {"type": "package", "serviceable": true, "sha512": "sha512-1jUpszv0ETm+hl78HKnYgY3wPzt6qRtjxaPENNrGCuB8nondbR/j75WAKdd6sxXzOzBcX07WMZhZEYc4s5jVWg==", "path": "portable.bouncycastle/*******", "hashPath": "portable.bouncycastle.*******.nupkg.sha512"}, "PublicHoliday/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoaAmVGyCZSt+EYtfa74wQipSSSxkdsgG+DO0tr4cJe2/2dtaWYW2KlYTAb/3DjY0ThFHOjB1YObrdnkKYkt8A==", "path": "publicholiday/3.2.0", "hashPath": "publicholiday.3.2.0.nupkg.sha512"}, "QRCoder-ImageSharp/0.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-Gk45RWAK/XIg5PjhlU8IusuYYiZmvzL1pPTH+FG2/Q3anTlIATWoCx5yqKsEFhZUz9mn+Sa2MPvawpqxVx10HA==", "path": "qrcoder-imagesharp/0.10.0", "hashPath": "qrcoder-imagesharp.0.10.0.nupkg.sha512"}, "Radzen.Blazor/7.1.3": {"type": "package", "serviceable": true, "sha512": "sha512-qxq0iDyBBLAd2gA3vLE+HmEMlw+wkVhy18Xo/9Jv7v/5YX3i0zfQH7Q+8Gw6n+r7MGqbxkQLE+npI8ioI/9UOA==", "path": "radzen.blazor/7.1.3", "hashPath": "radzen.blazor.7.1.3.nupkg.sha512"}, "RestSharp/112.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-bOUGNZqhhv/QeCXHTKEUil846yBjwWXpzPKjO67kFvaAOoF361z7jLY5BMnuZ6WD2WQRA750sNMcK7MSPCQ5Mg==", "path": "restsharp/112.1.0", "hashPath": "restsharp.112.1.0.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-9fIOOAsyLFid6qKypM2Iy0Z3Q9yoanV8VoYAHtI2sYGMNKzhvRTjgFDHonIiVe+ANtxIxM6SuqUzj0r91nItpA==", "path": "sixlabors.imagesharp/3.1.7", "hashPath": "sixlabors.imagesharp.3.1.7.nupkg.sha512"}, "SkiaSharp/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-DNDwbRjP+aMo27dV2h/uHCVTcWubWWxHnPLiePNyl24f4Pv43mQ8AQQeseOrKR+J3AOCEs6t0sUjo0aa3j3RWQ==", "path": "skiasharp/3.116.1", "hashPath": "skiasharp.3.116.1.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-3KPvpKysDmEMt0NnAZPX5U6KFk0LmG/72/IjAIJemIksIZ0Tjs9pGpr3L+zboVCv1MLVoJLKl3nJDXUG6Jda6A==", "path": "skiasharp.nativeassets.macos/3.116.1", "hashPath": "skiasharp.nativeassets.macos.3.116.1.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/3.116.1": {"type": "package", "serviceable": true, "sha512": "sha512-dRQ75MCI8oz6zAs2Y1w6pq6ARs4MhdNG+gf3doOxOxdnueDXffQLGQIxON54GDoxc0WjKOoHMKBR4DhaduwwQw==", "path": "skiasharp.nativeassets.win32/3.116.1", "hashPath": "skiasharp.nativeassets.win32.3.116.1.nupkg.sha512"}, "Stripe.net/47.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-paFQ1NFjdjd3BHkYS1qOmy7T+Bz663tgAC0unsGu7GLHrWTl5SISj6ukGB6nuycV6a1GA9VZr18eCi4pWNpE2w==", "path": "stripe.net/47.4.0", "hashPath": "stripe.net.47.4.0.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.ClientModel/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-UocOlCkxLZrG2CKMAAImPcldJTxeesHnHGHwhJ0pNlZEvEXcWKuQvVOER2/NiOkJGRJk978SNdw3j6/7O9H1lg==", "path": "system.clientmodel/1.1.0", "hashPath": "system.clientmodel.1.1.0.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Collections.Immutable/9.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-qt4+ClsZYKzar/Rw1zbt0t9iuBUCLVNsjXicEuVQyKL7143CwhWzI2gTwzz8AhSaFYVHnpcb8SBGA+zaY4weUg==", "path": "system.collections.immutable/9.0.1", "hashPath": "system.collections.immutable.9.0.1.nupkg.sha512"}, "System.Composition/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-tRwgcAkDd85O8Aq6zHDANzQaq380cek9lbMg5Qma46u5BZXq/G+XvIYmu+UI+BIIZ9zssXLYrkTykEqxxvhcmg==", "path": "system.composition/7.0.0", "hashPath": "system.composition.7.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-2QzClqjElKxgI1jK1Jztnq44/8DmSuTSGGahXqQ4TdEV0h9s2KikQZIgcEqVzR7OuWDFPGLHIprBJGQEPr8fAQ==", "path": "system.composition.attributedmodel/7.0.0", "hashPath": "system.composition.attributedmodel.7.0.0.nupkg.sha512"}, "System.Composition.Convention/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-IMhTlpCs4HmlD8B+J8/kWfwX7vrBBOs6xyjSTzBlYSs7W4OET4tlkR/Sg9NG8jkdJH9Mymq0qGdYS1VPqRTBnQ==", "path": "system.composition.convention/7.0.0", "hashPath": "system.composition.convention.7.0.0.nupkg.sha512"}, "System.Composition.Hosting/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eB6gwN9S+54jCTBJ5bpwMOVerKeUfGGTYCzz3QgDr1P55Gg/Wb27ShfPIhLMjmZ3MoAKu8uUSv6fcCdYJTN7Bg==", "path": "system.composition.hosting/7.0.0", "hashPath": "system.composition.hosting.7.0.0.nupkg.sha512"}, "System.Composition.Runtime/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aZJ1Zr5Txe925rbo4742XifEyW0MIni1eiUebmcrP3HwLXZ3IbXUj4MFMUH/RmnJOAQiS401leg/2Sz1MkApDw==", "path": "system.composition.runtime/7.0.0", "hashPath": "system.composition.runtime.7.0.0.nupkg.sha512"}, "System.Composition.TypedParts/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZK0KNPfbtxVceTwh+oHNGUOYV2WNOHReX2AXipuvkURC7s/jPwoWfsu3SnDBDgofqbiWr96geofdQ2erm/KTHg==", "path": "system.composition.typedparts/7.0.0", "hashPath": "system.composition.typedparts.7.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-0kw80ykL/iFhNtFbPpjxIavmCk7/l6MqQwNSNhEoSNuI9C82ZZ251thzQZ/btHut21Y2G9M+amQY1K5bhaAjKg==", "path": "system.configuration.configurationmanager/9.0.3", "hashPath": "system.configuration.configurationmanager.9.0.3.nupkg.sha512"}, "System.Data.DataSetExtensions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw==", "path": "system.data.datasetextensions/4.5.0", "hashPath": "system.data.datasetextensions.4.5.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KiLYDu2k2J82Q9BJpWiuQqCkFjRBWVq4jDzKKWawVi9KWzyD0XG3cmfX0vqTQlL14Wi9EufJrbL0+KCLTbqWiQ==", "path": "system.diagnostics.diagnosticsource/6.0.1", "hashPath": "system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-0nDJBZ06DVdTG2vvCZ4XjazLVaFawdT0pnji23ISX8I8fEOlRJyzH2I0kWiAbCtFwry2Zir4qE4l/GStLATfFw==", "path": "system.diagnostics.eventlog/9.0.3", "hashPath": "system.diagnostics.eventlog.9.0.3.nupkg.sha512"}, "System.Formats.Asn1/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-8LbKs3WVqyDSszFZJA9Uxg9z+C6WbPbFTSPm/HjFEsWx49XWs0ueqaAKPWncvFJ8yl4H4C/RTnUMhCKoXkddkg==", "path": "system.formats.asn1/9.0.6", "hashPath": "system.formats.asn1.9.0.6.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.6.1": {"type": "package", "serviceable": true, "sha512": "sha512-EXL1Tj+pizswtHHPiQyNumrTo8XOLX7SoTm7Bz00/DyiIoG2H/kQItoajSvr1MYtvDNXveqULsoWDoJFI3aHzQ==", "path": "system.identitymodel.tokens.jwt/8.6.1", "hashPath": "system.identitymodel.tokens.jwt.8.6.1.nupkg.sha512"}, "System.IO.Hashing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "path": "system.io.hashing/6.0.0", "hashPath": "system.io.hashing.6.0.0.nupkg.sha512"}, "System.IO.Packaging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-KYkIOAvPexQOLDxPO2g0BVoWInnQhPpkFzRqvNrNrMhVT6kqhVr0zEb6KCHlptLFukxnZrjuMVAnxK7pOGUYrw==", "path": "system.io.packaging/8.0.1", "hashPath": "system.io.packaging.8.0.1.nupkg.sha512"}, "System.IO.Pipelines/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jRn6JYnNPW6xgQazROBLSfpdoczRw694vO5kKvMcNnpXuolEixUyw6IBuBs2Y2mlSX/LdLvyyWmfXhaI3ND1Yg==", "path": "system.io.pipelines/7.0.0", "hashPath": "system.io.pipelines.7.0.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ntFHArH3I4Lpjf5m4DCXQHJuGwWPNVJPaAvM95Jy/u+2Yzt2ryiyIN04LAogkjP9DeRcEOiviAjQotfmPq/FrQ==", "path": "system.memory.data/6.0.0", "hashPath": "system.memory.data.6.0.0.nupkg.sha512"}, "System.Net.ServerSentEvents/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Vs/C2V27bjtwLqYag9ATzHilcUn8VQTICre4jSBMGFUeSTxEZffTjb+xZwjcmPsVAjmSZmBI5N7Ezq8UFvqQQg==", "path": "system.net.serversentevents/9.0.3", "hashPath": "system.net.serversentevents.9.0.3.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Metadata/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ptvgrFh7PvWI8bcVqG5rsA/weWM09EnthFHR5SCnS6IN+P4mj6rE1lBDC4U8HL9/57htKAqy4KQ3bBj84cfYyQ==", "path": "system.reflection.metadata/8.0.0", "hashPath": "system.reflection.metadata.8.0.0.nupkg.sha512"}, "System.Reflection.MetadataLoadContext/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SZxrQ4sQYnIcdwiO3G/lHZopbPYQ2lW0ioT4JezgccWUrKaKbHLJbAGZaDfkYjWcta1pWssAo3MOXLsR0ie4tQ==", "path": "system.reflection.metadataloadcontext/8.0.0", "hashPath": "system.reflection.metadataloadcontext.8.0.0.nupkg.sha512"}, "System.Runtime.Caching/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-mp4QRjXcuakbQJon7ZZOS/dUn61bWJzX7GSNkxngKwpa7Mt2CD/Lb2cX4kWniALEM0vzDpavsybq0XQvlqpgGg==", "path": "system.runtime.caching/9.0.3", "hashPath": "system.runtime.caching.9.0.3.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-JECvTt5aFF3WT3gHpfofL2MNNP6v84sxtXxpqhLBCcDRzqsPBmHhQ6shv4DwwN2tRlzsUxtb3G9M3763rbXKDg==", "path": "system.security.accesscontrol/4.7.0", "hashPath": "system.security.accesscontrol.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.Pkcs/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-4XYeGOYBYtvaF2bH+cps+vPWjI5jQCWR7R1B4dI9I1Da9JQrXYP7mxdci+zKV2Ie4JafaOnOoNaDtzxkaXUxCg==", "path": "system.security.cryptography.pkcs/9.0.3", "hashPath": "system.security.cryptography.pkcs.9.0.3.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-4PAwR9l3rhAESfymptisnO2KWpHmiTnKxovnREqmiY4BEakXD2ahQU4/NO0vzEarMw8RGzwpmWoiHwvL/waHfQ==", "path": "system.security.cryptography.protecteddata/9.0.3", "hashPath": "system.security.cryptography.protecteddata.9.0.3.nupkg.sha512"}, "System.Security.Cryptography.Xml/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-dPG84G5/szhNQ8utXUB4NmrJLGvFe8yd4P0MFjy0/VNxkQNGR6CkkLjptkO5b5rcnw/4c2xDKcrwLxgc1zKWgg==", "path": "system.security.cryptography.xml/9.0.3", "hashPath": "system.security.cryptography.xml.9.0.3.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OZIsVplFGaVY90G2SbpgU7EnCoOO5pw1t4ic21dBF3/1omrJFpAGoNAVpPyMVOC90/hvgkGG3VFqR13YgZMQfg==", "path": "system.text.encoding.codepages/8.0.0", "hashPath": "system.text.encoding.codepages.8.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vg8eB5Tawm1IFqj4TVK1czJX89rhFxJo9ELqc/Eiq0eXy13RK00eubyU6TJE6y+GQXjyV5gSfiewDUZjQgSE0w==", "path": "system.text.encodings.web/6.0.0", "hashPath": "system.text.encodings.web.6.0.0.nupkg.sha512"}, "System.Text.Json/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-h+ZtYTyTnTh5Ju6mHCKb3FPGx4ylJZgm9W7Y2psUnkhQRPMOIxX+TCN0ZgaR/+Yea+93XHWAaMzYTar1/EHIPg==", "path": "system.text.json/9.0.6", "hashPath": "system.text.json.9.0.6.nupkg.sha512"}, "System.Threading.Channels/9.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-Ao0iegVONKYVw0eWxJv0ArtMVfkFjgyyYKtUXru6xX5H95flSZWW3QCavD4PAgwpc0ETP38kGHaYbPzSE7sw2w==", "path": "system.threading.channels/9.0.3", "hashPath": "system.threading.channels.9.0.3.nupkg.sha512"}, "System.Threading.Tasks.Dataflow/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7V0I8tPa9V7UxMx/+7DIwkhls5ouaEMQx6l/GwGm1Y8kJQ61On9B/PxCXFLbgu5/C47g0BP2CUYs+nMv1+Oaqw==", "path": "system.threading.tasks.dataflow/8.0.0", "hashPath": "system.threading.tasks.dataflow.8.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "Telegram.Bot/22.4.4": {"type": "package", "serviceable": true, "sha512": "sha512-LnSX4GMA3p4XZY6xsBZ9zQGyMYUW3htTcBNI19g8ISMM4/BWz7zLFXrrZ6nfE/vI9FB8RvdDrfqiaoB6911qmA==", "path": "telegram.bot/22.4.4", "hashPath": "telegram.bot.22.4.4.nupkg.sha512"}, "TinyMCE.Blazor/2.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-yq2h6s3Sq0QrU9m4POfB9qiXjUsCDMvN4QsKi6jOKxYmSFWUbopB6gVJKDRLDQ+TpQaj5FTCzpTwSgehZ397/Q==", "path": "tinymce.blazor/2.1.0", "hashPath": "tinymce.blazor.2.1.0.nupkg.sha512"}, "WebPush/1.0.12": {"type": "package", "serviceable": true, "sha512": "sha512-vc91Uby6UaQQijOPRY4B1tWFaQJlbfOyfOMIPpdAT6mANcPhHcvDJeLjr6B0Adjij71WIAH718MC7coQdrJZAw==", "path": "webpush/1.0.12", "hashPath": "webpush.1.0.12.nupkg.sha512"}, "eZeroCore/1.2.1": {"type": "project", "serviceable": false, "sha512": ""}}}