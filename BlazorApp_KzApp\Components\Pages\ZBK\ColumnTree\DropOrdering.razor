@inject IJSRuntime JSRuntime
<style>
    .movingOver {
        background-color: #000;
        color: #ff0;
        transition: background-color 0.5s ease-in-out;
        transition: color 0.5s ease-in-out;
    }
</style>
<div class="list-group" ondragover="event.preventDefault();"
     ondragstart="event.dataTransfer.setData('', event.target.id);">
    @foreach (var item in Models.OrderBy(x => x.Order))
    {
        if (UseDrop)
        {
            var borderStyle = item.IsDragOver ? "border-style: solid none none none; border-color:red;" : "";
            <div @ondrop="()=>HandleDrop(item)" @onclick="()=> OnClick.InvokeAsync(item.Id)" @key="item">
                <div @ondragleave="@(()=> {item.IsDragOver = false;})"
                     @ondragenter="@(()=>{item.IsDragOver = true;})"
                     style="@(borderStyle)"
                     class="list-group-item list-group-item-action hand"
                     @ondragstart="() => draggingModel = item"
                     @ondragend="()=> draggingModel = null" draggable="true">
                    <i class="fal fa-grip-lines-vertical me-2"></i>
                    @item.Name
                </div>
            </div>
        }
        else
        {
            <div class="input-group d-flex
            justify-content-between mt-1 @(item.IsDragOver ? "movingOver" : "")">

                <button @onclick="() => MoveItem(item, -1)" class="btn btn-dark btn-sm" id="<EMAIL>">&#8679;</button>

                <div class="text-start p-1 ps-2 flex-grow-1 border eDot"
                     @onclick="()=> OnClick.InvokeAsync(item.Id)"
                style="width:100px;">@item.Name</div>

                <button @onclick="() => MoveItem(item, 1)" class="btn btn-dark btn-sm" id="<EMAIL>">&#8681;</button>

            </div>
        }
    }
</div>

@code {

    private bool IsAndroidDevice { get; set; }
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            IsAndroidDevice = await JSRuntime.InvokeAsync<bool>("isAndroidDevice");
        }
    }
    //https://github.com/tesar-tech/DragAndDropList
    [Parameter]
    public List<eZeroCore.Web.BlazorServerUI.DropOrdering.Model> Models { get; set; } = new();

    [Parameter]
    public EventCallback<List<eZeroCore.Web.BlazorServerUI.DropOrdering.Model>> OnDropOver { get; set; }

    [Parameter]
    public bool UseDrop { get; set; }

    [Parameter]
    public EventCallback<string> OnClick { get; set; }

    private void DropResult()
    {
        OnDropOver.InvokeAsync(Models);
    }

    private void HandleDrop(eZeroCore.Web.BlazorServerUI.DropOrdering.Model landingModel)
    {
        //landing model -> where the drop happened
        if (draggingModel is null) return;
        int originalOrderLanding = landingModel.Order;//keep the original order for later
                                                      //increase model under by 1
        Models.Where(x => x.Order >= landingModel.Order).ToList().ForEach(x => x.Order++);
        draggingModel.Order = originalOrderLanding;//replace landing model
        int ii = 0;
        foreach (var model in Models.OrderBy(x => x.Order).ToList())
        {
            model.Order = ii++;//keep the numbers from 0 to size-1
            model.IsDragOver = false;//remove drag over.
        }
        DropResult();
    }

    private eZeroCore.Web.BlazorServerUI.DropOrdering.Model? draggingModel;//the model that is being dragged

    private async Task MoveItem(eZeroCore.Web.BlazorServerUI.DropOrdering.Model item, int direction)
    {
        int currentIndex = Models.IndexOf(item);
        int newIndex = currentIndex + direction;

        if (newIndex >= 0 && newIndex < Models.Count)
        {
            Models[currentIndex].IsDragOver = true;

            Models[currentIndex].Order = newIndex;
            Models[newIndex].Order = currentIndex;

            Models.Sort((a, b) => a.Order.CompareTo(b.Order));

            await Task.Delay(800);

            await JSRuntime.InvokeVoidAsync("document.documentElement.focus");

            Models[currentIndex].IsDragOver = false;
            Models[newIndex].IsDragOver = false;

            await InvokeAsync(StateHasChanged);
        }
    }
}
