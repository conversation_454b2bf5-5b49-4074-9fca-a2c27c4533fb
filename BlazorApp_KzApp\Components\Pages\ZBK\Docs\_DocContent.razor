@inject IJSRuntime JS
@inject NavigationManager NavManagerObj
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public eZeroCore.Web.Document DocObj { get; set; } = new();

    [Parameter]
    public string PageHeaderClass { get; set; } = "";

    [Parameter]
    public bool HasPaid { get; set; }

    string? sContentPoster { get; set; } = "";
    bool videoStatus { get; set; } = true;
    bool AllowReader { get; set; }
    string sColIdOrUrlWrite = "";

    eZeroCore.Web.Document MainDoc { get; set; } = new();

    protected override Task OnInitializedAsync()
    {
        MainDoc = CObj.ThisDoc ?? new();
        return base.OnInitializedAsync();
    }


    string docId = "";
    protected override async Task OnParametersSetAsync()
    {
        if (!DocObj.DocId.Equals(docId, StringComparison.OrdinalIgnoreCase) && (!string.IsNullOrEmpty(DocObj.ColumnId) || !string.IsNullOrEmpty(DocObj.ReplyDocOrTargetId)))
        {
            docId = DocObj.DocId;
            await Init();
        }
    }

    async Task Init()
    {
        if (!CObj.IsBMS && string.IsNullOrEmpty(docId))
        {
            sColIdOrUrlWrite = CObj.PageValue.Split('.').FirstOrDefault() ?? "";
            DocObj = eZeroCore.Web.Document.GetDoc(sColIdOrUrlWrite);
            eZeroCore.Web.Document.PageViewAddOn(DocObj.DocId);
        }
        sContentPoster = DocObj.TitlePhotoUrls.Split(',').FirstOrDefault();
        videoStatus = await DocObj.VideoStatus();
        if (sContentPoster is null)
        {
            sContentPoster = "";
        }
        if (eZeroCore.Web.Stk.WebDefaultValue.PayDocumentsColId.ToLower() == DocObj.ColumnId.ToLower())
        {
            //AllowReader = await _DocumentPayment.ShowSubInfo(User);
        }
        else
        {
            AllowReader = true;
        }
        await Task.Delay(0);
    }
}

<div class="pageHeader @PageHeaderClass">
    <h1 id="title_@(DocObj.DocId)">
        @MainDoc.FullTitle
    </h1>
    @if (!MainDoc.DocId.Equals(DocObj.DocId, StringComparison.OrdinalIgnoreCase))
    {
        <h3>
            @DocObj.FullTitle
        </h3>
    }
    <p class="docSummary @(string.IsNullOrEmpty(DocObj.DocContentSummary)?"d-none":"")">
        @DocObj.DocContentSummary
    </p>
    <div class="d-flex justify-content-between">
        <div class="m-2 ms-0">
            @foreach (string tag in MainDoc.Tags.Split(','))
            {
                <span class="badge bg-dark fw-bold me-1">
                    @tag
                </span>
            }
        </div>
        <label class="DocInfoLabel m-2">
            <i class="fa-solid fa-calendar-days me-1"></i>
            @DocObj.CreateDate!.Value.ToString("yyyy/MM/dd")
        </label>
    </div>
</div>
<div class="pageContent docContent">
    @if (string.IsNullOrEmpty(DocObj.VideoOrAudioUrlOrID) && !string.IsNullOrEmpty(sContentPoster))
    {
        <div class="ratio ratio-16x9 bgImg" style="background-image:url(@sContentPoster)">
            &nbsp;
        </div>
    }
    else if (HasPaid)
    {
        <_CloudFlarePlayer VideoID="@DocObj.VideoOrAudioUrlOrID" PosterImg="@sContentPoster" ReadyToStream=@videoStatus />
    }
   
    @if (!string.IsNullOrEmpty(DocObj.DocContent))
    {
        <div class="EditorHtmlContent @(CObj.IsMobile?"mt-2":"mt-3")">
            @(new MarkupString(DocObj.DocContent))
        </div>
    }
    @if (!MainDoc.DocId.Equals(DocObj.DocId, StringComparison.OrdinalIgnoreCase) && !string.IsNullOrEmpty(MainDoc.DocContent))
    {
        <div class="EditorHtmlContent @(CObj.IsMobile?"mt-2":"mt-3")">
            @(new MarkupString(MainDoc.DocContent))
        </div>
    }
</div>
