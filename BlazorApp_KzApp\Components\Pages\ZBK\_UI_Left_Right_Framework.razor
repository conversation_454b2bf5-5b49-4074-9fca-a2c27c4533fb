@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public RenderFragment? LeftContent { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string ChildContentClass { get; set; } = "";

    [Parameter]
    public string ChildContentStyle { get; set; } = "";

    /// <summary>
    /// sticky-lg-top
    /// </summary>
    [Parameter]
    public string LeftContentClass { get; set; } = "";

    [Parameter]
    public string LeftContentDomId { get; set; } = "";

    [Parameter]
    public bool Row_md_Reverse { get; set; }

    /// <summary>
    /// top:30px;
    /// </summary>
    [Parameter]
    public string LeftContentStyle { get; set; } = "";

    [Parameter]
    public bool LeftContentBorder { get; set; } = true;

    [Parameter]
    public string PanelContentClass { get; set; } = "";

    [Parameter]
    public bool SmallLeftMenu { get; set; }

    [Parameter]
    public int TopFixPx { get; set; } = 21;

    string DomId { get; set; } = EString.CreateId("div");

    //col-12 col-xl-@(SmallLeftMenu?"2":"4") mt-2 px-1  // col-md-5 col-lg-5 col-xl-4
    //col-12 @(LeftContent is not null?$"col-xl-{(SmallLeftMenu?"10":"8")}":"") order-1 order-xl-0 mt-2 px-1
    // col-md-7 col-lg-7 col-xl-8
    //<i class="fa-solid fa-bars text-warning d-none"></i>
     /*
     * <button class="btn btn-phoenix-secondary text-body-tertiary position-absolute top-50 start-50 translate-middle border-0 d-flex justify-content-between" data-phoenix-toggle="offcanvas" data-phoenix-target="#@DomId">
    <i class="fal fa-chevron-double-right"></i>
    </button>
    */

    public async Task CloseLeftContent()
    {
        await JS.InvokeVoidAsync("eval", $"$('#{DomId}').removeClass('show');");
    }
}

@if (LeftContent is not null)
{
    <a class="card setting-toggle d-sm-block d-md-none" href="javascript:;" data-phoenix-toggle="offcanvas" data-phoenix-target="#@DomId">
        <div class="card-body d-flex align-items-center px-5 py-1">
            <i class="fal fa-chevron-double-up text-warning fa-bounce fw-bold" style="--fa-animation-duration: 2s; --fa-fade-opacity: 0.6;"></i>
        </div>
    </a>
}

<div class="row pt-md-2 @(Row_md_Reverse?"flex-row-reverse":"") @PanelContentClass">
    @if (LeftContent is not null)
    {
        <div class="col-md-5 col-xl-@(SmallLeftMenu?"2":"4") mt-md-2 px-1">
            <div class="sticky-xxl-top z-1" style="top: calc(var(--phoenix-navbar-top-height) + @(TopFixPx)px);">
                @* bg-body *@
                <div class="lead-details-offcanvas scrollbar phoenix-offcanvas phoenix-offcanvas-fixed _UI_Left_Right_Framework bg-body show" id="@DomId">
                    <div id="@LeftContentDomId" class="@(LeftContentBorder?"card shadow-none border":"") pb-200 pb-md-2 @LeftContentClass" style="@LeftContentStyle">
                        @LeftContent
                    </div>
                </div>
                <div class="phoenix-offcanvas-backdrop d-lg-none top-0"
                     data-phoenix-backdrop="data-phoenix-backdrop"></div>
            </div>
        </div>
    }
    <div class="col-md-7 @(LeftContent is not null?$"col-xl-{(SmallLeftMenu?"10":"8")}":"") order-1 order-xl-0 mt-2 px-1 position-relative">
        <div class="@ChildContentClass" style="@ChildContentStyle">
            @ChildContent
        </div>
    </div>
</div>