{"version": 3, "file": "calendar.js", "sources": ["../../../src/js/utils.js", "../../../src/js/theme/fullcalendar.js", "../../../src/js/theme/calendar/events.js", "../../../src/js/theme/calendar/template.js", "../../../src/js/theme/calendar/app-calendar.js", "../../../src/js/pages/calendar.js"], "sourcesContent": ["/* -------------------------------------------------------------------------- */\r\n/*                                    Utils                                   */\r\n/* -------------------------------------------------------------------------- */\r\nexport const docReady = fn => {\r\n  // see if DOM is already available\r\n  if (document.readyState === 'loading') {\r\n    document.addEventListener('DOMContentLoaded', fn);\r\n  } else {\r\n    setTimeout(fn, 1);\r\n  }\r\n};\r\n\r\nexport const toggleColor = (lightColor, darkColor) => {\r\n  const currentMode = getItemFromStore('phoenixTheme');\r\n  const mode = currentMode === 'auto' ? getSystemTheme() : currentMode;\r\n  return mode === 'light' ? lightColor : darkColor;\r\n};\r\n\r\nexport const resize = fn => window.addEventListener('resize', fn);\r\n\r\nexport const isIterableArray = array => Array.isArray(array) && !!array.length;\r\n\r\nexport const camelize = str => {\r\n  const text = str.replace(/[-_\\s.]+(.)?/g, (_, c) =>\r\n    c ? c.toUpperCase() : ''\r\n  );\r\n  return `${text.substr(0, 1).toLowerCase()}${text.substr(1)}`;\r\n};\r\n\r\nexport const getData = (el, data) => {\r\n  try {\r\n    return JSON.parse(el.dataset[camelize(data)]);\r\n  } catch (e) {\r\n    return el.dataset[camelize(data)];\r\n  }\r\n};\r\n\r\n/* ----------------------------- Colors function ---------------------------- */\r\n\r\nexport const hexToRgb = hexValue => {\r\n  let hex;\r\n  hexValue.indexOf('#') === 0\r\n    ? (hex = hexValue.substring(1))\r\n    : (hex = hexValue);\r\n  // Expand shorthand form (e.g. \"03F\") to full form (e.g. \"0033FF\")\r\n  const shorthandRegex = /^#?([a-f\\d])([a-f\\d])([a-f\\d])$/i;\r\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(\r\n    hex.replace(shorthandRegex, (m, r, g, b) => r + r + g + g + b + b)\r\n  );\r\n  return result\r\n    ? [\r\n        parseInt(result[1], 16),\r\n        parseInt(result[2], 16),\r\n        parseInt(result[3], 16)\r\n      ]\r\n    : null;\r\n};\r\n\r\nexport const rgbaColor = (color = '#fff', alpha = 0.5) =>\r\n  `rgba(${hexToRgb(color)}, ${alpha})`;\r\n\r\n/* --------------------------------- Colors --------------------------------- */\r\n\r\nexport const getColor = (name, dom = document.documentElement) => {\r\n  return getComputedStyle(dom).getPropertyValue(`--phoenix-${name}`).trim();\r\n};\r\n\r\nexport const hasClass = (el, className) => {\r\n  !el && false;\r\n  return el.classList.value.includes(className);\r\n};\r\n\r\nexport const addClass = (el, className) => {\r\n  el.classList.add(className);\r\n};\r\n\r\nexport const getOffset = el => {\r\n  const rect = el.getBoundingClientRect();\r\n  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;\r\n  const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\r\n  return { top: rect.top + scrollTop, left: rect.left + scrollLeft };\r\n};\r\n\r\nexport const isScrolledIntoView = el => {\r\n  let top = el.offsetTop;\r\n  let left = el.offsetLeft;\r\n  const width = el.offsetWidth;\r\n  const height = el.offsetHeight;\r\n\r\n  while (el.offsetParent) {\r\n    // eslint-disable-next-line no-param-reassign\r\n    el = el.offsetParent;\r\n    top += el.offsetTop;\r\n    left += el.offsetLeft;\r\n  }\r\n\r\n  return {\r\n    all:\r\n      top >= window.pageYOffset &&\r\n      left >= window.pageXOffset &&\r\n      top + height <= window.pageYOffset + window.innerHeight &&\r\n      left + width <= window.pageXOffset + window.innerWidth,\r\n    partial:\r\n      top < window.pageYOffset + window.innerHeight &&\r\n      left < window.pageXOffset + window.innerWidth &&\r\n      top + height > window.pageYOffset &&\r\n      left + width > window.pageXOffset\r\n  };\r\n};\r\n\r\nexport const breakpoints = {\r\n  xs: 0,\r\n  sm: 576,\r\n  md: 768,\r\n  lg: 992,\r\n  xl: 1200,\r\n  xxl: 1540\r\n};\r\n\r\nexport const getBreakpoint = el => {\r\n  const classes = el && el.classList.value;\r\n  let breakpoint;\r\n  if (classes) {\r\n    breakpoint =\r\n      breakpoints[\r\n        classes\r\n          .split(' ')\r\n          .filter(cls => cls.includes('navbar-expand-'))\r\n          .pop()\r\n          .split('-')\r\n          .pop()\r\n      ];\r\n  }\r\n  return breakpoint;\r\n};\r\n\r\n/* --------------------------------- Cookie --------------------------------- */\r\n\r\nexport const setCookie = (name, value, seconds) => {\r\n  const expires = window.dayjs().add(seconds, 'second').toDate();\r\n  document.cookie = `${name}=${value};expires=${expires}`;\r\n};\r\n\r\nexport const getCookie = name => {\r\n  const keyValue = document.cookie.match(`(^|;) ?${name}=([^;]*)(;|$)`);\r\n  return keyValue ? keyValue[2] : keyValue;\r\n};\r\n\r\nexport const settings = {\r\n  tinymce: {\r\n    theme: 'oxide'\r\n  },\r\n  chart: {\r\n    borderColor: 'rgba(255, 255, 255, 0.8)'\r\n  }\r\n};\r\n\r\n/* -------------------------- Chart Initialization -------------------------- */\r\n\r\nexport const newChart = (chart, config) => {\r\n  const ctx = chart.getContext('2d');\r\n  return new window.Chart(ctx, config);\r\n};\r\n\r\n/* ---------------------------------- Store --------------------------------- */\r\n\r\nexport const getItemFromStore = (key, defaultValue, store = localStorage) => {\r\n  try {\r\n    return JSON.parse(store.getItem(key)) || defaultValue;\r\n  } catch {\r\n    return store.getItem(key) || defaultValue;\r\n  }\r\n};\r\n\r\nexport const setItemToStore = (key, payload, store = localStorage) =>\r\n  store.setItem(key, payload);\r\nexport const getStoreSpace = (store = localStorage) =>\r\n  parseFloat(\r\n    (\r\n      escape(encodeURIComponent(JSON.stringify(store))).length /\r\n      (1024 * 1024)\r\n    ).toFixed(2)\r\n  );\r\n\r\n/* get Dates between */\r\n\r\nexport const getDates = (\r\n  startDate,\r\n  endDate,\r\n  interval = 1000 * 60 * 60 * 24\r\n) => {\r\n  const duration = endDate - startDate;\r\n  const steps = duration / interval;\r\n  return Array.from(\r\n    { length: steps + 1 },\r\n    (v, i) => new Date(startDate.valueOf() + interval * i)\r\n  );\r\n};\r\n\r\nexport const getPastDates = duration => {\r\n  let days;\r\n\r\n  switch (duration) {\r\n    case 'week':\r\n      days = 7;\r\n      break;\r\n    case 'month':\r\n      days = 30;\r\n      break;\r\n    case 'year':\r\n      days = 365;\r\n      break;\r\n\r\n    default:\r\n      days = duration;\r\n  }\r\n\r\n  const date = new Date();\r\n  const endDate = date;\r\n  const startDate = new Date(new Date().setDate(date.getDate() - (days - 1)));\r\n  return getDates(startDate, endDate);\r\n};\r\n\r\n/* Get Random Number */\r\nexport const getRandomNumber = (min, max) => {\r\n  return Math.floor(Math.random() * (max - min) + min);\r\n};\r\n\r\nexport const getSystemTheme = () =>\r\n  window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\r\n\r\n// export const handleThemeDropdownIcon = value => {\r\n//   document.querySelectorAll('[data-theme-dropdown-toggle-icon]').forEach(el => {\r\n//     const theme = getData(el, 'theme-dropdown-toggle-icon');\r\n\r\n//     if (value === theme) {\r\n//       el.classList.remove('d-none');\r\n//     } else {\r\n//       el.classList.add('d-none');\r\n//     }\r\n//   });\r\n// };\r\n// handleThemeDropdownIcon(getItemFromStore('phoenixTheme'));\r\n\r\nexport default {\r\n  docReady,\r\n  toggleColor,\r\n  resize,\r\n  isIterableArray,\r\n  camelize,\r\n  getData,\r\n  hasClass,\r\n  addClass,\r\n  hexToRgb,\r\n  rgbaColor,\r\n  getColor,\r\n  breakpoints,\r\n  // getGrays,\r\n  getOffset,\r\n  isScrolledIntoView,\r\n  getBreakpoint,\r\n  setCookie,\r\n  getCookie,\r\n  newChart,\r\n  settings,\r\n  getItemFromStore,\r\n  setItemToStore,\r\n  getStoreSpace,\r\n  getDates,\r\n  getPastDates,\r\n  getRandomNumber,\r\n  getSystemTheme\r\n  // handleThemeDropdownIcon\r\n};\r\n", "/* -------------------------------------------------------------------------- */\r\n/*                                   Calendar                                 */\r\n\r\n/* -------------------------------------------------------------------------- */\r\nconst renderCalendar = (el, option) => {\r\n  const { merge } = window._;\r\n\r\n  const options = merge(\r\n    {\r\n      initialView: 'dayGridMonth',\r\n      editable: true,\r\n      direction: document.querySelector('html').getAttribute('dir'),\r\n      headerToolbar: {\r\n        left: 'prev,next today',\r\n        center: 'title',\r\n        right: 'dayGridMonth,timeGridWeek,timeGridDay'\r\n      },\r\n      buttonText: {\r\n        month: 'Month',\r\n        week: 'Week',\r\n        day: 'Day'\r\n      }\r\n    },\r\n    option\r\n  );\r\n  const calendar = new window.FullCalendar.Calendar(el, options);\r\n  calendar.render();\r\n  document\r\n    .querySelector('.navbar-vertical-toggle')\r\n    ?.addEventListener('navbar.vertical.toggle', () => calendar.updateSize());\r\n  return calendar;\r\n};\r\n\r\nexport const fullCalendarInit = () => {\r\n  const { getData } = window.phoenix.utils;\r\n\r\n  const calendars = document.querySelectorAll('[data-calendar]');\r\n  calendars.forEach(item => {\r\n    const options = getData(item, 'calendar');\r\n    renderCalendar(item, options);\r\n  });\r\n};\r\n\r\nconst fullCalendar = {\r\n  renderCalendar,\r\n  fullCalendarInit\r\n};\r\nexport default fullCalendar;\r\n", "const { dayjs } = window;\r\nconst currentDay = dayjs && dayjs().format('DD');\r\nconst currentMonth = dayjs && dayjs().format('MM');\r\nconst prevMonth = dayjs && dayjs().subtract(1, 'month').format('MM');\r\nconst nextMonth = dayjs && dayjs().add(1, 'month').format('MM');\r\nconst currentYear = dayjs && dayjs().format('YYYY');\r\nconst events = [\r\n  {\r\n    title: 'Boot Camp',\r\n    start: `${currentYear}-${currentMonth}-01 10:00:00`,\r\n    end: `${currentYear}-${currentMonth}-03 16:00:00`,\r\n    description:\r\n      \"Boston Harbor Now in partnership with the Friends of Christopher Columbus Park, the Wharf District Council and the City of Boston is proud to announce the New Year's Eve Midnight Harbor Fireworks! This beloved nearly 40-year old tradition is made possible by the generous support of local waterfront organizations and businesses and the support of the City of Boston and the Office of Mayor <PERSON>.\",\r\n    className: 'text-success',\r\n    location:\r\n      'Boston Harborwalk, Christopher Columbus Park, <br /> Boston, MA 02109, United States',\r\n    organizer: 'Boston Harbor Now'\r\n  },\r\n  {\r\n    title: `Crain's New York Business `,\r\n    start: `${currentYear}-${currentMonth}-11`,\r\n    description:\r\n      \"Crain's 2020 Hall of Fame. Sponsored Content By Crain's Content Studio. Crain's Content Studio Presents: New Jersey: Perfect for Business. Crain's Business Forum: Letitia James, New York State Attorney General. Crain's NYC Summit: Examining racial disparities during the pandemic\",\r\n    className: 'text-primary'\r\n  },\r\n  {\r\n    title: 'Conference',\r\n    start: `${currentYear}-${currentMonth}-${currentDay}`,\r\n    description:\r\n      'The Milken Institute Global Conference gathered the best minds in the world to tackle some of its most stubborn challenges. It was a unique experience in which individuals with the power to enact change connected with experts who are reinventing health, technology, philanthropy, industry, and media.',\r\n    className: 'text-success',\r\n    // allDay: true,\r\n    schedules: [\r\n      {\r\n        title: 'Reporting',\r\n        start: `${currentYear}-${currentMonth}-${currentDay} 11:00:00`,\r\n        description:\r\n          'Time to start the conference and will briefly describe all information about the event.  ',\r\n        className: 'text-success '\r\n      },\r\n      {\r\n        title: 'Lunch',\r\n        start: `${currentYear}-${currentMonth}-${currentDay} 14:00:00`,\r\n        description: 'Lunch facility for all the attendance in the conference.',\r\n        className: 'text-info'\r\n      },\r\n      {\r\n        title: 'Contest',\r\n        start: `${currentYear}-${currentMonth}-${currentDay} 16:00:00`,\r\n        description: 'The starting of the programming contest',\r\n        className: 'text-success'\r\n      },\r\n      {\r\n        title: 'Dinner',\r\n        start: `${currentYear}-${currentMonth}-${currentDay} 22:00:00`,\r\n        description: 'Dinner facility for all the attendance in the conference',\r\n        className: 'text-success'\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    title: `ICT Expo ${currentYear} - Product Release`,\r\n    start: `${currentYear}-${currentMonth}-16 10:00:00`,\r\n    description: `ICT Expo ${currentYear} is the largest private-sector exposition aimed at showcasing IT and ITES products and services in Switzerland.`,\r\n    end: `${currentYear}-${currentMonth}-18 16:00:00`,\r\n    className: 'text-warning',\r\n    allDay: true\r\n  },\r\n  {\r\n    title: 'Meeting',\r\n    start: `${currentYear}-${currentMonth}-07 10:00:00`,\r\n    description:\r\n      'Discuss about the upcoming projects in current year and assign all tasks to the individuals',\r\n    className: 'text-info'\r\n  },\r\n  {\r\n    title: 'Contest',\r\n    start: `${currentYear}-${currentMonth}-14 10:00:00`,\r\n    className: 'text-info',\r\n    description:\r\n      'PeaceX is an international peace and amity organisation that aims at casting a pall at the striking issues surmounting the development of peoples and is committed to impacting the lives of young people all over the world.'\r\n  },\r\n  {\r\n    title: 'Event With Url',\r\n    start: `${currentYear}-${currentMonth}-23`,\r\n    description:\r\n      'Sample example of a event with url. Click the event, will redirect to the given link.',\r\n    className: 'text-success',\r\n    url: 'http://google.com'\r\n  },\r\n  {\r\n    title: 'Competition',\r\n    start: `${currentYear}-${currentMonth}-26`,\r\n    description:\r\n      'The Future of Zambia – Top 30 Under 30 is an annual award, ranking scheme, and recognition platform for young Zambian achievers under the age of 30, who are building brands, creating jobs, changing the game, and transforming the country.',\r\n    className: 'text-danger'\r\n  },\r\n  {\r\n    title: 'Birthday Party',\r\n    start: `${currentYear}-${nextMonth}-05`,\r\n    description: 'Will celebrate birthday party with my friends and family',\r\n    className: 'text-primary'\r\n  },\r\n  {\r\n    title: 'Click for Google',\r\n    url: 'http://google.com/',\r\n    start: `${currentYear}-${prevMonth}-10`,\r\n    description:\r\n      'Applications are open for the New Media Writing Prize 2020. The New Media Writing Prize (NMWP) showcases exciting and inventive stories and poetry that integrate a variety of formats, platforms, and digital media.',\r\n    className: 'text-primary'\r\n  }\r\n];\r\n\r\nexport default events;\r\n", "const getTemplate = event => `\r\n<div class=\"modal-header ps-card border-bottom border-translucent\">\r\n  <div>\r\n    <h4 class=\"modal-title text-body-highlight mb-0\">${event.title}</h4>\r\n    ${\r\n      event.extendedProps.organizer\r\n        ? `<p class=\"mb-0 fs-9 mt-1\">\r\n        by <a href=\"#!\">${event.extendedProps.organizer}</a>\r\n      </p>`\r\n        : ''\r\n    }\r\n  </div>\r\n  <button type=\"button\" class=\"btn p-1 fw-bolder\" data-bs-dismiss=\"modal\" aria-label=\"Close\">\r\n    <span class='fas fa-times fs-8'></span>\r\n  </button>\r\n\r\n</div>\r\n\r\n<div class=\"modal-body px-card pb-card pt-1 fs-9\">\r\n  ${\r\n    event.extendedProps.description\r\n      ? `\r\n      <div class=\"mt-3 border-bottom pb-3 border-translucent\">\r\n        <h5 class='mb-0 text-body-secondary'>Description</h5>\r\n        <p class=\"mb-0 mt-2\">\r\n          ${event.extendedProps.description.split(' ').slice(0, 30).join(' ')}\r\n        </p>\r\n      </div>\r\n    `\r\n      : ''\r\n  } \r\n  <div class=\"mt-4 ${\r\n    event.extendedProps.location ? 'border-bottom pb-3 border-translucent' : ''\r\n  }\">\r\n    <h5 class='mb-0 text-body-secondary'>Date and Time</h5>\r\n    <p class=\"mb-1 mt-2\">\r\n    ${\r\n      window.dayjs &&\r\n      window.dayjs(event.start).format('dddd, MMMM D, YYYY, h:mm A')\r\n    } \r\n    ${\r\n      event.end\r\n        ? `– ${\r\n            window.dayjs &&\r\n            window\r\n              .dayjs(event.end)\r\n              .subtract(1, 'day')\r\n              .format('dddd, MMMM D, YYYY, h:mm A')\r\n          }`\r\n        : ''\r\n    }\r\n  </p>\r\n\r\n  </div>\r\n  ${\r\n    event.extendedProps.location\r\n      ? `\r\n        <div class=\"mt-4 \">\r\n          <h5 class='mb-0 text-body-secondary'>Location</h5>\r\n          <p class=\"mb-0 mt-2\">${event.extendedProps.location}</p>\r\n        </div>\r\n      `\r\n      : ''\r\n  }\r\n  ${\r\n    event.schedules\r\n      ? `\r\n      <div class=\"mt-3\">\r\n        <h5 class='mb-0 text-body-secondary'>Schedule</h5>\r\n        <ul class=\"list-unstyled timeline mt-2 mb-0\">\r\n          ${event.schedules\r\n            .map(schedule => `<li>${schedule.title}</li>`)\r\n            .join('')}\r\n        </ul>\r\n      </div>\r\n      `\r\n      : ''\r\n  }\r\n  </div>\r\n</div>\r\n\r\n<div class=\"modal-footer d-flex justify-content-end px-card pt-0 border-top-0\">\r\n  <a href=\"#!\" class=\"btn btn-phoenix-secondary btn-sm\">\r\n    <span class=\"fas fa-pencil-alt fs-10 mr-2\"></span> Edit\r\n  </a>\r\n  <button class=\"btn btn-phoenix-danger btn-sm\" data-calendar-event-remove >\r\n    <span class=\"fa-solid fa-trash fs-9 mr-2\" data-fa-transform=\"shrink-2\"></span> Delete\r\n  </button>\r\n  <a href='#!' class=\"btn btn-primary btn-sm\">\r\n    See more details\r\n    <span class=\"fas fa-angle-right fs-10 ml-1\"></span>\r\n  </a>\r\n</div>\r\n`;\r\n\r\nexport default getTemplate;\r\n", "import { getData } from '../../utils';\r\nimport fullCalendar from '../fullcalendar';\r\nimport events from './events';\r\nimport getTemplate from './template';\r\n\r\n/*-----------------------------------------------\r\n|   Calendar\r\n-----------------------------------------------*/\r\nconst appCalendarInit = () => {\r\n  const Selectors = {\r\n    ACTIVE: '.active',\r\n    ADD_EVENT_FORM: '#addEventForm',\r\n    ADD_EVENT_MODAL: '#addEventModal',\r\n    CALENDAR: '#appCalendar',\r\n    CALENDAR_TITLE: '.calendar-title',\r\n    CALENDAR_DAY: '.calendar-day',\r\n    CALENDAR_DATE: '.calendar-date',\r\n    DATA_CALENDAR_VIEW: '[data-fc-view]',\r\n    DATA_EVENT: 'data-event',\r\n    DATA_VIEW_TITLE: '[data-view-title]',\r\n    EVENT_DETAILS_MODAL: '#eventDetailsModal',\r\n    EVENT_DETAILS_MODAL_CONTENT: '#eventDetailsModal .modal-content',\r\n    EVENT_START_DATE: '#addEventModal [name=\"startDate\"]',\r\n    INPUT_TITLE: '[name=\"title\"]'\r\n  };\r\n\r\n  const Events = {\r\n    CLICK: 'click',\r\n    SHOWN_BS_MODAL: 'shown.bs.modal',\r\n    SUBMIT: 'submit'\r\n  };\r\n\r\n  const DataKeys = {\r\n    EVENT: 'event',\r\n    FC_VIEW: 'fc-view'\r\n  };\r\n\r\n  const eventList = events.reduce(\r\n    (acc, val) =>\r\n      val.schedules ? acc.concat(val.schedules.concat(val)) : acc.concat(val),\r\n    []\r\n  );\r\n\r\n  const updateDay = day => {\r\n    const days = [\r\n      'Sunday',\r\n      'Monday',\r\n      'Tuesday',\r\n      'Wednesday',\r\n      'Thursday',\r\n      'Friday',\r\n      'Saturday'\r\n    ];\r\n    return days[day];\r\n  };\r\n\r\n  const setCurrentDate = () => {\r\n    const dateObj = new Date();\r\n    const month = dateObj.toLocaleString('en-US', { month: 'short' });\r\n    const date = dateObj.getDate(); // return date number\r\n    const day = dateObj.getDay(); // return week day number\r\n    const year = dateObj.getFullYear();\r\n    const newdate = `${date}  ${month},  ${year}`;\r\n    if (document.querySelector(Selectors.CALENDAR_DAY)) {\r\n      document.querySelector(Selectors.CALENDAR_DAY).textContent =\r\n        updateDay(day);\r\n    }\r\n    if (document.querySelector(Selectors.CALENDAR_DATE)) {\r\n      document.querySelector(Selectors.CALENDAR_DATE).textContent = newdate;\r\n    }\r\n  };\r\n  setCurrentDate();\r\n\r\n  const updateTitle = currentData => {\r\n    const { currentViewType } = currentData;\r\n    // week view\r\n    if (currentViewType === 'timeGridWeek') {\r\n      const weekStartsDate = currentData.dateProfile.currentRange.start;\r\n      const startingMonth = weekStartsDate.toLocaleString('en-US', {\r\n        month: 'short'\r\n      });\r\n      const startingDate = weekStartsDate.getDate();\r\n      const weekEndDate = currentData.dateProfile.currentRange.end;\r\n\r\n      const endingMonth = weekEndDate.toLocaleString('en-US', {\r\n        month: 'short'\r\n      });\r\n      const endingDate = weekEndDate.getDate();\r\n\r\n      document.querySelector(\r\n        Selectors.CALENDAR_TITLE\r\n      ).textContent = `${startingMonth} ${startingDate} - ${endingMonth} ${endingDate}`;\r\n    } else\r\n      document.querySelector(Selectors.CALENDAR_TITLE).textContent =\r\n        currentData.viewTitle;\r\n  };\r\n\r\n  const appCalendar = document.querySelector(Selectors.CALENDAR);\r\n  const addEventForm = document.querySelector(Selectors.ADD_EVENT_FORM);\r\n  const addEventModal = document.querySelector(Selectors.ADD_EVENT_MODAL);\r\n  const eventDetailsModal = document.querySelector(\r\n    Selectors.EVENT_DETAILS_MODAL\r\n  );\r\n\r\n  if (appCalendar) {\r\n    const calendar = fullCalendar.renderCalendar(appCalendar, {\r\n      headerToolbar: false,\r\n      dayMaxEvents: 3,\r\n      height: 800,\r\n      stickyHeaderDates: false,\r\n      views: {\r\n        week: {\r\n          eventLimit: 3\r\n        }\r\n      },\r\n      eventTimeFormat: {\r\n        hour: 'numeric',\r\n        minute: '2-digit',\r\n        omitZeroMinute: true,\r\n        meridiem: true\r\n      },\r\n      events: eventList,\r\n      eventClick: info => {\r\n        if (info.event.url) {\r\n          window.open(info.event.url, '_blank');\r\n          info.jsEvent.preventDefault();\r\n        } else {\r\n          const template = getTemplate(info.event);\r\n          document.querySelector(\r\n            Selectors.EVENT_DETAILS_MODAL_CONTENT\r\n          ).innerHTML = template;\r\n          const modal = new window.bootstrap.Modal(eventDetailsModal);\r\n          modal.show();\r\n        }\r\n      },\r\n      dateClick(info) {\r\n        const modal = new window.bootstrap.Modal(addEventModal);\r\n        modal.show();\r\n        /* eslint-disable-next-line */\r\n        const flatpickr = document.querySelector(Selectors.EVENT_START_DATE)._flatpickr;\r\n        flatpickr.setDate([info.dateStr]);\r\n      }\r\n    });\r\n\r\n    updateTitle(calendar.currentData);\r\n\r\n    document.addEventListener('click', e => {\r\n      // handle prev and next button click\r\n      if (\r\n        e.target.hasAttribute(Selectors.DATA_EVENT) ||\r\n        e.target.parentNode.hasAttribute(Selectors.DATA_EVENT)\r\n      ) {\r\n        const el = e.target.hasAttribute(Selectors.DATA_EVENT)\r\n          ? e.target\r\n          : e.target.parentNode;\r\n        const type = getData(el, DataKeys.EVENT);\r\n        switch (type) {\r\n          case 'prev':\r\n            calendar.prev();\r\n            updateTitle(calendar.currentData);\r\n            break;\r\n          case 'next':\r\n            calendar.next();\r\n            updateTitle(calendar.currentData);\r\n            break;\r\n          case 'today':\r\n            calendar.today();\r\n            updateTitle(calendar.currentData);\r\n            break;\r\n          default:\r\n            calendar.today();\r\n            updateTitle(calendar.currentData);\r\n            break;\r\n        }\r\n      }\r\n\r\n      // handle fc-view\r\n      if (e.target.hasAttribute('data-fc-view')) {\r\n        const el = e.target;\r\n        calendar.changeView(getData(el, DataKeys.FC_VIEW));\r\n        updateTitle(calendar.currentData);\r\n        document\r\n          .querySelectorAll(Selectors.DATA_CALENDAR_VIEW)\r\n          .forEach(item => {\r\n            if (item === e.target) {\r\n              item.classList.add('active-view');\r\n            } else {\r\n              item.classList.remove('active-view');\r\n            }\r\n          });\r\n      }\r\n    });\r\n\r\n    if (addEventForm) {\r\n      addEventForm.addEventListener(Events.SUBMIT, e => {\r\n        e.preventDefault();\r\n        const { title, startDate, endDate, label, description, allDay } =\r\n          e.target;\r\n        calendar.addEvent({\r\n          title: title.value,\r\n          start: startDate.value,\r\n          end: endDate.value ? endDate.value : null,\r\n          allDay: allDay.checked,\r\n          className: `text-${label.value}`,\r\n          description: description.value\r\n        });\r\n        e.target.reset();\r\n        window.bootstrap.Modal.getInstance(addEventModal).hide();\r\n      });\r\n    }\r\n\r\n    if (addEventModal) {\r\n      addEventModal.addEventListener(\r\n        Events.SHOWN_BS_MODAL,\r\n        ({ currentTarget }) => {\r\n          currentTarget.querySelector(Selectors.INPUT_TITLE)?.focus();\r\n        }\r\n      );\r\n    }\r\n  }\r\n};\r\n\r\nexport default appCalendarInit;\r\n", "import appCalendarInit from '../theme/calendar/app-calendar';\r\n\r\nconst { docReady } = window.phoenix.utils;\r\n\r\ndocReady(appCalendarInit);\r\n"], "names": [], "mappings": ";;;;;EAAA;AAqBA;EACO,MAAM,QAAQ,GAAG,GAAG,IAAI;EAC/B,EAAE,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC;EACjD,IAAI,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,EAAE;EAC5B,GAAG,CAAC;EACJ,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,CAAC,CAAC;AACF;EACO,MAAM,OAAO,GAAG,CAAC,EAAE,EAAE,IAAI,KAAK;EACrC,EAAE,IAAI;EACN,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAClD,GAAG,CAAC,OAAO,CAAC,EAAE;EACd,IAAI,OAAO,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACtC,GAAG;EACH,CAAC;;ECnCD;EACA;AACA;EACA;EACA,MAAM,cAAc,GAAG,CAAC,EAAE,EAAE,MAAM,KAAK;EACvC,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;AAC7B;EACA,EAAE,MAAM,OAAO,GAAG,KAAK;EACvB,IAAI;EACJ,MAAM,WAAW,EAAE,cAAc;EACjC,MAAM,QAAQ,EAAE,IAAI;EACpB,MAAM,SAAS,EAAE,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,KAAK,CAAC;EACnE,MAAM,aAAa,EAAE;EACrB,QAAQ,IAAI,EAAE,iBAAiB;EAC/B,QAAQ,MAAM,EAAE,OAAO;EACvB,QAAQ,KAAK,EAAE,uCAAuC;EACtD,OAAO;EACP,MAAM,UAAU,EAAE;EAClB,QAAQ,KAAK,EAAE,OAAO;EACtB,QAAQ,IAAI,EAAE,MAAM;EACpB,QAAQ,GAAG,EAAE,KAAK;EAClB,OAAO;EACP,KAAK;EACL,IAAI,MAAM;EACV,GAAG,CAAC;EACJ,EAAE,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;EACjE,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC;EACpB,EAAE,QAAQ;EACV,KAAK,aAAa,CAAC,yBAAyB,CAAC;EAC7C,MAAM,gBAAgB,CAAC,wBAAwB,EAAE,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;EAC9E,EAAE,OAAO,QAAQ,CAAC;EAClB,CAAC,CAAC;AACF;EACO,MAAM,gBAAgB,GAAG,MAAM;EACtC,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC3C;EACA,EAAE,MAAM,SAAS,GAAG,QAAQ,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;EACjE,EAAE,SAAS,CAAC,OAAO,CAAC,IAAI,IAAI;EAC5B,IAAI,MAAM,OAAO,GAAG,OAAO,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;EAC9C,IAAI,cAAc,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;EAClC,GAAG,CAAC,CAAC;EACL,CAAC,CAAC;AACF;EACA,MAAM,YAAY,GAAG;EACrB,EAAE,cAAc;EAChB,EAAE,gBAAgB;EAClB,CAAC;;EC9CD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC;EACzB,MAAM,UAAU,GAAG,KAAK,IAAI,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EACjD,MAAM,YAAY,GAAG,KAAK,IAAI,KAAK,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EACnD,MAAM,SAAS,GAAG,KAAK,IAAI,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EACrE,MAAM,SAAS,GAAG,KAAK,IAAI,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;EAChE,MAAM,WAAW,GAAG,KAAK,IAAI,KAAK,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;EACpD,MAAM,MAAM,GAAG;EACf,EAAE;EACF,IAAI,KAAK,EAAE,WAAW;EACtB,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC;EACvD,IAAI,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC;EACrD,IAAI,WAAW;EACf,MAAM,qZAAqZ;EAC3Z,IAAI,SAAS,EAAE,cAAc;EAC7B,IAAI,QAAQ;EACZ,MAAM,sFAAsF;EAC5F,IAAI,SAAS,EAAE,mBAAmB;EAClC,GAAG;EACH,EAAE;EACF,IAAI,KAAK,EAAE,CAAC,0BAA0B,CAAC;EACvC,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC;EAC9C,IAAI,WAAW;EACf,MAAM,yRAAyR;EAC/R,IAAI,SAAS,EAAE,cAAc;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,KAAK,EAAE,YAAY;EACvB,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;EACzD,IAAI,WAAW;EACf,MAAM,8SAA8S;EACpT,IAAI,SAAS,EAAE,cAAc;EAC7B;EACA,IAAI,SAAS,EAAE;EACf,MAAM;EACN,QAAQ,KAAK,EAAE,WAAW;EAC1B,QAAQ,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC;EACtE,QAAQ,WAAW;EACnB,UAAU,2FAA2F;EACrG,QAAQ,SAAS,EAAE,eAAe;EAClC,OAAO;EACP,MAAM;EACN,QAAQ,KAAK,EAAE,OAAO;EACtB,QAAQ,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC;EACtE,QAAQ,WAAW,EAAE,0DAA0D;EAC/E,QAAQ,SAAS,EAAE,WAAW;EAC9B,OAAO;EACP,MAAM;EACN,QAAQ,KAAK,EAAE,SAAS;EACxB,QAAQ,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC;EACtE,QAAQ,WAAW,EAAE,yCAAyC;EAC9D,QAAQ,SAAS,EAAE,cAAc;EACjC,OAAO;EACP,MAAM;EACN,QAAQ,KAAK,EAAE,QAAQ;EACvB,QAAQ,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC;EACtE,QAAQ,WAAW,EAAE,0DAA0D;EAC/E,QAAQ,SAAS,EAAE,cAAc;EACjC,OAAO;EACP,KAAK;EACL,GAAG;EACH,EAAE;EACF,IAAI,KAAK,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,kBAAkB,CAAC;EACtD,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC;EACvD,IAAI,WAAW,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,+GAA+G,CAAC;EACzJ,IAAI,GAAG,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC;EACrD,IAAI,SAAS,EAAE,cAAc;EAC7B,IAAI,MAAM,EAAE,IAAI;EAChB,GAAG;EACH,EAAE;EACF,IAAI,KAAK,EAAE,SAAS;EACpB,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC;EACvD,IAAI,WAAW;EACf,MAAM,6FAA6F;EACnG,IAAI,SAAS,EAAE,WAAW;EAC1B,GAAG;EACH,EAAE;EACF,IAAI,KAAK,EAAE,SAAS;EACpB,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,YAAY,CAAC;EACvD,IAAI,SAAS,EAAE,WAAW;EAC1B,IAAI,WAAW;EACf,MAAM,+NAA+N;EACrO,GAAG;EACH,EAAE;EACF,IAAI,KAAK,EAAE,gBAAgB;EAC3B,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC;EAC9C,IAAI,WAAW;EACf,MAAM,uFAAuF;EAC7F,IAAI,SAAS,EAAE,cAAc;EAC7B,IAAI,GAAG,EAAE,mBAAmB;EAC5B,GAAG;EACH,EAAE;EACF,IAAI,KAAK,EAAE,aAAa;EACxB,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,CAAC;EAC9C,IAAI,WAAW;EACf,MAAM,+OAA+O;EACrP,IAAI,SAAS,EAAE,aAAa;EAC5B,GAAG;EACH,EAAE;EACF,IAAI,KAAK,EAAE,gBAAgB;EAC3B,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC;EAC3C,IAAI,WAAW,EAAE,0DAA0D;EAC3E,IAAI,SAAS,EAAE,cAAc;EAC7B,GAAG;EACH,EAAE;EACF,IAAI,KAAK,EAAE,kBAAkB;EAC7B,IAAI,GAAG,EAAE,oBAAoB;EAC7B,IAAI,KAAK,EAAE,CAAC,EAAE,WAAW,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC;EAC3C,IAAI,WAAW;EACf,MAAM,uNAAuN;EAC7N,IAAI,SAAS,EAAE,cAAc;EAC7B,GAAG;EACH,CAAC;;EC/GD,MAAM,WAAW,GAAG,KAAK,IAAI,CAAC;AAC9B;AACA;AACA,qDAAqD,EAAE,KAAK,CAAC,KAAK,CAAC;AACnE,IAAI;AACJ,MAAM,KAAK,CAAC,aAAa,CAAC,SAAS;AACnC,UAAU,CAAC;AACX,wBAAwB,EAAE,KAAK,CAAC,aAAa,CAAC,SAAS,CAAC;AACxD,UAAU,CAAC;AACX,UAAU,EAAE;AACZ,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE;AACF,IAAI,KAAK,CAAC,aAAa,CAAC,WAAW;AACnC,QAAQ,CAAC;AACT;AACA;AACA;AACA,UAAU,EAAE,KAAK,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC9E;AACA;AACA,IAAI,CAAC;AACL,QAAQ,EAAE;AACV,GAAG;AACH,mBAAmB;AACnB,IAAI,KAAK,CAAC,aAAa,CAAC,QAAQ,GAAG,uCAAuC,GAAG,EAAE;AAC/E,GAAG;AACH;AACA;AACA,IAAI;AACJ,MAAM,MAAM,CAAC,KAAK;AAClB,MAAM,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,4BAA4B,CAAC;AACpE,KAAK;AACL,IAAI;AACJ,MAAM,KAAK,CAAC,GAAG;AACf,UAAU,CAAC,EAAE;AACb,YAAY,MAAM,CAAC,KAAK;AACxB,YAAY,MAAM;AAClB,eAAe,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;AAC/B,eAAe,QAAQ,CAAC,CAAC,EAAE,KAAK,CAAC;AACjC,eAAe,MAAM,CAAC,4BAA4B,CAAC;AACnD,WAAW,CAAC;AACZ,UAAU,EAAE;AACZ,KAAK;AACL;AACA;AACA;AACA,EAAE;AACF,IAAI,KAAK,CAAC,aAAa,CAAC,QAAQ;AAChC,QAAQ,CAAC;AACT;AACA;AACA,+BAA+B,EAAE,KAAK,CAAC,aAAa,CAAC,QAAQ,CAAC;AAC9D;AACA,MAAM,CAAC;AACP,QAAQ,EAAE;AACV,GAAG;AACH,EAAE;AACF,IAAI,KAAK,CAAC,SAAS;AACnB,QAAQ,CAAC;AACT;AACA;AACA;AACA,UAAU,EAAE,KAAK,CAAC,SAAS;AAC3B,aAAa,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC1D,aAAa,IAAI,CAAC,EAAE,CAAC,CAAC;AACtB;AACA;AACA,MAAM,CAAC;AACP,QAAQ,EAAE;AACV,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;ECxFD;EACA;EACA;EACA,MAAM,eAAe,GAAG,MAAM;EAC9B,EAAE,MAAM,SAAS,GAAG;EACpB,IAAI,MAAM,EAAE,SAAS;EACrB,IAAI,cAAc,EAAE,eAAe;EACnC,IAAI,eAAe,EAAE,gBAAgB;EACrC,IAAI,QAAQ,EAAE,cAAc;EAC5B,IAAI,cAAc,EAAE,iBAAiB;EACrC,IAAI,YAAY,EAAE,eAAe;EACjC,IAAI,aAAa,EAAE,gBAAgB;EACnC,IAAI,kBAAkB,EAAE,gBAAgB;EACxC,IAAI,UAAU,EAAE,YAAY;EAC5B,IAAI,eAAe,EAAE,mBAAmB;EACxC,IAAI,mBAAmB,EAAE,oBAAoB;EAC7C,IAAI,2BAA2B,EAAE,mCAAmC;EACpE,IAAI,gBAAgB,EAAE,mCAAmC;EACzD,IAAI,WAAW,EAAE,gBAAgB;EACjC,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,KAAK,EAAE,OAAO;EAClB,IAAI,cAAc,EAAE,gBAAgB;EACpC,IAAI,MAAM,EAAE,QAAQ;EACpB,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,QAAQ,GAAG;EACnB,IAAI,KAAK,EAAE,OAAO;EAClB,IAAI,OAAO,EAAE,SAAS;EACtB,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM;EACjC,IAAI,CAAC,GAAG,EAAE,GAAG;EACb,MAAM,GAAG,CAAC,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC;EAC7E,IAAI,EAAE;EACN,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,SAAS,GAAG,GAAG,IAAI;EAC3B,IAAI,MAAM,IAAI,GAAG;EACjB,MAAM,QAAQ;EACd,MAAM,QAAQ;EACd,MAAM,SAAS;EACf,MAAM,WAAW;EACjB,MAAM,UAAU;EAChB,MAAM,QAAQ;EACd,MAAM,UAAU;EAChB,KAAK,CAAC;EACN,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;EACrB,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,cAAc,GAAG,MAAM;EAC/B,IAAI,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;EAC/B,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAC;EACtE,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;EACnC,IAAI,MAAM,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;EACjC,IAAI,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;EACvC,IAAI,MAAM,OAAO,GAAG,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;EAClD,IAAI,IAAI,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;EACxD,MAAM,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,WAAW;EAChE,QAAQ,SAAS,CAAC,GAAG,CAAC,CAAC;EACvB,KAAK;EACL,IAAI,IAAI,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;EACzD,MAAM,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,WAAW,GAAG,OAAO,CAAC;EAC5E,KAAK;EACL,GAAG,CAAC;EACJ,EAAE,cAAc,EAAE,CAAC;AACnB;EACA,EAAE,MAAM,WAAW,GAAG,WAAW,IAAI;EACrC,IAAI,MAAM,EAAE,eAAe,EAAE,GAAG,WAAW,CAAC;EAC5C;EACA,IAAI,IAAI,eAAe,KAAK,cAAc,EAAE;EAC5C,MAAM,MAAM,cAAc,GAAG,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,KAAK,CAAC;EACxE,MAAM,MAAM,aAAa,GAAG,cAAc,CAAC,cAAc,CAAC,OAAO,EAAE;EACnE,QAAQ,KAAK,EAAE,OAAO;EACtB,OAAO,CAAC,CAAC;EACT,MAAM,MAAM,YAAY,GAAG,cAAc,CAAC,OAAO,EAAE,CAAC;EACpD,MAAM,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC;AACnE;EACA,MAAM,MAAM,WAAW,GAAG,WAAW,CAAC,cAAc,CAAC,OAAO,EAAE;EAC9D,QAAQ,KAAK,EAAE,OAAO;EACtB,OAAO,CAAC,CAAC;EACT,MAAM,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,EAAE,CAAC;AAC/C;EACA,MAAM,QAAQ,CAAC,aAAa;EAC5B,QAAQ,SAAS,CAAC,cAAc;EAChC,OAAO,CAAC,WAAW,GAAG,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;EACxF,KAAK;EACL,MAAM,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,WAAW;EAClE,QAAQ,WAAW,CAAC,SAAS,CAAC;EAC9B,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,WAAW,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;EACjE,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC;EACxE,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC;EAC1E,EAAE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa;EAClD,IAAI,SAAS,CAAC,mBAAmB;EACjC,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,WAAW,EAAE;EACnB,IAAI,MAAM,QAAQ,GAAG,YAAY,CAAC,cAAc,CAAC,WAAW,EAAE;EAC9D,MAAM,aAAa,EAAE,KAAK;EAC1B,MAAM,YAAY,EAAE,CAAC;EACrB,MAAM,MAAM,EAAE,GAAG;EACjB,MAAM,iBAAiB,EAAE,KAAK;EAC9B,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE;EACd,UAAU,UAAU,EAAE,CAAC;EACvB,SAAS;EACT,OAAO;EACP,MAAM,eAAe,EAAE;EACvB,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,MAAM,EAAE,SAAS;EACzB,QAAQ,cAAc,EAAE,IAAI;EAC5B,QAAQ,QAAQ,EAAE,IAAI;EACtB,OAAO;EACP,MAAM,MAAM,EAAE,SAAS;EACvB,MAAM,UAAU,EAAE,IAAI,IAAI;EAC1B,QAAQ,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;EAC5B,UAAU,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;EAChD,UAAU,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;EACxC,SAAS,MAAM;EACf,UAAU,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;EACnD,UAAU,QAAQ,CAAC,aAAa;EAChC,YAAY,SAAS,CAAC,2BAA2B;EACjD,WAAW,CAAC,SAAS,GAAG,QAAQ,CAAC;EACjC,UAAU,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;EACtE,UAAU,KAAK,CAAC,IAAI,EAAE,CAAC;EACvB,SAAS;EACT,OAAO;EACP,MAAM,SAAS,CAAC,IAAI,EAAE;EACtB,QAAQ,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;EAChE,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;EACrB;EACA,QAAQ,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC,UAAU,CAAC;EACxF,QAAQ,SAAS,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;EAC1C,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;AACtC;EACA,IAAI,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,IAAI;EAC5C;EACA,MAAM;EACN,QAAQ,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC;EACnD,QAAQ,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC;EAC9D,QAAQ;EACR,QAAQ,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,SAAS,CAAC,UAAU,CAAC;EAC9D,YAAY,CAAC,CAAC,MAAM;EACpB,YAAY,CAAC,CAAC,MAAM,CAAC,UAAU,CAAC;EAChC,QAAQ,MAAM,IAAI,GAAG,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;EACjD,QAAQ,QAAQ,IAAI;EACpB,UAAU,KAAK,MAAM;EACrB,YAAY,QAAQ,CAAC,IAAI,EAAE,CAAC;EAC5B,YAAY,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAC9C,YAAY,MAAM;EAClB,UAAU,KAAK,MAAM;EACrB,YAAY,QAAQ,CAAC,IAAI,EAAE,CAAC;EAC5B,YAAY,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAC9C,YAAY,MAAM;EAClB,UAAU,KAAK,OAAO;EACtB,YAAY,QAAQ,CAAC,KAAK,EAAE,CAAC;EAC7B,YAAY,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAC9C,YAAY,MAAM;EAClB,UAAU;EACV,YAAY,QAAQ,CAAC,KAAK,EAAE,CAAC;EAC7B,YAAY,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAC9C,YAAY,MAAM;EAClB,SAAS;EACT,OAAO;AACP;EACA;EACA,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,YAAY,CAAC,cAAc,CAAC,EAAE;EACjD,QAAQ,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;EAC5B,QAAQ,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EAC3D,QAAQ,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;EAC1C,QAAQ,QAAQ;EAChB,WAAW,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,CAAC;EACzD,WAAW,OAAO,CAAC,IAAI,IAAI;EAC3B,YAAY,IAAI,IAAI,KAAK,CAAC,CAAC,MAAM,EAAE;EACnC,cAAc,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;EAChD,aAAa,MAAM;EACnB,cAAc,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;EACnD,aAAa;EACb,WAAW,CAAC,CAAC;EACb,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,IAAI,YAAY,EAAE;EACtB,MAAM,YAAY,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI;EACxD,QAAQ,CAAC,CAAC,cAAc,EAAE,CAAC;EAC3B,QAAQ,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE;EACvE,UAAU,CAAC,CAAC,MAAM,CAAC;EACnB,QAAQ,QAAQ,CAAC,QAAQ,CAAC;EAC1B,UAAU,KAAK,EAAE,KAAK,CAAC,KAAK;EAC5B,UAAU,KAAK,EAAE,SAAS,CAAC,KAAK;EAChC,UAAU,GAAG,EAAE,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,IAAI;EACnD,UAAU,MAAM,EAAE,MAAM,CAAC,OAAO;EAChC,UAAU,SAAS,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;EAC1C,UAAU,WAAW,EAAE,WAAW,CAAC,KAAK;EACxC,SAAS,CAAC,CAAC;EACX,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;EACzB,QAAQ,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;EACjE,OAAO,CAAC,CAAC;EACT,KAAK;AACL;EACA,IAAI,IAAI,aAAa,EAAE;EACvB,MAAM,aAAa,CAAC,gBAAgB;EACpC,QAAQ,MAAM,CAAC,cAAc;EAC7B,QAAQ,CAAC,EAAE,aAAa,EAAE,KAAK;EAC/B,UAAU,aAAa,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,CAAC;EACtE,SAAS;EACT,OAAO,CAAC;EACR,KAAK;EACL,GAAG;EACH,CAAC;;EC1ND,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1C;EACA,QAAQ,CAAC,eAAe,CAAC;;;;;;"}