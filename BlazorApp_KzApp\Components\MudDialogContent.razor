@code {

    [Parameter]
    public RenderFragment? ChildContent { get; set; }
    [Parameter]
    public RenderFragment? MudDialogActions { get; set; }
    [Parameter]
    public string ContentClass { get; set; } = "";
    [Parameter]
    public string ActionsClass { get; set; } = "";
}


<MudDialog>
    <DialogContent>
        <div class="card border-0 bg-body mb-2">
            <div class="card-footer border-0 p-0 @ContentClass">
                @ChildContent
            </div>
        </div>
    </DialogContent>
    <DialogActions>
        <div class="px-3 pb-2 @ActionsClass">
            @MudDialogActions
        </div>
    </DialogActions>
</MudDialog>