{"version": 3, "sources": ["theme/_variables.scss"], "names": [], "mappings": "AAAA,oBAAA", "file": "user.css", "sourcesContent": ["/* prettier-ignore */\r\n@use 'sass:math';\r\n@import 'colors';\r\n\r\n// Options\r\n//\r\n// Quickly modify global styling by enabling or disabling optional features.\r\n$enable-shadows: true !default;\r\n$enable-negative-margins: true !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Spacing\r\n//----------------------------------------------\r\n$spacer: 1rem !default;\r\n$spacers: (\r\n  0: 0,\r\n  1: $spacer * 0.25,\r\n  2: $spacer * 0.5,\r\n  3: $spacer,\r\n  4: $spacer * 1.5,\r\n  5: $spacer * 2,\r\n  6: $spacer * 2.5,\r\n  7: $spacer * 3,\r\n  8: $spacer * 3.5,\r\n  9: $spacer * 4,\r\n  10: $spacer * 4.5,\r\n  11: $spacer * 5,\r\n  12: $spacer * 6,\r\n  13: $spacer * 6.5,\r\n  14: $spacer * 7,\r\n  15: $spacer * 7.5\r\n) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Link\r\n//-----------------------------------------------*/\r\n$link-decoration: none !default;\r\n$link-hover-decoration: underline !default;\r\n$link-hover-color: $primary-text-emphasis !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Grid Breakpoints\r\n//-----------------------------------------------*/\r\n$grid-breakpoints: (\r\n  xs: 0,\r\n  sm: 576px,\r\n  md: 768px,\r\n  lg: 992px,\r\n  xl: 1200px,\r\n  xxl: 1540px\r\n) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Grid containers\r\n//-----------------------------------------------*/\r\n$container-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1184px,\r\n  xxl: 1678px\r\n) !default;\r\n\r\n// phoenix specific variable\r\n$container-small-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1216px\r\n) !default;\r\n\r\n$container-medium-max-widths: (\r\n  sm: 540px,\r\n  md: 720px,\r\n  lg: 960px,\r\n  xl: 1216px,\r\n  xxl: 1400px\r\n) !default;\r\n\r\n$bg-opacities: (\r\n  10: 0.1,\r\n  25: 0.25,\r\n  50: 0.5,\r\n  75: 0.75,\r\n  85: 0.85,\r\n  // new\r\n  100: 1\r\n) !default;\r\n\r\n$text-opacities: (\r\n  25: 0.25,\r\n  50: 0.5,\r\n  75: 0.75,\r\n  85: 0.85,\r\n  100: 1\r\n) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Grid Collumn\r\n//-----------------------------------------------*/\r\n$grid-gutter-width: 2rem !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Border radious\r\n//-----------------------------------------------*/\r\n$border-radius: 0.375rem !default;\r\n$border-radius-sm: 0.25rem !default;\r\n$border-radius-lg: 0.5rem !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Shadow\r\n//-----------------------------------------------*/\r\n$box-shadow: 0px 2px 4px -2px rgba(36, 40, 46, 0.08) !default;\r\n\r\n// phoenix specific variable\r\n$box-shadow-gray-400: 0px 4px 8px -6px rgba(36, 40, 46, 0.03),\r\n  0px 16px 8px -4px rgba(36, 40, 46, 0.03) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Fonts Families\r\n//-----------------------------------------------*/\r\n$font-family-sans-serif: 'Nunito Sans', -apple-system, BlinkMacSystemFont,\r\n  'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif, 'Apple Color Emoji',\r\n  'Segoe UI Emoji', 'Segoe UI Symbol' !default;\r\n$font-family-monospace: 'SFMono-Regular', Menlo, Monaco, Consolas,\r\n  'Liberation Mono', 'Courier New', monospace !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Fonts\r\n//-----------------------------------------------*/\r\n$type-scale: 1.25 !default; // phoenix spesific variable\r\n\r\n$font-size-base: 1rem !default;\r\n$font-size-sm: $font-size-base * 0.875 !default;\r\n$font-size-lg: $font-size-base * 1.2 !default;\r\n\r\n$font-sizes: (\r\n  1: pow($type-scale, 7) * $font-size-base,\r\n  // 76.29px\r\n  2: pow($type-scale, 6) * $font-size-base,\r\n  // 61.04px\r\n  3: pow($type-scale, 5) * $font-size-base,\r\n  // 48.83px\r\n  4: pow($type-scale, 4) * $font-size-base,\r\n  // 39.06px\r\n  5: pow($type-scale, 3) * $font-size-base,\r\n  // 31.25px\r\n  6: pow($type-scale, 2) * $font-size-base,\r\n  //25px\r\n  7: pow($type-scale, 1) * $font-size-base,\r\n  //20px\r\n  8: $font-size-base,\r\n  // 16px\r\n  9: math.div(1, $type-scale) * $font-size-base,\r\n  // 12.80px\r\n  10: math.div(1, pow($type-scale, 2)) * $font-size-base,\r\n  // 10.24px\r\n  11: math.div(1, pow($type-scale, 3)) * $font-size-base,\r\n  // 8.19px\r\n) !default;\r\n\r\n$font-weight-thin: 100 !default;\r\n$font-weight-lighter: 200 !default;\r\n$font-weight-light: 300 !default;\r\n$font-weight-normal: 400 !default;\r\n$font-weight-medium: 500 !default;\r\n$font-weight-semibold: 600 !default;\r\n$font-weight-bold: 700 !default;\r\n$font-weight-bolder: 800 !default;\r\n$font-weight-black: 900 !default;\r\n\r\n$line-height-base: 1.49 !default;\r\n$line-height-sm: 1.2 !default;\r\n$line-height-lg: 1.4 !default;\r\n\r\n$h1-font-size: map_get($font-sizes, 4) !default;\r\n$h2-font-size: map_get($font-sizes, 5) !default;\r\n$h3-font-size: map_get($font-sizes, 6) !default;\r\n$h4-font-size: map_get($font-sizes, 7) !default;\r\n$h5-font-size: map_get($font-sizes, 8) !default;\r\n$h6-font-size: map_get($font-sizes, 9) !default;\r\n\r\n// scss-docs-start headings-variables\r\n$headings-margin-bottom: map-get($spacers, 1) !default;\r\n// $headings-font-family: $font-family-sans-serif !default;\r\n$headings-font-weight: $font-weight-bold !default;\r\n$headings-line-height: 1 !default;\r\n$headings-color: var(--#{$prefix}emphasis-color) !default;\r\n// scss-docs-end headings-variables\r\n\r\n// scss-docs-start display-headings\r\n$display-font-sizes: (\r\n  1: map_get($font-sizes, 1),\r\n  2: map_get($font-sizes, 2),\r\n  3: map_get($font-sizes, 3),\r\n  4: map_get($font-sizes, 4),\r\n  5: map_get($font-sizes, 5),\r\n  6: map_get($font-sizes, 6)\r\n) !default;\r\n\r\n$display-font-weight: $font-weight-normal !default;\r\n// scss-docs-end display-headings\r\n\r\n// scss-docs-start type-variables\r\n$lead-font-size: map-get($font-sizes, 7) !default;\r\n$lead-font-weight: $font-weight-semibold !default;\r\n\r\n$small-font-size: 75% !default;\r\n\r\n$blockquote-font-size: $font-size-lg !default;\r\n\r\n$hr-color: var(--#{$prefix}border-color-translucent) !default;\r\n$hr-opacity: 1 !default;\r\n// scss-docs-end type-variables\r\n\r\n//*-----------------------------------------------\r\n//|   Tables\r\n//-----------------------------------------------*/\r\n$table-color: var(--#{$prefix}body-color) !default;\r\n$table-cell-padding-y: 1rem !default;\r\n$table-bg: transparent !default;\r\n$table-th-font-weight: $font-weight-bolder !default;\r\n$table-active-bg: rgba(var(--#{$prefix}quaternary-bg-rgb), 0.4) !default;\r\n\r\n$table-striped-color: var(--#{$prefix}body-highlight-color) !default;\r\n$table-striped-bg: rgba(var(--#{$prefix}quaternary-bg-rgb), 0.2) !default;\r\n\r\n$table-hover-color: var(--#{$prefix}emphasis-color) !default;\r\n$table-hover-bg: rgba(var(--#{$prefix}primary-dark-rgb), 0.07) !default;\r\n\r\n$table-border-color: var(--#{$prefix}border-color-translucent);\r\n\r\n$table-striped-order: even !default;\r\n\r\n$table-group-separator-color: inherit !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Buttons and Forms\r\n//-----------------------------------------------*/\r\n\r\n// scss-docs-start input-btn-variables\r\n$input-btn-padding-y: 0.625rem !default;\r\n$input-btn-padding-x: 1.5rem !default;\r\n\r\n$input-btn-focus-blur: 0 !default;\r\n\r\n$input-btn-padding-y-sm: 0.5rem !default;\r\n$input-btn-padding-x-sm: 1rem !default;\r\n$input-btn-font-size-sm: map-get($font-sizes, 9) !default;\r\n\r\n$input-btn-padding-y-lg: 0.875rem !default;\r\n$input-btn-padding-x-lg: 1.5rem !default;\r\n$input-btn-font-size-lg: map-get($font-sizes, 8) !default;\r\n// scss-docs-end input-btn-variables\r\n\r\n// buttons\r\n//\r\n\r\n// scss-docs-start btn-variables\r\n$btn-line-height: 1.2 !default;\r\n\r\n$input-btn-font-size: map-get($font-sizes, 9) !default;\r\n$btn-font-weight: $font-weight-bold !default;\r\n\r\n$btn-box-shadow: initial !default;\r\n\r\n$btn-focus-width: 0 !default;\r\n$btn-focus-box-shadow: 0 0 0 0.25rem transparent !default;\r\n$btn-disabled-opacity: 0.3 !default;\r\n$btn-active-box-shadow: initial !default;\r\n\r\n$btn-link-disabled-color: $gray-400 !default;\r\n\r\n$btn-border-radius-sm: $border-radius !default;\r\n$btn-border-radius-lg: $border-radius !default;\r\n// scss-docs-end btn-variables\r\n\r\n// Forms\r\n//\r\n// scss-docs-start form-label-variables\r\n$form-label-font-size: map-get($font-sizes, 10) !default;\r\n$form-label-font-weight: $font-weight-bold !default;\r\n$form-label-color: var(--#{$prefix}tertiary-color) !default;\r\n// scss-docs-end form-label-variables\r\n\r\n// scss-docs-start form-input-variables\r\n$input-padding-y: 0.5rem !default;\r\n$input-padding-x: 1rem !default;\r\n$input-font-weight: $font-weight-semibold !default;\r\n\r\n$input-padding-y-sm: 0.375rem !default;\r\n\r\n$input-padding-y-lg: 0.75rem !default;\r\n$input-padding-x-lg: 1rem !default;\r\n\r\n$input-bg: var(--#{$prefix}emphasis-bg) !default;\r\n$input-disabled-color: var(--#{$prefix}quaternary-color) !default;\r\n$input-disabled-bg: rgba(var(--#{$prefix}gray-100-rgb), 0.7) !default;\r\n\r\n$input-color: var(--#{$prefix}body-color) !default;\r\n$input-border-color: var(--#{$prefix}border-color) !default;\r\n$input-box-shadow: inset 0 1px 2px transparent !default;\r\n\r\n$input-border-radius-sm: $border-radius !default;\r\n$input-border-radius-lg: $border-radius !default;\r\n\r\n$input-focus-border-color: $primary !default;\r\n$input-focus-box-shadow: inset 0 0 0 30px var(--#{$prefix}emphasis-bg),\r\n  0 0 0 0.25rem rgba(var(--#{$prefix}primary-rgb), 0.25) !default;\r\n\r\n$input-placeholder-color: rgba(\r\n  var(--#{$prefix}quaternary-color-rgb),\r\n  0.8\r\n) !default; // scss-docs-end form-input-variables\r\n\r\n// scss-docs-start form-check-variables\r\n$form-check-margin-bottom: 0.34375rem !default;\r\n$form-check-label-color: $form-label-color !default;\r\n$form-check-input-border: 1px solid var(--#{$prefix}border-color) !default;\r\n\r\n// scss-docs-end form-check-variables\r\n\r\n// scss-docs-start form-switch-variables\r\n$form-switch-color: $white !default;\r\n$form-switch-focus-color: $white !default;\r\n// scss-docs-end form-switch-variables\r\n\r\n// scss-docs-start input-group-variables\r\n$input-group-addon-bg: var(--#{$prefix}input-group-addon-bg-global) !default;\r\n// scss-docs-end input-group-variables\r\n\r\n// scss-docs-start form-select-variables\r\n$form-select-indicator-padding: 2.5rem !default;\r\n$form-select-disabled-bg: $input-disabled-bg !default;\r\n$form-select-box-shadow: $input-box-shadow !default;\r\n$form-select-indicator: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwIiBoZWlnaHQ9IjE1MCIgdmlld0JveD0iMCAwIDE1MCAxNTAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik03NS4zNDggMTI3LjE5MkM3Mi40MzgxIDEyNy4xOTIgNjkuODUxNCAxMjYuMjIyIDY3LjkxMTUgMTI0LjI4Mkw1LjgzMjE1IDYyLjIwMjNDMS42Mjg4NyA1OC4zMjIzIDEuNjI4ODcgNTEuNTMyNCA1LjgzMjE1IDQ3LjY1MjVDOS43MTIxMSA0My40NDkyIDE2LjUwMiA0My40NDkyIDIwLjM4MiA0Ny42NTI1TDc1LjM0OCAxMDIuMjk1TDEyOS45OTEgNDcuNjUyNUMxMzMuODcxIDQzLjQ0OTIgMTQwLjY2MSA0My40NDkyIDE0NC41NDEgNDcuNjUyNUMxNDguNzQ0IDUxLjUzMjQgMTQ4Ljc0NCA1OC4zMjIzIDE0NC41NDEgNjIuMjAyM0w4Mi40NjEzIDEyNC4yODJDODAuNTIxMyAxMjYuMjIyIDc3LjkzNDcgMTI3LjE5MiA3NS4zNDggMTI3LjE5MloiIGZpbGw9IiMzMTM3NEEiLz4KPC9zdmc+Cg==') !default;\r\n$form-select-bg-size: 9px 12px !default;\r\n// scss-docs-end form-select-variables\r\n\r\n// scss-docs-start form-range-variables\r\n$form-range-track-bg: var(--#{$prefix}tertiary-bg) !default;\r\n// scss-docs-end form-range-variables\r\n\r\n// scss-docs-start form-floating-variables\r\n$form-floating-height: add(2.875rem, 2px) !default;\r\n$form-floating-input-padding-t: 22.5px !default;\r\n$form-floating-input-padding-b: 9.5px !default;\r\n$form-floating-label-transform: scale(0.85) translateY(-0.4rem)\r\n  translateX(0.15rem) !default;\r\n// scss-docs-end form-floating-variables\r\n\r\n//*-----------------------------------------------\r\n//|   Z-index\r\n//-----------------------------------------------*/\r\n$zindex-levels: (\r\n  n1: -1,\r\n  0: 0,\r\n  1: 1,\r\n  2: 2,\r\n  3: 3,\r\n  4: 4,\r\n  5: 5\r\n) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Nav-tabs\r\n//-----------------------------------------------*/\r\n$nav-tabs-border-width: 2px !default;\r\n$nav-underline-link-active-color: var(--#{$prefix}primary) !default;\r\n\r\n// nav-pills\r\n$nav-pills-link-active-color: var(--#{$prefix}primary) !default;\r\n$nav-pills-link-active-bg: transparent !default;\r\n$nav-pills-border-radius: var(--#{$prefix}border-radius-pill) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Navbar\r\n//-----------------------------------------------*/\r\n$navbar-padding-y: map_get($spacers, 2) !default;\r\n$navbar-padding-x: map_get($spacers, 4) !default;\r\n\r\n$navbar-brand-font-size: map_get($font-sizes, 5) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Navigation\r\n//-----------------------------------------------*/\r\n\r\n$navbar-font-size: $font-size-base * 0.8 !default; // phoenix specific variable\r\n\r\n$navbar-light-hover-color: rgba(\r\n  var(--#{$prefix}emphasis-color-rgb),\r\n  0.9\r\n) !default;\r\n$navbar-light-disabled-color: rgba(\r\n  var(--#{$prefix}emphasis-color-rgb),\r\n  0.25\r\n) !default;\r\n$navbar-light-toggler-icon-bg: str-replace(\r\n  url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#9da9bb' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M0 6h30M0 14h30M0 22h30'/%3E%3C/svg%3E\"),\r\n  '#',\r\n  '%23'\r\n) !default;\r\n\r\n$navbar-dark-color: rgba($white, 0.7) !default;\r\n$navbar-dark-hover-color: rgba($white, 0.9) !default;\r\n$navbar-dark-toggler-icon-bg: str-replace(\r\n  url(\"data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='#9da9bb' stroke-width='2' stroke-linecap='round' stroke-miterlimit='10' d='M0 6h30M0 14h30M0 22h30'/%3E%3C/svg%3E\"),\r\n  '#',\r\n  '%23'\r\n) !default;\r\n\r\n// phoenix specific variable\r\n$navbar-top-slim-height: 1.7rem !default;\r\n$navbar-top-height: var(--#{$prefix}navbar-top-height) !default;\r\n$standard-nav-height: 3.5625rem !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Dropdowns | Dropdown menu container and contents.\r\n//-----------------------------------------------*/\r\n$dropdown-padding-y: map_get($spacers, 3) !default;\r\n$dropdown-font-size: $navbar-font-size !default;\r\n\r\n// $dropdown-color: var(--#{$prefix}tertiary-color) !default;\r\n$dropdown-bg: var(--#{$prefix}emphasis-bg) !default;\r\n$dropdown-border-color: var(--#{$prefix}border-color) !default;\r\n$dropdown-divider-bg: var(--#{$prefix}border-color) !default;\r\n\r\n$dropdown-link-color: var(--#{$prefix}dropdown-link-color-global) !default;\r\n$dropdown-link-hover-color: var(\r\n  --#{$prefix}dropdown-link-hover-color-global\r\n) !default;\r\n$dropdown-link-hover-bg: var(--#{$prefix}secondary-bg) !default;\r\n$dropdown-link-disabled-color: $gray-100 !default;\r\n\r\n$dropdown-item-padding-y: map_get($spacers, 2) !default;\r\n$dropdown-item-padding-x: map_get($spacers, 3) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Pagination\r\n//-----------------------------------------------*/\r\n$pagination-padding-y: 0.5rem !default;\r\n\r\n$pagination-font-size: map-get($font-sizes, 9) !default;\r\n\r\n$pagination-color: var(--#{$prefix}secondary-color) !default;\r\n$pagination-bg: transparent !default;\r\n$pagination-border-width: 0 !default;\r\n\r\n$pagination-hover-color: var(--#{$prefix}primary) !default;\r\n$pagination-hover-bg: var(--#{$prefix}secondary-bg) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Cards\r\n//-----------------------------------------------*/\r\n$card-spacer-y: map-get($spacers, 4) !default;\r\n$card-spacer-x: map-get($spacers, 4) !default;\r\n$card-title-spacer-y: $spacer !default;\r\n$card-title-color: $headings-color !default;\r\n$card-border-radius: $border-radius-lg !default;\r\n$card-border-color: var(--#{$prefix}border-color-translucent) !default;\r\n$card-cap-bg: transparent !default;\r\n$card-cap-padding-y: $card-spacer-y !default;\r\n$card-bg: var(--#{$prefix}emphasis-bg) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Accordion\r\n//-----------------------------------------------*/\r\n$accordion-padding-y: map-get($spacers, 2) !default;\r\n$accordion-padding-x: 0 !default;\r\n$accordion-bg: transparent !default;\r\n$accordion-color: var(--#{$prefix}body-highlight-color) !default;\r\n\r\n$accordion-border-radius: 0 !default;\r\n$accordion-border-width: 0 !default;\r\n\r\n$accordion-button-color: var(--#{$prefix}primary) !default;\r\n$accordion-button-active-color: var(--#{$prefix}emphasis-color) !default;\r\n$accordion-button-active-bg: transparent !default;\r\n\r\n$accordion-icon-color: $primary !default;\r\n$accordion-icon-active-color: $primary !default;\r\n\r\n$accordion-button-icon: url(\"data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='-200 -200 900 900'%3E%3Cpath fill='#{$accordion-icon-color}' d='M416 208H272V64c0-17.67-14.33-32-32-32h-32c-17.67 0-32 14.33-32 32v144H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h144v144c0 17.67 14.33 32 32 32h32c17.67 0 32-14.33 32-32V304h144c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z'%3E%3C/path%3E%3C/svg%3E\") !default;\r\n$accordion-button-active-icon: url(\"data:image/svg+xml,%3Csvg aria-hidden='true' focusable='false' role='img' xmlns='http://www.w3.org/2000/svg' viewBox='-200 -200 900 900'%3E%3Cpath fill='#{$accordion-icon-active-color}' d='M416 208H32c-17.67 0-32 14.33-32 32v32c0 17.67 14.33 32 32 32h384c17.67 0 32-14.33 32-32v-32c0-17.67-14.33-32-32-32z'%3E%3C/path%3E%3C/svg%3E\") !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Tooltip\r\n//-----------------------------------------------*/\r\n$tooltip-font-size: map-get($font-sizes, 9) !default;\r\n$tooltip-padding-y: 0.5rem !default;\r\n\r\n$tooltip-color: $white;\r\n$tooltip-bg: $gray-1000 !default;\r\n\r\n$tooltip-border-radius: $border-radius-sm !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Popover\r\n//-----------------------------------------------*/\r\n$popover-bg: var(--#{$prefix}emphasis-bg) !default;\r\n$popover-border-color: var(--#{$prefix}border-color) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Toast\r\n//-----------------------------------------------*/\r\n$toast-font-size: $font-size-base !default;\r\n$toast-padding-x: 0 !default;\r\n$toast-padding-y: map-get($spacers, 3) !default;\r\n$toast-box-shadow: $box-shadow-gray-400 !default;\r\n$toast-background-color: rgba(var(--#{$prefix}emphasis-bg-rgb), 0.85) !default;\r\n$toast-border-color: var(--#{$prefix}border-color) !default;\r\n\r\n$toast-header-color: var(--#{$prefix}secondary-color) !default;\r\n$toast-header-background-color: transparent !default;\r\n$toast-header-border-color: var(--#{$prefix}border-color-translucent) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Badge\r\n//-----------------------------------------------*/\r\n$badge-padding-y: 0.355555em !default;\r\n$badge-padding-x: 0.711111em !default;\r\n$badge-border-radius: $border-radius-sm !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Modal\r\n//-----------------------------------------------*/\r\n$modal-content-border-radius: $border-radius !default;\r\n$modal-content-bg: var(--#{$prefix}emphasis-bg) !default;\r\n$modal-content-border-color: transparent !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Alert\r\n//-----------------------------------------------*/\r\n$alert-padding-y: map-get($spacers, 4) !default;\r\n$alert-padding-x: map-get($spacers, 4) !default;\r\n$alert-border-radius: map-get($spacers, 2) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Progress\r\n//-----------------------------------------------*/\r\n$progress-height: 0.313rem !default;\r\n$progress-border-radius: $border-radius-lg !default;\r\n$progress-box-shadow: initial !default;\r\n$progress-bg: var(--#{$prefix}secondary-bg) !default;\r\n\r\n///*-----------------------------------------------\r\n//|   List Group\r\n//-----------------------------------------------*/\r\n$list-group-color: var(--#{$prefix}secondary-color) !default;\r\n$list-group-bg: var(--#{$prefix}emphasis-bg) !default;\r\n$list-group-border-color: var(--#{$prefix}border-color-translucent) !default;\r\n$list-group-disabled-color: var(--#{$prefix}quaternary-color) !default;\r\n$list-group-hover-bg: var(--#{$prefix}body-highlight-bg) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Thumbnail\r\n//-----------------------------------------------*/\r\n$thumbnail-bg: var(--#{$prefix}emphasis-bg) !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Breadcrumbs\r\n//-----------------------------------------------*/\r\n$breadcrumb-font-size: map-get($font-sizes, 9) !default;\r\n$breadcrumb-item-padding-x: map-get($spacers, 2) !default;\r\n$breadcrumb-active-color: var(--#{$prefix}body-color) !default;\r\n$breadcrumb-divider: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' height='10' width='6' fill='#{$gray-600}' viewBox='0 0 256 480'%3E%3Cpath d='M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34z'/%3E%3C/svg%3E\") !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Carousel\r\n//-----------------------------------------------*/\r\n$carousel-transition-duration: 0.8s !default;\r\n$carousel-indicator-active-bg: $primary !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Spinner\r\n//-----------------------------------------------*/\r\n$spinner-width-sm: 1.35rem !default;\r\n$spinner-height-sm: $spinner-width-sm !default;\r\n\r\n// close\r\n// phoenix specific variable\r\n$btn-close-darken-nav-bg: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#ffffff'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\r\n// $btn-close-dark: url(\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='#ffffff'><path d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/></svg>\") !default;\r\n\r\n//*-----------------------------------------------\r\n//|  Offcanvas\r\n//-----------------------------------------------*/\r\n$offcanvas-horizontal-width: 458px !default;\r\n$offcanvas-bg-color: var(--#{$prefix}emphasis-bg) !default;\r\n\r\n// gutters\r\n$gutters: map-merge(\r\n  $spacers,\r\n  (\r\n    card: $card-spacer-x * 2\r\n  )\r\n) !default;\r\n\r\n//*---------------------------------------------------------------------------\r\n//|   Phoenix specific components\r\n//----------------------------------------------------------------------------*/\r\n\r\n//*-----------------------------------------------\r\n//|   Viewport Heights & Widths\r\n//----------------------------------------------\r\n$viewport-heights: (\r\n  50: 50vh,\r\n  100: 100vh\r\n) !default;\r\n\r\n$sizes: (\r\n  25: 25%,\r\n  30: 30%,\r\n  50: 50%,\r\n  60: 60%,\r\n  70: 70%,\r\n  75: 75%,\r\n  100: 100%,\r\n  auto: auto\r\n) !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Footer\r\n//-----------------------------------------------*/\r\n$footer-height: 4rem !default;\r\n\r\n// /*-----------------------------------------------\r\n//|   Avatar\r\n//-----------------------------------------------*/\r\n$avatars-dimension: (\r\n  's': toRem(24),\r\n  'm': toRem(32),\r\n  'l': toRem(40),\r\n  'xl': toRem(48),\r\n  '2xl': toRem(56),\r\n  '3xl': toRem(72),\r\n  '4xl': toRem(96),\r\n  '5xl': toRem(150)\r\n) !default;\r\n$avatars-placeholder-bg: var(--#{$prefix}secondary-bg) !default;\r\n$avatars-status-border-color: var(--#{$prefix}emphasis-bg) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Scrollbar\r\n//-----------------------------------------------*/\r\n$scrollbar-bg: var(--#{$prefix}scrollbar-bg) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Layout\r\n//-----------------------------------------------*/\r\n$content-padding-x: map-get($spacers, 6) !default;\r\n$content-padding-top: calc($navbar-top-height + 2rem) !default;\r\n$content-padding-bottom: 6.375rem !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Plugins color variables\r\n//-----------------------------------------------*/\r\n\r\n$data-table-pagination-button-color: $black !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Navbar Vertical\r\n//-----------------------------------------------*/\r\n\r\n$navbar-vertical-breakpoints: mapReverse($grid-breakpoints) !default;\r\n$navbar-vertical-width: 15.875rem !default;\r\n$navbar-vertical-icon-width: 1rem !default;\r\n\r\n$navbar-vertical-link-font-size: map-get($font-sizes, 9) !default;\r\n$navbar-vertical-link-padding-y: 0.35rem !default;\r\n\r\n$navbar-vertical-dropdown-font-size: 0.8125rem !default;\r\n$navbar-vertical-dropdown-link-padding-x: map-get($spacers, 2) !default;\r\n$navbar-vertical-dropdown-link-padding-y: map-get($spacers, 1) !default;\r\n\r\n$navbar-vertical-footer-height: $footer-height !default;\r\n$navbar-footer-border-color: var(\r\n  --#{$prefix}navbar-footer-border-color\r\n) !default;\r\n$navbar-vertical-height: calc(100vh - $navbar-top-height) !default;\r\n\r\n$navbar-vertical-content-height: calc(\r\n  100vh - var(--#{$prefix}navbar-top-height) - $navbar-vertical-footer-height\r\n) !default;\r\n$navbar-vertical-content-padding-x: map-get($spacers, 2) !default;\r\n$navbar-vertical-content-padding-y: map-get($spacers, 3) !default;\r\n$navbar-vertical-label-font-size: map-get($font-sizes, 10) !default;\r\n\r\n$navbar-vertical-collapse-width: 4rem !default;\r\n$navbar-vertical-link-collapse-padding-x: 1.5rem !default;\r\n$navbar-vertical-collapse-padding-x: 2.5rem !default;\r\n$navbar-vertical-hover-width: 12.625rem !default;\r\n$navbar-vertical-collapsed-hover-shadow: 0.625rem 0 0.625rem -0.5625rem var(--#{$prefix}navbar-vertical-collapsed-hover-shadow-color) !default;\r\n\r\n$navbar-vertical-border-color: var(\r\n  --#{$prefix}navbar-vertical-border-color\r\n) !default;\r\n$navbar-vertical-bg-color: var(--#{$prefix}navbar-vertical-bg-color) !default;\r\n$navbar-vertical-label-color: var(\r\n  --#{$prefix}navbar-vertical-label-color\r\n) !default;\r\n$navbar-vertical-link-color: var(\r\n  --#{$prefix}navbar-vertical-link-color\r\n) !default;\r\n$navbar-vertical-link-hover-color: var(\r\n  --#{$prefix}navbar-vertical-link-hover-color\r\n) !default;\r\n$navbar-vertical-link-hover-bg: var(\r\n  --#{$prefix}navbar-vertical-link-hover-bg\r\n) !default;\r\n$navbar-vertical-link-active-color: var(\r\n  --#{$prefix}navbar-vertical-link-active-color\r\n) !default;\r\n$navbar-vertical-link-disable-color: var(\r\n  --#{$prefix}navbar-vertical-link-disable-color\r\n) !default;\r\n\r\n$navbar-vertical-collapsed-menu-bg: var(\r\n  --#{$prefix}navbar-vertical-collapsed-menu-bg\r\n) !default;\r\n$navbar-vertical-hr-color: var(--#{$prefix}navbar-vertical-hr-color) !default;\r\n$navbar-vertical-dropdown-indicator-color: var(\r\n  --#{$prefix}navbar-vertical-dropdown-indicator-color\r\n) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Navbar Top\r\n//-----------------------------------------------*/\r\n$navbar-top-link-padding-start: 1.125rem !default;\r\n$navbar-top-link-padding-end: 1.5rem !default;\r\n$navbar-top-link-color: var(--#{$prefix}navbar-top-link-color) !default;\r\n\r\n//*-----------------------------------------------\r\n//|   Navbar dual\r\n//-----------------------------------------------*/\r\n$dual-nav-border-color: map-get($grays, '200') !default;\r\n$dual-nav-first-layer-bg: map-get($grays, '100') !default;\r\n"]}