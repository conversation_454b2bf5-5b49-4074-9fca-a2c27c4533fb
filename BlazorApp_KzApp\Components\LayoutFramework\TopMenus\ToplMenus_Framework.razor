﻿@inject NavigationManager NavigationManagerObj
@inject IJSRuntime JS

@code {

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public bool LeftMenusVisible { get; set; } = true;

    [Parameter]
    public bool TopRightMenus { get; set; } = true;

    [Parameter]
    public string TopBarCss { get; set; } = "";

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public bool UserLoginOrMenus { get; set; } = true;

    string ChannelName { get; set; } = "";
    string HomePageUrl = "";

    protected override Task OnInitializedAsync()
    {
        ChannelName = CObj.ThisChannel?.ColumnName ?? eZeroCore.Settings.GetSystemName();
        if (!CObj.IsWeiCity)
        {
            HomePageUrl = "/";
        }
        else
        {
            ChannelName = ChannelName.Truncate(30, "...");
            HomePageUrl = CObj.ThisChannel?.GetChannelUrl() ?? "/";
        }
        return base.OnInitializedAsync();
    }
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender)
        {
            await Generic.Js(JS, "Blz.ThemeSet");
        }        
    }
}

<nav class="navbar navbar-top fixed-top navbar-slim justify-content-between navbar-expand-lg @TopBarCss" id="navbarComboSlim" data-navbar-top="combo" data-move-target="#navbarVerticalNav">
    <div class="navbar-logo flex-grow-1 justify-content-start fw-bolder">
        @if (LeftMenusVisible)
        {
            <button class="btn navbar-toggler navbar-toggler-humburger-icon hover-bg-transparent" type="button" data-bs-toggle="collapse" data-bs-target="#navbarVerticalCollapse" aria-controls="navbarVerticalCollapse" aria-expanded="false" aria-label="Toggle Navigation">
                <span class="navbar-toggle-icon">
                    <span class="toggle-line"></span>
                </span>
            </button>
        }
        @if (CObj is not null && CObj.IsWeiCity)
        {
            <Ez_Btn WaitTimeMs=0 OnBtnClick="() => { NavigationManagerObj.NavigateTo(HomePageUrl); }" ClassName="navbar-brand ms-auto ms-md-0 me-auto d-none d-md-block">
                @ChannelName.ToUpper()
            </Ez_Btn>
        }
        else
        {
            <Ez_Btn WaitTimeMs=0 OnBtnClick="() => { NavigationManagerObj.NavigateTo(HomePageUrl, true); }" ClassName="navbar-brand ms-auto ms-md-0 me-auto">
                <img id="navbar_domainLogo" src="/stk/logoW1.png" alt="@CObj.WebSiteTitle" loading="eager">
            </Ez_Btn>
        }
    </div>

    <div class="collapse navbar-collapse navbar-top-collapse order-1 order-lg-0 justify-content-center" id="navbarTopCollapse">

        <ul class="navbar-nav navbar-nav-top" data-dropdown-on-hover="data-dropdown-on-hover">

            @ChildContent

        </ul>

    </div>

    @if (TopRightMenus)
    {
        /*
        <button onclick="Blz.ThemeSet()">
        <span class="fs-9 me-1">Dark</span>
        <span class="d-sm-flex flex-center">
        <i class="fa-solid fa-moon"></i>
        </span>
        </button>
        */
        <ul class="navbar-nav navbar-nav-icons flex-row PageLoading">
            @* Dark模式切换 *@
            <li class="nav-item @(CObj is not null && CObj.IsRenderOver ? "" : "d-none")">
                <div class="theme-control-toggle fa-ion-wait theme-control-toggle-slim">
                    <input class="form-check-input ms-0 theme-control-toggle-input" id="themeControlToggle" type="checkbox" data-theme-control="phoenixTheme" value="dark" />
                    <label class="mb-0 theme-control-toggle-label theme-control-toggle-light" for="themeControlToggle">
                        Dark
                        <i class="ms-1 fa-solid fa-moon"></i>
                    </label>
                    <label class="mb-0 theme-control-toggle-label theme-control-toggle-dark" for="themeControlToggle">
                        Light
                        <i class="ms-1 fa-regular fa-sun"></i>
                    </label>
                    
                </div>
            </li>
            @* 搜索 *@
            <li class="nav-item d-none @(CObj.IsLogin ? "" : "me-3")">
                <a class="nav-link" href="javascript:none" data-bs-toggle="modal" data-bs-target="#searchBoxModal">
                    <i class="fas fa-search"></i>
                </a>
            </li>
            @* 通知:Notification *@
            <li class="nav-item dropdown d-none @(CObj.IsLogin ? "" : "d-none")">
                <_Navbar_Notification_Dropdown />
            </li>
            <li class="nav-item dropdown d-none">
                <_Navbar_DropdownNindeDots />
            </li>
            <li class="nav-item dropdown">
                @if (UserLoginOrMenus)
                {
                    <_Navbar_User_DropdownMenu />
                }
            </li>
        </ul>
    }
</nav>