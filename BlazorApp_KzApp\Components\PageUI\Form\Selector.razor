﻿@using eZeroCore.Web.BlazorServerUI

@code {
    // 禁用自动渲染，重写 ShouldRender 方法并返回 false
    bool ShouldRenderEnabled = true;
    protected override bool ShouldRender()
    {
        return ShouldRenderEnabled;
    }

    [Parameter]
    public string ClassName { get; set; } = "";

    [Parameter]
    public List<SelectOption> Options { get; set; } = new();
    [Parameter]
    public string SelectedValue { get; set; } = "";

    [Parameter]
    public EventCallback<SelectOption> OnSelected { get; set; }

    [Parameter]
    public SelectOption? AddOption { get; set; }

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public bool AllowSelectNone { get; set; } = true;

    protected override Task OnInitializedAsync()
    {
        if (AddOption is not null && !Options.Any(x => x.Value.Equals(AddOption.Value,StringComparison.OrdinalIgnoreCase)))
        {
            Options.Add(AddOption);
        }
        return base.OnInitializedAsync();
    }

    string selectedValue = "";
    async Task SelectionChanged(ChangeEventArgs e)
    {
        selectedValue = e.Value?.ToString() ?? "";
        SelectOption selected = Options.FirstOrDefault(x => x.Value.Equals(selectedValue, StringComparison.OrdinalIgnoreCase)) ?? new();
        await OnSelected.InvokeAsync(selected);
    }
}

<select @onchange=@SelectionChanged class="form-select" disabled="@Disabled">
    @if (AllowSelectNone)
    {
        <option value="">
            --
        </option>
    }
    @foreach (SelectOption op in Options)
    {
        if (!string.IsNullOrWhiteSpace(op.Text))
        {
            <option value="@op.Value" style="@(string.IsNullOrEmpty(op.Color)?"":$"color:{op.Color}")" selected="@(op.Value.Equals(SelectedValue,StringComparison.OrdinalIgnoreCase)?true:false)">
                @op.Text
            </option>
        }
    }
</select>
