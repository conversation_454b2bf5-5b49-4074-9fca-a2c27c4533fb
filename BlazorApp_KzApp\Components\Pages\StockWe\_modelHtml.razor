﻿@using System.Globalization

@code {

    Settings settings = new();

    [Parameter]
    public eZeroCore.Web.StkV2.StockModel AIModel { get; set; } = new();

    public static string ThClass => "align-middle text-end text-body-tertiary fw-bold fs-9 text-uppercase text-nowrap";
    public static string TdClass => "align-middle white-space-nowrap fw-semibold text-body-highlight py-2 ps-2";
    public static string TdClassEnd => "align-middle text-end white-space-nowrap fw-semibold text-body-highlight p-2";
    public static string TbClassStockBadge = "badge badge-phoenix fs-8 badge-phoenix-warning";
}


<td class="@TdClass">
    <span class="@TbClassStockBadge">
        @AIModel.stock
    </span>
</td>
<td class="@TdClassEnd">
    <span class="text-warning-light fs-8">
        @if (AIModel.model < 3)
        {
            @eZeroCore.ENumeric.OutMonetaryStr(AIModel.price!.Value, true).Replace("$", "")
        }
        @if (AIModel.model >= 3 && AIModel.model < 4)
        {
            @((int)AIModel.score!)
        }
    </span>
</td>
<td class="@TdClassEnd">
    @if (AIModel.model < 3)
    {
        <div class="fs-10">
            @(AIModel.createDate!.Value.ToString(settings.SettingsEntity.LongDateFormat))
        </div>
        <div class="fs-10">
            <_Stars Score="(int)(AIModel.score??0)" />
        </div>
    }
    @if (AIModel.model >= 3 && AIModel.model < 4)
    {
        <div class="fs-10">
            @(AIModel.createDate!.Value.ToString(settings.SettingsEntity.LongDateFormat))
        </div>
    }
</td>

