@using <PERSON><PERSON><PERSON>
@inject IJSRuntime JS

<div class="position-relative w-100">
    <RadzenTextBox @ref="textBoxRef"
                   Value="@Value"
                   ValueChanged="@OnValueChanged"
                   Placeholder="@Placeholder"
                   ReadOnly="@ReadOnly"
                   MaxLength="@MaxLength"
                   Name="@Name"
                   Type="@InputType"
                   class="@(InputClass + " w-100 ps-5")"
                   Style="@Style" />
    @if (!string.IsNullOrEmpty(IconClass))
    {
        <i class="@IconClass position-absolute top-50 start-0 translate-middle-y ps-3 text-muted"></i>
    }
</div>

@code {
    private RadzenTextBox? textBoxRef;
    public async Task FocusAsync()
    {
        if (textBoxRef != null)
        {
            // Use the injected JSRuntime instance to call the JavaScript function.
            await Task.Delay(300); // Optional delay to ensure the element is ready
            await JS.InvokeVoidAsync("eSc.focusInput", textBoxRef.Element);
        }
    }    

    [Parameter] public string? Value { get; set; }
    [Parameter] public EventCallback<string?> ValueChanged { get; set; }
    [Parameter] public string? Placeholder { get; set; }
    [Parameter] public int MaxLength { get; set; } = 200;
    [Parameter] public string? IconClass { get; set; }
    [Parameter] public bool ReadOnly { get; set; } = false;
    [Parameter] public string? Name { get; set; }
    [Parameter] public string? InputClass { get; set; }
    [Parameter] public string? Style { get; set; }
    [Parameter] public InputType InputType { get; set; } = InputType.Text;

    private async Task OnValueChanged(string? newValue)
    {
        Value = newValue;
        await ValueChanged.InvokeAsync(newValue);
    }
}
