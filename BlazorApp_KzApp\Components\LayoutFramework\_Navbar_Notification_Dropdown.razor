@inject IJSRuntime JS

@code {    

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }
    protected override async void OnAfterRender(bool firstRender)
    {
        if (firstRender && eZeroCore.AppSettings.IsTestModeAll)
        {
            //await JS.InvokeVoidAsync("console.log", $"_Navbar_NotificationDropdown:{CObj.IpAddress}");
            await Task.Delay(0);
        }
        //base.OnAfterRender(firstRender);
    }
}

<a class="nav-link" id="navbarDropdownNotification" href="#" role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-haspopup="true" aria-expanded="false">
    <i class="fal fa-bell"></i>
</a>
<div class="dropdown-menu dropdown-menu-end notification-dropdown-menu py-0 shadow border navbar-dropdown-caret" id="navbarDropdownNotfication" aria-labelledby="navbarDropdownNotfication">
    <div class="card position-relative border-0">
        <div class="card-header p-2">
            <div class="d-flex justify-content-between">
                <h5 class="text-body-emphasis mb-0">Notifications</h5>
                <button class="btn btn-link p-0 fs-9 fw-normal d-none" type="button">Mark all as read</button>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="scrollbar-overlay" style="height: 27rem;">
                <!--
                    Notification Content
                -->
            </div>
        </div>
        <div class="card-footer p-0 border-top border-translucent border-0">
            <div class="my-2 text-center fw-bold fs-10 text-body-tertiary text-opactity-85">
                @*/lib/Ph117/public/pages/notifications.html*@
                <a class="fw-bolder" href="javascript:;">Notification history</a>
            </div>
        </div>
    </div>
</div>