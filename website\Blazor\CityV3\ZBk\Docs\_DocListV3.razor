@inject IJSRuntime JS
@inject NavigationManager NavManagerObj
@using <PERSON><PERSON><PERSON>
@code {
    [CascadingParameter]
    public ComponentPassObj CObj { get; set; } = new();

    [Parameter]
    public eZeroCore.Web.Document.RoleEnumForDoc RoleEnum { get; set; } = eZeroCore.Web.Document.RoleEnumForDoc.Website;

    [Parameter]
    public int PageSize { get; set; } = 6;

    [Parameter]
    public bool AllowPaging { get; set; } = true;

    /// <summary>
    /// SubVideoList Not Search
    /// </summary>
    [Parameter]
    public bool AllowSearch { get; set; } = true;

    [Parameter]
    public bool OrderByPageview { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    /// <summary>
    /// SubVideoList is true
    /// </summary>
    [Parameter]
    public bool AllowRowSelectOnRowClick { get; set; }

    [Parameter]
    public eZeroCore.Web.Column ColObj { get; set; } = new();

    [Parameter]
    public bool SubVideoList { get; set; }
    [Parameter]
    public string MainDocId { get; set; } = "";

    [Parameter]
    public bool MnanageMode { get; set; }
    //[Parameter]
    //public bool ReadOnly { get; set; }


    string ColumnId { get; set; } = "";
    int PageIndex = 0;
    int TotalCount { get; set; }
    string sSearchValue { get; set; } = "";
    string PageTitle = "";

    string sParentDocId = "";
    string sSubDocID = "";
    //bool videoStatus { get; set; }

    bool InManageMode;
    protected override async Task OnInitializedAsync()
    {
        InManageMode = CObj.IsBMS || MnanageMode;
        string sKeyIdOrUrlWrite = CObj.PageValue.Split('.').FirstOrDefault() ?? "";
        if (!SubVideoList)
        {
            if (string.IsNullOrEmpty(ColObj.ColumnIdTree))
            {
                ColObj = eZeroCore.Web.Column.GetColumn(sKeyIdOrUrlWrite);
            }

            PageTitle = ColObj.ColumnName;
            ColumnId = ColObj.ColumnId;
            if (InManageMode)
            {
                AllowRowSelectOnRowClick = true;
            }
            if (string.IsNullOrEmpty(ColumnId))
            {
                return;
            }
            else
            {
                await Init();
                if (!InManageMode)
                {
                    eZeroCore.Web.Column.PageViewAddOn(ColObj.ColumnId);
                }
            }
        }
        else if (!string.IsNullOrWhiteSpace(sKeyIdOrUrlWrite))
        {
            AllowSearch = false;
            AllowRowSelectOnRowClick = true;
            sParentDocId = MainDocId;
            if (CObj.PageValue.Split('.').Length > 1)
            {
                sSubDocID = CObj.PageValue.Split('.').LastOrDefault() ?? "";
            }
            await Init();
            if (string.IsNullOrEmpty(sSubDocID) && TotalCount > 0)
            {
                sSubDocID = Datas.FirstOrDefault()!.DocId;
            }
            var selectedDoc = new eZeroCore.Web.Document(sSubDocID);
            if (!string.IsNullOrWhiteSpace(selectedDoc.FullTitle) && rzDataTable is not null)
            {
                await rzDataTable.SetSelectedItem(Datas.Find(d => d.DocId.Equals(selectedDoc.DocId, StringComparison.OrdinalIgnoreCase)));
            }
        }


    }
    List<eZeroCore.Web.Document> Datas = new();
    bool isLoading = false;
    RzDataTable<eZeroCore.Web.Document> rzDataTable;
    async Task CreateNewDoc()
    {
        eZeroCore.Web.Document document = new()
            {
                VerifyPass = true,
                RoleEnum = RoleEnum,
                Hidden = true,
                FullTitle = $"New Title {DateTime.Now.Date.ToString(CObj.DataFormat_Short)}"
            };
        if (!SubVideoList)
        {
            document.ColumnId = ColumnId;
        }
        else
        {
            document.ReplyDocOrTargetId = sParentDocId;
        }
        await document.SaveAsync();
        if (rzDataTable is not null)
        {
            await Init();
            await rzDataTable.RefreshGrid();
        }
        else
        {
            await JS.InvokeVoidAsync("location.reload");
        }
        StateHasChanged();
    }
    async Task Init()
    {
        await Generic.Loading(JS);
        await LoadData();
        await Generic.Loading(JS, false);
    }
    async Task LoadData(LoadDataArgs? args = null)
    {
        isLoading = true;
        StateHasChanged();
        await Task.Delay(10);
        eZeroCore.Web.DocList docList = new()
            {
                RoleEnum = RoleEnum,
                HiddenVisable = InManageMode,
                Search = sSearchValue,
                UseCache = !InManageMode
                //OrderById = "docid='dciod8b03072'"
            };
        if (!SubVideoList)
        {
            docList.ColumnIds = [ColumnId];
            docList.OrderBy = OrderByPageview ? eZeroCore.Web.DocList.OrderByEnum.Hits : eZeroCore.Web.DocList.OrderByEnum.Createdate;
        }
        else
        {
            docList.ReplyDocOrTargetId = sParentDocId;
            docList.OrderBy = OrderByPageview ? eZeroCore.Web.DocList.OrderByEnum.Hits : eZeroCore.Web.DocList.OrderByEnum.Followorder;
            docList.SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending;
        }
        Datas = await docList.GetListAsync(args, PageSize, eZeroCore.Db.CacheHelperV2.CacheNameType.Doc_List);
        //await JS.InvokeVoidAsync("console.log", $"【dataGrid.GoToPag:{docList.SetPageNumber}】");
        PageIndex = docList.SetPageNumber;
        TotalCount = docList.TotalCount;
        isLoading = false;
    }

    [Parameter]
    public EventCallback<eZeroCore.Web.Document> OnSelectedDoc { get; set; }

    async Task OnSelected(eZeroCore.Web.Document? docObj)
    {
        if (docObj is not null)
        {
            await OnSelectedDoc.InvokeAsync(new(docObj.DocId));
            if (!InManageMode && SubVideoList)
            {
                NavManagerObj.NavigateTo(docObj.GetDocUrl(), false);
            }
            if (AppSettings.IsTestModeAll)
            {
                await JS.InvokeVoidAsync("console.log", $"OnSelected:【{docObj.GetDocUrl()}】");
            }
        }
        //StateHasChanged();
        await Task.Delay(0);

    }

    public async Task Refresh(string value = "")
    {
        sSearchValue = value.ToUpper();
        //await JS.InvokeVoidAsync("console.log", $"OnSearch:【{sSearchValue}】");
        await Init();
        StateHasChanged();
        await Task.Delay(0);
    }

    async Task ClickTag(string tag)
    {
        if (AllowSearch)
        {
            await Refresh(tag);
        }
    }

    [Parameter]
    public string HeaderClass { get; set; } = "";

}
@if (AllowSearch)
{
    <SearchBox OnSearch="@Refresh" InputValue="@sSearchValue" Placeholder="@CObj.UserLang.GetLangValue("搜索股票代号或关键词")" />
}
@if (InManageMode && PageIndex < 2)
{
    <div class="d-grid gap-2 mb-2">
        <Ez_Btn OnBtnClick="@CreateNewDoc" WaitTimeMs="500" ClassName="btn btn-phoenix-success w-100">
            <i class="fal fa-plus me-1"></i>
            @CObj.UserLang.GetLangValue("创建新项")
        </Ez_Btn>
    </div>
}
<div class="card shadow-none border py-2">

    @if (ChildContent is not null)
    {
        <div class="card-header pt-1 pb-2 px-2 @HeaderClass">
            @ChildContent
        </div>
    }

    <RzDataTable @ref=rzDataTable TItem="eZeroCore.Web.Document"
                 PageNumbersCount="@(InManageMode?5:8)"
                 TheadVisable="false"
                 AllowRowSelectOnRowClick="@AllowRowSelectOnRowClick"
                 ShowPagingSummary="@(CObj.IsMobile?false:true)"
                 PagerAlignCenter="@(CObj.IsMobile?true:false)"
                 AllowPaging="@AllowPaging"
                 Slim="true"
                 PageArgsEnabled="false"
                 Data="@Datas" PageSize="@PageSize"
                 IsLoading="@isLoading"
                 OnSelected="@OnSelected"
                 Count="@TotalCount" LoadData="@LoadData">

        <RadzenDataGridColumn TItem="eZeroCore.Web.Document" Sortable="false"
                              CssClass="@(AllowRowSelectOnRowClick?"hand":"")" Property="FullTitle">
            <Template Context="data">                
                <_DocTitleItem TagsVisible="AllowSearch" AllowRowSelectOnRowClick="@AllowRowSelectOnRowClick" OnClickTag="async (tag)=>{ await ClickTag(tag);}" DocObj="@data" />
            </Template>
        </RadzenDataGridColumn>

    </RzDataTable>
</div>