﻿@inject IJSRuntime JS
@code {

    [Parameter]
    public EventCallback<List<string>> OnInputChange { get; set; }
    [Parameter]
    public string TagsName { get; set; } = "Tags";

    public void Clear()
    {
        listTags.Clear();
    }

    private bool inputVisible { get; set; } = false;
    string _inputValue = "";    

    [Parameter]
    public List<string> listTags { get; set; } = [];

    async Task ValueChange(ChangeEventArgs value)
    {
        listTags.Add(value.Value!.ToString()!.ToUpper());
        //await InitDataTable();
        listTags.RemoveAll(item => string.IsNullOrEmpty(item));
        await OnInputChange.InvokeAsync(listTags);
    }

    async Task OnClose(string item)
    {
        listTags.Remove(item);
        listTags.RemoveAll(item => string.IsNullOrEmpty(item));
        await OnInputChange.InvokeAsync(listTags);
        await Task.Delay(0);
    }

    async Task HandleInputConfirm()
    {
        if (string.IsNullOrEmpty(_inputValue))
        {
            CancelInput();
            return;
        }
        string res = "";
        try
        {
            res = listTags.Find(s => s.Equals(_inputValue, StringComparison.OrdinalIgnoreCase))!;
        }
        catch { }

        if (string.IsNullOrEmpty(res) && !string.IsNullOrEmpty(_inputValue.Trim()))
        {
            listTags.Add(_inputValue.ToUpper());
            //await InitDataTable();
            listTags.RemoveAll(item => string.IsNullOrEmpty(item));
            await OnInputChange.InvokeAsync(listTags);
        }

        CancelInput();
    }

    void CancelInput()
    {
        this._inputValue = "";
        this.inputVisible = false;
    }

    string inputId = "InputTagsAddInput";
    private async Task SetFocusToInput()
    {
        await JS.InvokeVoidAsync("eval", $"document.getElementById('{inputId}').focus()");
    }
    async Task OnAddTags()
    {
        inputVisible = !inputVisible;
        await Task.Delay(100);
        await SetFocusToInput();
    }
}

<div class="@(string.Join(',', listTags))">
    @foreach (var item in listTags)
    {
        if (!string.IsNullOrEmpty(item))
        {
            <label class="d-inline-flex mb-3 px-2 py-1 badge badge-phoenix badge-phoenix-info me-1" role="alert" @key="item">
                @item
                <button @onclick="()=>OnClose(item)" type="button">
                    <i class="fa-solid fa-xmark ms-2"></i>
                </button>
            </label>
        }
    }
    @if (inputVisible)
    {
        <input type="text" id="@inputId" class="form-control w-80px d-inline px-2 py-1 ms-1" @bind=@_inputValue @onblur=@HandleInputConfirm />
    }
    else
    {
        <label class="d-inline-flex mb-3 px-2 py-1 badge text-bg-info hand" role="alert">
            <button @onclick="OnAddTags" type="button">
                <i class="fa-solid fa-plus me-2"></i>
            </button>
            @TagsName
        </label>
    }
</div>
