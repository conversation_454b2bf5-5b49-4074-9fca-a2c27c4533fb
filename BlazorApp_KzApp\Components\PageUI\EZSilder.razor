﻿@inject IJSRuntime JS
@code {
    private object nonRefreshingKeyA = new object();
    private object nonRefreshingKeyB = new object();
}
<div class="w-100 ps-3 pe-4">
    <div class="rangeslider">
        <input step="@Step" class="min"
               @key=nonRefreshingKeyA
               @onchange=HandleMinInputChange
               data-multiplier="@Multipliter"
               tabindex="-1"
               data-unit="@Unit"
               data-UnitAfter="@(UnitAfter?"1":"")"
               data-fmtNm="@(FormatNumber?"1":"")"
               name="@groupName" type="range"
               value="@SetValue.s"
               min="@(MinMaxValue.s)" max="@(MinMaxValue.e)" />
        <input class="max"
               @key=nonRefreshingKeyB
               @onchange=HandleMaxInputChange
               data-multiplier="@Multipliter"
               tabindex="-1"
               data-unit="@Unit"
               data-UnitAfter="@(UnitAfter?"1":"")"
               data-fmtNm="@(FormatNumber?"1":"")"
               name="@groupName" type="range"
               value="@SetValue.e"
               min="@(MinMaxValue.s)" max="@(MinMaxValue.e)" />

        @if (SetValue.Equals(MinMaxValue))
        {
            <span class="range_min left">
                @if (!UnitAfter)
                {
                    @Unit
                }
                @if (Multipliter >= 0)
                {
                    @(FormatNumber ? eZeroCore.ENumeric.ConvertNumberToMorKString(MinMaxValue.s * Multipliter) : MinMaxValue.s * Multipliter)
                }
                else
                {
                    @(FormatNumber ? eZeroCore.ENumeric.ConvertNumberToMorKString(MinMaxValue.s / Math.Abs(Multipliter)) : MinMaxValue.s / Math.Abs(Multipliter))
                }
                @if (UnitAfter)
                {
                    @Unit
                }
            </span>
            <span class="range_max right">
                @if (!UnitAfter)
                {
                    @Unit
                }
                @if (Multipliter >= 0)
                {
                    @(FormatNumber ? eZeroCore.ENumeric.ConvertNumberToMorKString(MinMaxValue.e * Multipliter) : MinMaxValue.e * Multipliter)
                }
                else
                {
                    @(FormatNumber ? eZeroCore.ENumeric.ConvertNumberToMorKString(MinMaxValue.e / Math.Abs(Multipliter)) : MinMaxValue.e / Math.Abs(Multipliter))
                }
                @if (UnitAfter)
                {
                    @Unit
                }
            </span>
        }
        else
        {
            <span class="range_min left">
                @if (!UnitAfter)
                {
                    @Unit
                }
                @if (Multipliter >= 0)
                {
                    @(FormatNumber ? eZeroCore.ENumeric.ConvertNumberToMorKString(SetValue.s * Multipliter) : SetValue.s * Multipliter)
                }
                else
                {
                    @(FormatNumber ? eZeroCore.ENumeric.ConvertNumberToMorKString(SetValue.s / Math.Abs(Multipliter)) : SetValue.s / Math.Abs(Multipliter))
                }
                @if (UnitAfter)
                {
                    @Unit
                }
            </span>
            <span class="range_max right">
                @if (!UnitAfter)
                {
                    @Unit
                }
                @if (Multipliter >= 0)
                {
                    @(FormatNumber ? eZeroCore.ENumeric.ConvertNumberToMorKString(SetValue.e * Multipliter) : SetValue.e * Multipliter)
                }
                else
                {
                    @(FormatNumber ? eZeroCore.ENumeric.ConvertNumberToMorKString(SetValue.e / Math.Abs(Multipliter)) : SetValue.e / Math.Abs(Multipliter))
                }
                @if (UnitAfter)
                {
                    @Unit
                }
            </span>

        }

    </div>
</div>

@code {

    [Parameter]
    public EventCallback<(double, double)> OnChangeAfter { get; set; }
    //public (double s, double e) SetValue { get; set; }
    [Parameter]
    public (double s, double e) SetValue { get; set; }
    /// <summary>
    /// 倍率（100=X100，-100=÷100）
    /// </summary>
    [Parameter]
    public int Multipliter { get; set; } = 1;
    /// <summary>
    /// Default Value
    /// </summary>
    [Parameter]
    public (double s, double e) MinMaxValue { get; set; }
    [Parameter]
    public string Unit { get; set; } = "";
    [Parameter]
    public bool UnitAfter { get; set; } = false;
    [Parameter]
    public bool FormatNumber { get; set; } = true;
    [Parameter]
    public int Step { get; set; } = 1;

    private async Task HandleMinInputChange(ChangeEventArgs e)
    {
        SetValue = (eZeroCore.ENumeric.ConvertTodouble(e.Value ?? ""), SetValue.e);
        await OnChangeAfter.InvokeAsync(SetValue);
    }
    private async Task HandleMaxInputChange(ChangeEventArgs e)
    {
        SetValue = (SetValue.s, eZeroCore.ENumeric.ConvertTodouble(e.Value ?? ""));
        await OnChangeAfter.InvokeAsync(SetValue);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await Init();
        }
    }

    public async Task Init()
    {
        try
        {
            await JS.InvokeVoidAsync("initializeRangeInputHandler");
        }
        catch
        {
            
        }

    }

    private string groupName = "";

    protected override Task OnInitializedAsync()
    {
        groupName = Guid.NewGuid().ToString();
        if (SetValue.s + SetValue.e == 0)
        {
            SetValue = MinMaxValue;
            //StateHasChanged();
        }
        return base.OnInitializedAsync();
    }

}
