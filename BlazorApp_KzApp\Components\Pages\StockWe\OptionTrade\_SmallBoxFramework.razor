﻿@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public RenderFragment? Title { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }
    [Parameter]
    public string Icon { get; set; } = "";
    [Parameter]
    public string TitleClass { get; set; } = "mb-2";
    [Parameter]
    public string ContentClass { get; set; } = "h3";

    [Parameter]
    public bool DFlex { get; set; } = true;

    [Parameter]
    public string CardBodyClass { get; set; } = "card-body py-1 px-1 px-xl-3 pt-3";

    /// <summary>
    /// col-6 col-xl-2
    /// </summary>
    [Parameter]
    public string ClassName { get; set; } = "col-6 col-xl-3";
}

<div class="@ClassName">
    <div class="card h-100">
        <div class="@CardBodyClass">
            <div class="@(DFlex?"d-flex justify-content-between":"")">
                <div class="w-100">
                    @if (Title is not null)
                    {
                        <h6 class="text-body-tertiary @TitleClass">
                            <i class="@Icon me-1"></i>
                            <b>
                                @Title
                            </b>
                        </h6>
                    }
                    <div class="ms-xl-n1 position-relative @ContentClass">
                        @ChildContent
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@*
    <div>
        <h6 class="text-body-tertiary">Last 7 days</h6>
        <h5 class="mb-1">New customers<span class="badge badge-phoenix badge-phoenix-warning rounded-pill fs-9 ms-2"> <span class="badge-label">+26.5%</span></span></h5>
        </div>
    <h4>356</h4>
*@
