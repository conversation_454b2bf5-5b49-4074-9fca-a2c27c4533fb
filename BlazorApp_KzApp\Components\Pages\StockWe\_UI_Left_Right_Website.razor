﻿@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public RenderFragment? RightContent { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string ChildContentClass { get; set; } = "";

    [Parameter]
    public string ChildContentStyle { get; set; } = "";

    [Parameter]
    public string RightContentClass { get; set; } = "position-sticky";

    [Parameter]
    public string RightContentStyle { get; set; } = "top:30px;";

    [Parameter]
    public string PanelContentClass { get; set; } = "";

    [Parameter]
    public bool Border { get; set; } = true;

}

<div class="row pt-2 mb-5 @PanelContentClass">

    <div class="col-12 col-xl-8 mt-2 px-1">
        <div class="@(Border?"card shadow-none border":"") @ChildContentClass" style="@ChildContentStyle">
            @ChildContent
        </div>
    </div>

    @if (RightContent is not null)
    {
        <div class="d-md-none d-xl-block col-xl-4 order-1 order-xl-0 mt-2 px-1">
            <div class="@(Border?"card shadow-none border":"") @RightContentClass" style="@RightContentStyle">
                @RightContent
            </div>
        </div>
    }

</div>