﻿@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public string GreenTitle { get; set; } = "做多";
    [Parameter]
    public string RedTitle { get; set; } = "做空";
    [Parameter]
    public string GreenTitleTip { get; set; } = "看涨";
    [Parameter]
    public string RedTitleTip { get; set; } = "看跌";


    string Stock { get; set; } = "";
    long ValueGreen { get; set; }
    long ValueRed { get; set; }
    long resultValue{ get; set; }

    private ElementReference? chartDiv;
    public async Task DrawChart(long valueGreen, long valueRed,string stock)
    {
        if (chartDiv is not null)
        {
            ValueGreen = valueGreen;
            ValueRed = valueRed;
            Stock = stock;
            TrendTotal = ValueGreen + ValueRed;
            resultValue = ValueGreen - ValueRed;
            StateHasChanged();
            //await Task.Delay(50);
            await Generic.Js(JS, "HalfPie_TrendUpDown.drawChart", chartDiv, ValueGreen, ValueRed);
            //await JS.InvokeVoidAsync("HalfPie_TrendUpDown.drawChart", chartDiv, ValueGreen, ValueRed);
        }
    }
    long TrendTotal = 0;

    string getRate(long value)
    {
        if (TrendTotal != 0)
        {
            // 计算百分比
            double result = (double)value / TrendTotal * 100;
            return Math.Round(result, 2).ToString("F2") + "%";
        }
        return "";
    }

    bool upDownString(long red, long green)
    {
        if (red > green)
        {
            return true;
        }
        else
        {
            return false;
        }
    }


    /*
    protected override async Task OnParametersSetAsync()
    {
        if (!_ValueGreen.Equals(ValueGreen))
        {
            _ValueGreen = ValueGreen;
            await JS.InvokeVoidAsync("console.log", $"【HalfPie_TrendUpDown_OnParametersSetAsync{ValueGreen}】");
            await DrawChart();
        }
        if(CObj.IsKyrison)
        {
            await JS.InvokeVoidAsync("console.log", $"【{ValueGreen}】");
        }
        //return base.OnParametersSetAsync();
    }
    */

}

<_SmallBoxFramework Icon="fal fa-analytics" ContentClass="h6" ClassName="col-12 col-xl" DFlex=false CardBodyClass="">
    <ChildContent>

        <div class="d-flex justify-content-center @Stock">
            <div class="flex-fill">
                <div class="text-end">
                    <svg class="ms-auto" width="100" height="60" xmlns="http://www.w3.org/2000/svg">
                        <g transform="scale(-1, 1) translate(-100, 0)">
                            <polyline points="5,30 60,30 90,50" class="stkGreen" fill="none"></polyline>
                            <circle cx="5" cy="30" r="3" class="stkGreen"></circle>
                            <circle cx="90" cy="50" r="3" class="stkGreen"></circle>
                        </g>
                    </svg>
                </div>
                <div class="text-end pe-3">
                    <div class="stkGreen stkNumber mb-2">
                        <span class="h4 stkGreen">
                            @getRate(ValueGreen)
                        </span>
                        <span class="badge badge-phoenix badge-phoenix-danger stkGreen fs-9">
                            @GreenTitle
                        </span>
                    </div>
                    <span class="stkGreen d-block mb-2">
                        @(GreenTitleTip)总权利金
                    </span>
                    <span class="stkGreen stkNumber d-block">
                        @eZeroCore.ENumeric.NFormattedNum(ValueGreen)
                    </span>
                </div>

            </div>
            <div style="width:160px;">
                <div @ref="chartDiv" class="mx-auto w-100 eChartDom" style="height: 160px;"></div>
            </div>
            <div class="flex-fill">
                <div>
                    <svg width="100" height="60" xmlns="http://www.w3.org/2000/svg">
                        <polyline points="5,30 60,30 90,50" class="stkRed" fill="none"></polyline>
                        <circle cx="5" cy="30" r="3" class="stkRed"></circle>
                        <circle cx="90" cy="50" r="3" class="stkRed"></circle>
                    </svg>
                </div>
                <div class="text-start ps-3">
                    <div class="stkRed stkNumber mb-2">
                        <span class="badge badge-phoenix badge-phoenix-success stkRed fs-9">
                            @RedTitle
                        </span>
                        <span class="h4 stkRed">
                            @getRate(ValueRed)
                        </span>
                    </div>
                    <span class="stkRed d-block mb-2">
                        @(RedTitleTip)总权利金
                    </span>
                    <span class="stkRed stkNumber d-block">
                        @eZeroCore.ENumeric.NFormattedNum(ValueRed)
                    </span>
                </div>
            </div>
        </div>

        <div class="position-absolute top-50 start-50 translate-middle">
            @if (upDownString(ValueGreen, ValueRed))
            {
                <span class="badge badge-phoenix badge-phoenix-success p-1">
                    <span class="stkGreen fs-8">
                        @GreenTitleTip
                    </span>
                    <span class="d-block mt-2 fs-10">
                        @eZeroCore.ENumeric.NFormattedNum(resultValue)
                    </span>
                </span>
            }
            else
            {
                <span class="badge badge-phoenix badge-phoenix-danger p-1">
                    <span class="stkRed fs-8">
                        @RedTitleTip
                    </span>
                    <span class="d-block mt-2 fs-10">
                        @eZeroCore.ENumeric.NFormattedNum(resultValue)
                    </span>
                </span>
            }
        </div>


    </ChildContent>
</_SmallBoxFramework>