@using MudBlazor
@using ZycCore.Models.Dto
@using ZycCore.Models
@using ZycCore.Utils
@using <PERSON><PERSON><PERSON>
@using System.Globalization
@using BlazorApp_KzApp.Components.PageUI.RzDataGrid
@inject IJSRuntime JS
@inject NavigationManager Navigation
@implements IDisposable

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public string StockCodeString { get; set; } = string.Empty;

    // 动态标题属性
    public string Title
    {
        get
        {
            return CObj.UserLang.GetLangValue("分析师评级");
        }
    }

    private List<StockAnalystRating.Data> Datas = new();
    bool isLoading = false;
    int PageSize = 20; // 每页显示20条数据
    int TotalCount { get; set; } = 0; // 添加总数
    int page { get; set; } = 1; // 添加当前页码

    // 保存上一次的股票代号，用于检测参数变化
    private string _previousStockCode = string.Empty;

    // 每行独立的弹窗状态 - 统一弹窗
    private Dictionary<int, bool> _upgradePopoverStates = new();
    private Dictionary<int, System.Threading.Timer?> _upgradeTimers = new();

    // 鼠标位置状态
    private Dictionary<int, (double X, double Y)> _mousePositions = new();
    
    // 预计算的弹窗位置
    private Dictionary<int, string> _popoverPositions = new();

    // 检查用户是否为付费用户
    private bool IsPayingUser()
    {
        if (!CObj.IsLogin)
        {
            return false;
        }
        return eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj.UserId));
    }

    protected override async Task OnInitializedAsync()
    {
        _previousStockCode = StockCodeString;
        await LoadData();
    }

    // 监听参数变化
    protected override async Task OnParametersSetAsync()
    {
        // 如果股票代号发生变化，重新加载数据
        if (_previousStockCode != StockCodeString)
        {
            _previousStockCode = StockCodeString;
            await LoadData();
        }
    }

    // 公共方法：供外部组件调用以刷新数据
    public async Task RefreshData(string stockCode)
    {
        if (StockCodeString != stockCode)
        {
            // 这种情况理论上不应该发生，因为参数绑定会自动更新
            // 但为了保险起见，我们也支持直接传入股票代号
            await LoadDataForStock(stockCode);
        }
        else
        {
            await LoadData();
        }
    }

    // 为指定股票代号加载数据 - 修改支持分页
    private async Task LoadDataForStock(string stockCode, LoadDataArgs? args = null)
    {
        isLoading = true;
        StateHasChanged();
        await Task.Delay(10);

        // 构建Dto对象
        var currentDate = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date;
        var dateStart = currentDate.AddDays(-365);
        var dateEnd = currentDate;
        var dto = new StockAnalystRatingDto
            {
                DateStart = dateStart,
                DateEnd = dateEnd,
                StockCode = stockCode, // 使用传入的股票代号
                Stocks = new List<string>(),
                NewRatings = new List<string>(),
                RatingChange = string.Empty,
                ScoreMin = -50,
                ScoreMax = 100,
                MarketCapMin = 0,
                MarketCapMax = 3000,
                AccurateMin = 0,
                AccurateMax = 100,
                NewPriceTargetMin = 0,
                NewPriceTargetMax = 2000,
                CurrentPriceMin = 0,
                CurrentPriceMax = 1500,
                UpsideDownsideMin = -10,
                UpsideDownsideMax = 100
            };

        // 调用实体类获取数据 - 支持分页参数
        var list = new StockAnalystRating.List();
        Datas = await list.DataTableListAsync(args, PageSize, dto);
        
        // 获取分页信息
        TotalCount = list.TotalCount;
        page = (args?.Skip ?? 0) / PageSize + 1;

        // 初始化所有行的弹窗状态 - 数据加载完成后
        _upgradePopoverStates.Clear();
        _popoverPositions.Clear();
        for (int i = 0; i < Datas.Count; i++)
        {
            _upgradePopoverStates[i] = false;
        }

        isLoading = false;
        StateHasChanged();
    }

    // 加载数据 - 支持分页
    private async Task LoadData(LoadDataArgs? args = null)
    {
        await LoadDataForStock(StockCodeString, args);
    }

    // 判断是否需要模糊遮罩（前5行）
    private bool ShouldBlur(int index)
    {
        // 如果是付费用户，不需要模糊
        if (IsPayingUser())
        {
            return false;
        }
        return index < 5;
    }

    // 获取分析师准确率头像URL
    private string GetAnalystAvatar(int index)
    {
        // 基于索引计算头像ID，确保相同索引总是返回相同头像
        int avatarId = (index % 5) + 1; // 假设有5个头像文件循环使用
        return $"/Ph/images/analyst/{avatarId}.png";
    }

    // 获取响应式弹窗位置（考虑不同屏幕尺寸）
    private async Task<string> GetResponsivePopoverPositionAsync(int rowIndex)
    {
        // 基础配置
        var config = new
        {
            PopoverWidth = 280, // 弹窗宽度
            PopoverHeight = 200,// 弹窗高度
            Padding = 20,       // 边距
            Offset = 20         // 与鼠标的距离
        };

        // 获取鼠标位置，如果没有则使用默认位置
        var (mouseX, mouseY) = _mousePositions.TryGetValue(rowIndex, out var pos) ? pos : (420, 200 + (rowIndex * 39));

        // 动态获取视口尺寸
        var viewportSize = await GetViewportSizeAsync();
        var viewportWidth = viewportSize.Width > 0 ? viewportSize.Width : 1200; // 默认1200px
        var viewportHeight = viewportSize.Height > 0 ? viewportSize.Height : 800; // 默认800px

        // 智能判断弹窗应该出现在鼠标的左侧还是右侧
        bool showOnLeft = mouseX + config.PopoverWidth + config.Offset > viewportWidth - config.Padding;
        
        // 计算弹窗位置
        double calculatedLeft;
        if (showOnLeft)
        {
            // 显示在鼠标左侧
            calculatedLeft = mouseX - config.PopoverWidth - config.Offset;
        }
        else
        {
            // 显示在鼠标右侧
            calculatedLeft = mouseX + config.Offset;
        }

        // 计算垂直位置（稍微在鼠标上方）
        double calculatedTop = mouseY - 50;

        // 边界检查 - 垂直方向
        calculatedTop = Math.Max(config.Padding, calculatedTop);
        calculatedTop = Math.Min(viewportHeight - config.PopoverHeight - config.Padding, calculatedTop);

        // 边界检查 - 水平方向（确保弹窗完全在视口内）
        calculatedLeft = Math.Max(config.Padding, calculatedLeft);
        calculatedLeft = Math.Min(viewportWidth - config.PopoverWidth - config.Padding, calculatedLeft);

        return $"position: fixed; top: {calculatedTop}px; left: {calculatedLeft}px;";
    }

    // 获取视口尺寸的方法
    private async Task<(double Width, double Height)> GetViewportSizeAsync()
    {
        try
        {
            var size = await JS.InvokeAsync<double[]>("getViewportSize");
            if (size != null && size.Length >= 2)
            {
                return (size[0], size[1]);
            }
        }
        catch
        {
            // 如果获取失败，返回默认值
        }
        return (1200, 800);
    }

    // 添加行渲染样式处理方法
    private void OnRowRender(RowRenderEventArgs<StockAnalystRating.Data> args)
    {
        // 默认样式
        string className = "rz-data-row";

        // 根据准确率设置样式
        if (args.Data.Accurate > 78)
        {
            //  className = "rz-data-row flowOverviewBarRowFill";
        }

        args.Attributes["class"] = className;
    }

    // 悬浮事件处理方法 - 统一弹窗（使用实时鼠标位置）
    private async Task OnUpgradeMouseEnter(int rowIndex)
    {
        // 如果是付费用户，不显示弹窗
        if (IsPayingUser())
        {
            return;
        }

        // 获取当前实时鼠标位置
        try
        {
            var mousePos = await JS.InvokeAsync<double[]>("getCurrentMousePosition");
            if (mousePos != null && mousePos.Length >= 2)
            {
                _mousePositions[rowIndex] = (mousePos[0], mousePos[1]);
            }
            else
            {
                // 如果获取失败，使用基于行索引的估算位置
                _mousePositions[rowIndex] = (420, 200 + (rowIndex * 39));
            }
        }
        catch
        {
            // 如果获取鼠标位置失败，使用默认位置
            _mousePositions[rowIndex] = (420, 200 + (rowIndex * 39));
        }

        // 预计算弹窗位置
        _popoverPositions[rowIndex] = await GetResponsivePopoverPositionAsync(rowIndex);

        // 先清除所有弹窗状态
        foreach (var key in _upgradePopoverStates.Keys.ToList())
        {
            _upgradePopoverStates[key] = false;
        }

        // 清理所有定时器
        foreach (var timer in _upgradeTimers.Values)
        {
            timer?.Dispose();
        }
        _upgradeTimers.Clear();
        
        // 清理其他行的预计算位置（保留当前行的位置）
        var keysToRemove = _popoverPositions.Keys.Where(k => k != rowIndex).ToList();
        foreach (var key in keysToRemove)
        {
            _popoverPositions.Remove(key);
        }

        // 设置当前行弹窗显示状态
        _upgradePopoverStates[rowIndex] = true;
        StateHasChanged();
    }

    private void OnUpgradeMouseLeave(int rowIndex)
    {
        // 如果是付费用户，不处理弹窗逻辑
        if (IsPayingUser())
        {
            return;
        }

        // 清理现有定时器
        if (_upgradeTimers.ContainsKey(rowIndex))
        {
            _upgradeTimers[rowIndex]?.Dispose();
            _upgradeTimers.Remove(rowIndex);
        }

        // 设置延迟隐藏弹窗（给用户时间移动到弹窗上）
        _upgradeTimers[rowIndex] = new System.Threading.Timer(_ =>
        {
            InvokeAsync(() =>
            {
                _upgradePopoverStates[rowIndex] = false;
                StateHasChanged();
            });
        }, null, 300, Timeout.Infinite); // 300ms延迟
    }

    // 移动端专用的点击处理方法
    private async Task OnMobileUpgradeClick(int rowIndex)
    {
        // 如果是付费用户，不处理弹窗逻辑
        if (IsPayingUser())
        {
            return;
        }

        // 先清除所有弹窗状态
        foreach (var key in _upgradePopoverStates.Keys.ToList())
        {
            _upgradePopoverStates[key] = false;
        }

        // 清理所有定时器
        foreach (var timer in _upgradeTimers.Values)
        {
            timer?.Dispose();
        }
        _upgradeTimers.Clear();

        // 设置当前行弹窗显示状态
        _upgradePopoverStates[rowIndex] = true;
        StateHasChanged();

        await Task.CompletedTask;
    }

    // 关闭移动端弹窗
    private void CloseMobileUpgradePopover(int rowIndex)
    {
        _upgradePopoverStates[rowIndex] = false;
        StateHasChanged();
    }

    // 保持弹窗显示的方法
    private void KeepUpgradePopoverOpen(int rowIndex)
    {
        // 如果是付费用户，不处理弹窗逻辑
        if (IsPayingUser())
        {
            return;
        }

        // 清除延迟隐藏的定时器，保持弹窗显示
        if (_upgradeTimers.ContainsKey(rowIndex))
        {
            _upgradeTimers[rowIndex]?.Dispose();
            _upgradeTimers.Remove(rowIndex);
        }

        // 确保弹窗状态为显示
        _upgradePopoverStates[rowIndex] = true;
    }

    // 升级点击处理方法
    private async Task HandleUpgrade()
    {
        // 清除所有弹窗状态
        _upgradePopoverStates.Clear();
        _popoverPositions.Clear();

        // 跳转到升级页面
        Navigation.NavigateTo("https://stockwe.com/plan", true);

        await Task.CompletedTask;
    }

    // 处理页面变化
    private async Task HandlePageChanged(int newPage)
    {
        await LoadData(new LoadDataArgs { Skip = (newPage - 1) * PageSize, Top = PageSize });
    }

    // 组件销毁时清理资源
    public void Dispose()
    {
        foreach (var timer in _upgradeTimers.Values)
            timer?.Dispose();
        
        _upgradeTimers.Clear();
        _upgradePopoverStates.Clear();
        _mousePositions.Clear();
        _popoverPositions.Clear();
    }
}

<_UIFramework IncludeRowDiv="false" Class="mt-3 mb-3 stock-data" HeaderContentClass="d-flex justify-content-between py-1">
    <HeaderContent>
        <div style="margin-top: 10px;display: flex; align-items: center;color:rgba(245,156,26,1);font-weight:900;margin:6px;">
            <span style="font-weight: 900; font-size: 14px;display: flex; align-items: center;color:rgba(245,156,26,1);">
                <RadzenIcon Icon="preview" Style="margin-right: 4px;" />
                @Title
            </span>
            @if (!IsPayingUser())
            {
                <div style="margin-left: 8px;height: 20px; display: flex; align-items: center;background-color: rgba(245,156,26,1); color: #2d353c; padding: 4px 10px; border-radius: 8px; font-size: 12px; font-weight: bold;">
                    @CObj.UserLang.GetLangValue("限制预览")
                </div>
            }
        </div>
    </HeaderContent>
    <ChildContent>
        <!-- PC端表格 - 使用RzDataTable -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
            <RzDataTable TItem="StockAnalystRating.Data"
                         Slim="true"
                         AllowPaging=true
                         AllowColumnResize="true"
                         Data="@Datas" PageSize="@PageSize"
                         IsLoading="@isLoading"
                         RowRenderCallback="@OnRowRender"
                         Count="@TotalCount" LoadData="@LoadData">

                <!-- 分析师名称 - 移到最左边 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" Sortable="false" Width="220px" Title="@CObj.UserLang.GetLangValue("分析师名称")" Frozen="true">
                    <Template Context="data">
                        @{
                            var rowIndex = Datas.IndexOf(data);
                            var shouldBlur = ShouldBlur(rowIndex);
                            var starRating = AnalystUtil.GetStarRating(data.Accurate);
                        }

                        @if (shouldBlur)
                        {
                            <!-- 触发器 -->
                            <div @onmouseenter="async () => await OnUpgradeMouseEnter(rowIndex)" @onmouseleave="() => OnUpgradeMouseLeave(rowIndex)" style="position: relative; cursor: pointer; width: 200px; height: 39px; overflow: hidden; border-radius: 6px;">
                                <!-- 毛玻璃背景 -->
                                <div style="position: absolute; inset: 0; backdrop-filter: blur(15px); background-color: rgba(15,17,26,0.1); border: 1px solid rgba(255,255,255,0.1);"></div>

                                <!-- 内容区域 -->
                                <div style="position: relative; z-index: 10; display: flex; align-items: center; gap: 14px; padding: 0 12px; height: 100%;">
                                    <img src="@GetAnalystAvatar(rowIndex)" alt="@data.Analyst" style="width: 36px; height: 36px; border-radius: 50%; object-fit: cover; border: 2px solid #ddd; flex-shrink: 0; filter: blur(1px); opacity: 0.8;" />
                                    <div style="background-color: rgba(111, 187, 241, 0.4); width: 130px; height: 23px; border-radius: 3px; filter: blur(0.5px); opacity: 0.7;"></div>
                                </div>

                                <!-- 悬浮覆盖层 -->
                                <div style="position: absolute; inset: 0; z-index: 20; display: flex; flex-direction: column; align-items: center; justify-content: center; gap: 2px; background-color: rgba(0,0,0,0.4);">
                                    <i class="fas fa-lock" style="color: #f59c1a; font-size: 16px;"></i>
                                    <div style="font-size: 10px;">@((MarkupString)AnalystUtil.GetStarHtml(starRating))</div>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div style="position: relative; width: 200px; height: 39px; overflow: hidden;">
                                <div style="display: flex; align-items: center; gap: 10px; padding: 0 12px; height: 100%;">
                                    <img src="@GetAnalystAvatar(rowIndex)" alt="@data.Analyst" style="width: 36px; height: 36px; border-radius: 50%; object-fit: cover; border: 2px solid #ddd; flex-shrink: 0;" />
                                    <div style="position: relative; display: flex; flex-direction: column; gap: 3px; width: 125px; min-width: 125px; justify-content: center;">
                                        <div style="margin-left:8px;font-weight: 600; font-size: 13px; line-height: 1.2; color: white; text-align: left;">@data.Analyst</div>
                                        <div style="margin-left:8px;font-size: 11px; line-height: 1; text-align: left;">
                                            @((MarkupString)AnalystUtil.GetStarHtml(starRating))
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </Template>
                </RadzenDataGridColumn>

                <!-- 机构列 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" Sortable="false" property="AnalystFirm" Width="135px" Title="@CObj.UserLang.GetLangValue("机构")">
                    <Template Context="data">
                        @{
                            var rowIndex = Datas.IndexOf(data);
                            var shouldBlur = ShouldBlur(rowIndex);
                        }

                        @if (shouldBlur)
                        {
                            <!-- 触发器 -->
                            <div @onmouseenter="async () => await OnUpgradeMouseEnter(rowIndex)" @onmouseleave="() => OnUpgradeMouseLeave(rowIndex)" style="position: relative; cursor: pointer; width: 110px; height: 35px; overflow: hidden; border-radius: 4px;">
                                <!-- 毛玻璃背景 -->
                                <div style="position: absolute; inset: 0; backdrop-filter: blur(10px); background-color: rgba(15,17,26,0.1); border: 1px solid rgba(255,255,255,0.1);"></div>

                                <!-- 内容区域 -->
                                <div style="position: relative; z-index: 10; display: flex; align-items: center; padding: 0 8px; height: 100%;">
                                    <div style="background-color: rgba(111, 187, 241, 0.4); width: 80px; height: 16px; border-radius: 3px; filter: blur(0.5px); opacity: 0.7;"></div>
                                </div>

                                <!-- 悬浮覆盖层 -->
                                <div style="position: absolute; inset: 0; z-index: 20; display: flex; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.4);">
                                    <i class="fas fa-lock" style="color: #f59c1a; font-size: 14px;"></i>
                                </div>
                            </div>
                        }
                        else
                        {
                            <div style="position: relative; width: 110px; height: 35px; overflow: hidden;">
                                <div style="display: flex; align-items: center; padding: 0 8px; height: 100%;">
                                    <span>@data.AnalystFirm</span>
                                </div>
                            </div>
                        }
                    </Template>
                </RadzenDataGridColumn>

                <!-- 评级日期 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" property="CreateDate" Sortable="true" Width="100px" Title="@CObj.UserLang.GetLangValue("评级日期")">
                    <Template Context="data">
                        <span>@data.CreateDate?.ToString("MM/dd/yyyy HH:mm")</span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 股票代号 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" property="Stock" Sortable="false" Width="80px" Title="@CObj.UserLang.GetLangValue("代号")">
                    <Template Context="data">
                        <span style="background-color:#212529; color:white; padding:3px 8px; border-radius:4px; display:inline-block;">
                            @data.Stock
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 公司名称 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" Sortable="false" property="Company" Width="150px" Title="@CObj.UserLang.GetLangValue("公司")">
                    <Template Context="data">
                        <span>@data.Company</span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 分析师准确率 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" property="Accurate" Width="100px" Title="@CObj.UserLang.GetLangValue("准确率")">
                    <Template Context="data">
                        @{
                            var rowIndex = Datas.IndexOf(data);
                            var shouldBlur = ShouldBlur(rowIndex);
                        }

                        @if (shouldBlur)
                        {
                            <!-- 触发器 -->
                            <div @onmouseenter="async () => await OnUpgradeMouseEnter(rowIndex)" @onmouseleave="() => OnUpgradeMouseLeave(rowIndex)" style="position: relative; cursor: pointer; width: 100%; height: 39px; overflow: hidden; border-radius: 4px;">
                                <!-- 毛玻璃背景 -->
                                <div style="position: absolute; inset: 0; backdrop-filter: blur(10px); background-color: rgba(15,17,26,0.1); border: 1px solid rgba(255,255,255,0.1);"></div>

                                <!-- 内容区域 -->
                                <div style="position: relative; z-index: 10; display: flex; align-items: center; justify-content: center; height: 100%; padding: 0 8px;">
                                    <div style="background-color: rgba(255, 193, 7, 0.4); width: 70px; height: 20px; border-radius: 3px; filter: blur(0.5px); opacity: 0.7;"></div>
                                </div>

                                <!-- 悬浮覆盖层 -->
                                <div style="position: absolute; inset: 0; z-index: 20; display: flex; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.4);">
                                    <i class="fas fa-lock" style="color: #f59c1a; font-size: 14px;"></i>
                                </div>
                            </div>
                        }
                        else
                        {
                            <span style="display:block; text-align:right;">
                                @AnalystUtil.FormatPercent(data.Accurate)
                            </span>
                        }
                    </Template>
                </RadzenDataGridColumn>

                <!-- 旧目标价 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" property="PrePriceTarget" Width="100px" Title="@CObj.UserLang.GetLangValue("旧目标价")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            $@AnalystUtil.FormatNumber(data.PrePriceTarget)
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 新目标价 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" property="NewPriceTarget" Width="100px" Title="@CObj.UserLang.GetLangValue("新目标价")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            $@AnalystUtil.FormatNumber(data.NewPriceTarget)
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 现价 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" property="CurrentPrice" Width="80px" Title="@CObj.UserLang.GetLangValue("现价")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            $@AnalystUtil.FormatNumber(data.CurrentPrice)
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 离目标价幅度 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" property="UpsideDownside" Width="120px" Title="@CObj.UserLang.GetLangValue("离目标价幅度")">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            @AnalystUtil.FormatPercent(data.UpsideDownside, 2, true)
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 评级变化 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" Sortable="false" property="RatingChange" Width="100px" Title="@CObj.UserLang.GetLangValue("评级变化")">
                    <Template Context="data">
                        @{
                            var ratingChangeType = AnalystUtil.ParseRatingChangeType(data.RatingChange);
                        }
                        <span class="badge @AnalystUtil.GetRatingChangeClass(ratingChangeType)">@AnalystUtil.GetRatingChangeText(ratingChangeType)</span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 旧评级 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" Sortable="false" property="PreRating" Width="90px" Title="@CObj.UserLang.GetLangValue("旧评级")">
                    <Template Context="data">
                        @{
                            var ratingType = AnalystUtil.ParseRatingType(data.PreRating);
                        }
                        <span class="badge @AnalystUtil.GetRatingClass(ratingType)">@AnalystUtil.GetRatingText(ratingType)</span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 新评级 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" Sortable="false" property="NewRating" Width="90px" Title="@CObj.UserLang.GetLangValue("新评级")">
                    <Template Context="data">
                        @{
                            var ratingType = AnalystUtil.ParseRatingType(data.NewRating);
                        }
                        <span class="badge @AnalystUtil.GetRatingClass(ratingType)">@AnalystUtil.GetRatingText(ratingType)</span>
                    </Template>
                </RadzenDataGridColumn>

                <!-- 市值 -->
                @* <RadzenDataGridColumn TItem="StockAnalystRating.Data" property="MarketCap" Width="80px" Title="市值"> *@
                @*     <Template Context="data"> *@
                @*         <span style="display:block; text-align:right;"> *@
                @*             @AnalystUtil.FormatMarketCap(data.MarketCap) *@
                @*         </span> *@
                @*     </Template> *@
                @* </RadzenDataGridColumn> *@

                <!-- 分数 -->
                <RadzenDataGridColumn TItem="StockAnalystRating.Data" property="Score" Width="80px" Title="@CObj.UserLang.GetLangValue("分数")">
                    <Template Context="data">
                        @{
                            var rowIndex = Datas.IndexOf(data);
                            var shouldBlur = ShouldBlur(rowIndex);
                        }

                        @if (shouldBlur)
                        {
                            <!-- 触发器 -->
                            <div @onmouseenter="async () => await OnUpgradeMouseEnter(rowIndex)" @onmouseleave="() => OnUpgradeMouseLeave(rowIndex)" style="position: relative; cursor: pointer; width: 100%; height: 39px; overflow: hidden; border-radius: 4px;">
                                <!-- 毛玻璃背景 -->
                                <div style="position: absolute; inset: 0; backdrop-filter: blur(10px); background-color: rgba(15,17,26,0.1); border: 1px solid rgba(255,255,255,0.1);"></div>

                                <!-- 内容区域 -->
                                <div style="position: relative; z-index: 10; display: flex; align-items: center; justify-content: center; height: 100%; padding: 0 8px;">
                                    <div style="background-color: rgba(75, 192, 192, 0.4); width: 50px; height: 16px; border-radius: 3px; filter: blur(0.5px); opacity: 0.7;"></div>
                                </div>

                                <!-- 悬浮覆盖层 -->
                                <div style="position: absolute; inset: 0; z-index: 20; display: flex; align-items: center; justify-content: center; background-color: rgba(0,0,0,0.4);">
                                    <i class="fas fa-lock" style="color: #f59c1a; font-size: 14px;"></i>
                                </div>
                            </div>
                        }
                        else
                        {
                            <span style="display:block; text-align:right;">
                                @(data.Score?.ToString("N2"))
                            </span>
                        }
                    </Template>
                </RadzenDataGridColumn>
            </RzDataTable>

            <!-- 统一的升级弹窗 -->
            @for (int i = 0; i < Datas.Count; i++)
            {
                var currentIndex = i;
                @if (!IsPayingUser() && _upgradePopoverStates.TryGetValue(currentIndex, out bool upgradePopoverOpen) && upgradePopoverOpen && ShouldBlur(currentIndex))
                {
                    <div @onmouseenter="() => KeepUpgradePopoverOpen(currentIndex)" @onmouseleave="() => OnUpgradeMouseLeave(currentIndex)"
                         style="@(_popoverPositions.TryGetValue(currentIndex, out var position) ? position : "position: fixed; top: 200px; left: 420px;") z-index: 99999; padding: 20px; background: linear-gradient(135deg, #2d353c 0%, #1a1e23 100%); color: white; border-radius: 12px; font-size: 14px; width: 280px; box-shadow: 0 10px 30px rgba(0,0,0,0.6); border: 1px solid rgba(245,156,26,0.5); backdrop-filter: blur(12px);">
                        <div style="margin-bottom: 16px;">
                            <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: white;">@CObj.UserLang.GetLangValue("开通VIP解锁更多")</h3>
                        </div>
                        <div style="margin-bottom: 20px;">
                            <div style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px;">
                                <i class="fas fa-check-circle" style="color: #f59c1a; font-size: 16px; margin-top: 2px; flex-shrink: 0;"></i>
                                <span style="font-size: 14px; line-height: 1.4; color: #e0e6ed;">@CObj.UserLang.GetLangValue("解锁全部分析师准确率")</span>
                            </div>
                            <div style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px;">
                                <i class="fas fa-check-circle" style="color: #f59c1a; font-size: 16px; margin-top: 2px; flex-shrink: 0;"></i>
                                <span style="font-size: 14px; line-height: 1.4; color: #e0e6ed;">@CObj.UserLang.GetLangValue("目标价细节")</span>
                            </div>
                            <div style="display: flex; align-items: flex-start; gap: 12px;">
                                <i class="fas fa-check-circle" style="color: #f59c1a; font-size: 16px; margin-top: 2px; flex-shrink: 0;"></i>
                                <span style="font-size: 14px; line-height: 1.4; color: #e0e6ed;">@CObj.UserLang.GetLangValue("推荐股票和期权操作")</span>
                            </div>
                            <div style="display: flex; align-items: flex-start; gap: 12px;">
                                <i class="fas fa-check-circle" style="color: #f59c1a; font-size: 16px; margin-top: 2px; flex-shrink: 0;"></i>
                                <span style="font-size: 14px; line-height: 1.4; color: #e0e6ed;">@CObj.UserLang.GetLangValue("AI选股、主力资金流向")</span>
                            </div>
                        </div>
                        <div>
                            <button @onclick="HandleUpgrade" class="btn btn-warning" style="width: 100%; background-color: #f59c1a; border-color: #f59c1a; color: white; font-weight: 600; padding: 10px 0; border-radius: 8px; font-size: 14px;">
                                @CObj.UserLang.GetLangValue("立即订阅")
                            </button>
                        </div>
                    </div>
                }
            }
        </MudHidden>

        <!-- 移动端表格 - 使用组件化布局 -->
        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <_MobileAnalystRatingTable Data="@Datas"
                                       IsLoading="@isLoading"
                                       TotalCount="@TotalCount"
                                       PageSize="@PageSize"
                                       CurrentPage="@(page)"
                                       EnableBlurFeature="true"
                                       GetAnalystAvatar="@GetAnalystAvatar"
                                       ShouldBlur="@ShouldBlur"
                                       OnPageChanged="@HandlePageChanged"
                                       OnMobileUpgradeClick="@OnMobileUpgradeClick" />

            <!-- 移动端的升级弹窗 -->
            @for (int i = 0; i < Datas.Count; i++)
            {
                var currentIndex = i;
                @if (!IsPayingUser() && _upgradePopoverStates.TryGetValue(currentIndex, out bool upgradePopoverOpen) && upgradePopoverOpen && ShouldBlur(currentIndex))
                {
                    <div @onclick="() => CloseMobileUpgradePopover(currentIndex)" @ontouchstart="() => CloseMobileUpgradePopover(currentIndex)"
                         style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; z-index: 99999; background-color: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; padding: 20px;">
                        <div @onclick:stopPropagation="true" @ontouchstart:stopPropagation="true"
                             style="background: linear-gradient(135deg, #2d353c 0%, #1a1e23 100%); color: white; border-radius: 12px; padding: 20px; width: 100%; max-width: 320px; box-shadow: 0 10px 30px rgba(0,0,0,0.6); border: 1px solid rgba(245,156,26,0.5);">
                            <div style="margin-bottom: 16px;">
                                <h3 style="margin: 0; font-size: 16px; font-weight: 600; color: white;">@CObj.UserLang.GetLangValue("开通VIP解锁更多")</h3>
                            </div>
                            <div style="margin-bottom: 20px;">
                                <div style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px;">
                                    <i class="fas fa-check-circle" style="color: #f59c1a; font-size: 16px; margin-top: 2px; flex-shrink: 0;"></i>
                                    <span style="font-size: 14px; line-height: 1.4; color: #e0e6ed;">@CObj.UserLang.GetLangValue("解锁全部分析师准确率")</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px; margin-bottom: 12px;">
                                    <i class="fas fa-check-circle" style="color: #f59c1a; font-size: 16px; margin-top: 2px; flex-shrink: 0;"></i>
                                    <span style="font-size: 14px; line-height: 1.4; color: #e0e6ed;">@CObj.UserLang.GetLangValue("目标价细节")</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <i class="fas fa-check-circle" style="color: #f59c1a; font-size: 16px; margin-top: 2px; flex-shrink: 0;"></i>
                                    <span style="font-size: 14px; line-height: 1.4; color: #e0e6ed;">@CObj.UserLang.GetLangValue("推荐股票和期权操作")</span>
                                </div>
                                <div style="display: flex; align-items: flex-start; gap: 12px;">
                                    <i class="fas fa-check-circle" style="color: #f59c1a; font-size: 16px; margin-top: 2px; flex-shrink: 0;"></i>
                                    <span style="font-size: 14px; line-height: 1.4; color: #e0e6ed;">@CObj.UserLang.GetLangValue("AI选股、主力资金流向")</span>
                                </div>
                            </div>
                            <div style="display: flex; gap: 12px;">
                                <button @onclick="() => CloseMobileUpgradePopover(currentIndex)" style="flex: 1; background-color: transparent; border: 1px solid #6c757d; color: #6c757d; font-weight: 600; padding: 10px 0; border-radius: 8px; font-size: 14px;">
                                    @CObj.UserLang.GetLangValue("取消")
                                </button>
                                <button @onclick="HandleUpgrade" style="flex: 1; background-color: #f59c1a; border: 1px solid #f59c1a; color: white; font-weight: 600; padding: 10px 0; border-radius: 8px; font-size: 14px;">
                                    @CObj.UserLang.GetLangValue("立即订阅")
                                </button>
                            </div>
                        </div>
                    </div>
                }
            }
        </MudHidden>
    </ChildContent>
</_UIFramework>

<script>
    // 立即获取当前鼠标位置
    window.getCurrentMousePosition = function () {
        // 直接返回最后记录的鼠标位置
        return [window.lastMouseX || 420, window.lastMouseY || 200];
    };

    // 获取视口尺寸
    window.getViewportSize = function () {
        return [
            window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth,
            window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight
        ];
    };

    window.getMousePosition = function () {
        return new Promise((resolve) => {
            const handler = function(event) {
                document.removeEventListener('mousemove', handler);
                resolve([event.clientX, event.clientY]);
            };

            // 如果鼠标正在移动，立即获取位置
            document.addEventListener('mousemove', handler);

            // 如果100ms内没有鼠标移动，使用当前记录的位置或默认位置
            setTimeout(() => {
                document.removeEventListener('mousemove', handler);
                resolve([window.lastMouseX || 420, window.lastMouseY || 200]);
            }, 100);
        });
    };

    // 持续跟踪鼠标位置 - 提高更新频率
    document.addEventListener('mousemove', function(event) {
        window.lastMouseX = event.clientX;
        window.lastMouseY = event.clientY;
    });

    // 页面加载时初始化鼠标位置
    document.addEventListener('DOMContentLoaded', function() {
        window.lastMouseX = 420;
        window.lastMouseY = 200;
    });
</script>