@using MudBlazor
@using System.Globalization

@using eZeroCore.Web.Stk.Data

@code {
    [CascadingParameter]
    IMudDialogInstance? MudDialog { get; set; }

    [Parameter]
    public BlzHelperObj? CObj { get; set; }
    
    [Parameter]
    public eZeroCore.Web.Stk.Data.Dt_StockOption.Filters CurrentFilter { get; set; } = new();

    [Parameter]
    public DateTime? DateStart { get; set; }
    
    [Parameter]
    public DateTime? DateEnd { get; set; }

    // 滑块默认范围定义
    private readonly (double s, double e) ivol_default = (0, 800);
    private readonly (double s, double e) size_default = (0, 500000);
    private readonly (double s, double e) score_default = (0, 130);

    // 滑块当前值
    (double s, double e) ivol = (0, 800);
    (double s, double e) size = (0, 500000);
    (double s, double e) score = (0, 130);

    // 时间选择器属性
    DateRange ExpireDateRange
    {
        get => new(CurrentFilter.ExpireDateStart, CurrentFilter.ExpireDateEnd);
        set
        {
            if (value != null)
            {
                CurrentFilter.ExpireDateStart = value.Start;
                CurrentFilter.ExpireDateEnd = value.End;
                StateHasChanged();
            }
        }
    }

    // 组件引用
    private E_CkBoxList _rd_tendency;
    private E_CkBoxList _rd_cP;
    private E_CkBoxList _rd_direction;
    private E_CkBoxList _rd_inoutmoney;
    private E_CkBoxList _rd_unique;

    protected override async Task OnInitializedAsync()
    {
        // 根据CurrentFilter状态初始化滑块值
        UpdateSlidersFromCurrentFilter();
        
        await base.OnInitializedAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        // 当CurrentFilter参数变化时，更新滑块值
        UpdateSlidersFromCurrentFilter();
        
        await base.OnParametersSetAsync();
    }

    // 根据CurrentFilter更新滑块值
    private void UpdateSlidersFromCurrentFilter()
    {
        ivol = (CurrentFilter.ivol, CurrentFilter.ivolMax == 0 ? (int)ivol_default.e : CurrentFilter.ivolMax);
        size = (CurrentFilter.size, CurrentFilter.sizeMax == 0 ? (int)size_default.e : CurrentFilter.sizeMax);
        score = (CurrentFilter.score, CurrentFilter.scoreMax == 0 ? (int)score_default.e : CurrentFilter.scoreMax);
    }

    // 趋势选择变化处理
    private async Task OnTendencyChanged(List<string> values)
    {
        List<int> intValues = values
            .Select(s => int.TryParse(s, out int result) ? result : (int?)null)
            .Where(i => i.HasValue)
            .Select(i => i!.Value)
            .ToList();
        CurrentFilter.Tendencys = intValues;
    }

    // C/P选择变化处理
    private async Task OnCPChanged(List<string> values)
    {
        var selectedValue = values?.FirstOrDefault() ?? "0";
        CurrentFilter.cP = eZeroCore.ENumeric.ConvertToInt(selectedValue);
    }

    // 成交价选择变化处理
    private async Task OnDirectionChanged(List<string> values)
    {
        var selectedValue = values?.FirstOrDefault() ?? "0";
        CurrentFilter.direction = selectedValue;
    }

    // 价内外选择变化处理
    private async Task OnInOutMoneyChanged(List<string> values)
    {
        var selectedValue = values?.FirstOrDefault() ?? "-2";
        CurrentFilter.inoutmoney = (eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum)eZeroCore.ENumeric.ConvertToInt(selectedValue);
    }

    // 是否平仓选择变化处理
    private async Task OnUniqueChanged(List<string> values)
    {
        var selectedValue = values?.FirstOrDefault() ?? "0";
        CurrentFilter.unique = eZeroCore.ENumeric.ConvertToInt(selectedValue);
    }

    // 滑块变化事件处理方法
    private async Task OnIvolChange((double s, double e) value)
    {
        ivol = value;
        CurrentFilter.ivol = (int)ivol.s;
        CurrentFilter.ivolMax = (int)ivol.e;
    }

    private async Task OnSizeChange((double s, double e) value)
    {
        size = value;
        CurrentFilter.size = (int)size.s;
        CurrentFilter.sizeMax = (int)size.e;
    }

    private async Task OnScoreChange((double s, double e) value)
    {
        score = value;
        CurrentFilter.score = (int)score.s;
        CurrentFilter.scoreMax = (int)score.e;
    }

    private async Task OnStocksChanged(List<string> stocks)
    {
        CurrentFilter.stocks = stocks ?? new List<string>();
    }

    private async Task OnExcludeStocksChanged(List<string> stocks)
    {
        CurrentFilter.searchSkip = stocks ?? new List<string>();
    }

    private async Task ResetFilter()
    {
        // 重置所有选项到默认状态
        CurrentFilter.trend = 0;
        CurrentFilter.tendency = 0;
        CurrentFilter.Tendencys = [];
        CurrentFilter.premium = 0;
        CurrentFilter.cP = 0;
        CurrentFilter.direction = "0";
        CurrentFilter.inoutmoney = eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.全部;
        CurrentFilter.unique = 0;
        CurrentFilter.searchSkip = [];
        CurrentFilter.stocks = [];

        // 重置权利金范围
        CurrentFilter.premium = 0;
        CurrentFilter.premiumMax = 0;
        // 重置行权价溢价范围
        CurrentFilter.absStockPrice = null;
        CurrentFilter.absStockPriceTMax = null;
        // 重置行权价范围
        CurrentFilter.MinStrike = 0;
        CurrentFilter.MaxStrike = 0;
        // 重置到期天数范围
        CurrentFilter.dayExpire = 0;
        CurrentFilter.dayExpireMax = 0;

        ivol = ivol_default;
        CurrentFilter.ivol = (int)ivol_default.s;
        CurrentFilter.ivolMax = (int)ivol_default.e;

        size = size_default;
        CurrentFilter.size = (int)size_default.s;
        CurrentFilter.sizeMax = (int)size_default.e;

        score = score_default;
        CurrentFilter.score = (int)score.s;
        CurrentFilter.scoreMax = (int)score.e;

        CurrentFilter.MinCVolORest = 0;
        CurrentFilter.MinStrike = 0;
        
        // 重置E_CkBoxList组件
        if (_rd_tendency != null)
        {
            await _rd_tendency.ClearSelections();
        }
        if (_rd_cP != null)
        {
            await _rd_cP.ClearSelections();
        }
        if (_rd_direction != null)
        {
            await _rd_direction.ClearSelections();
        }
        if (_rd_inoutmoney != null)
        {
            await _rd_inoutmoney.ClearSelections();
        }
        if (_rd_unique != null)
        {
            await _rd_unique.ClearSelections();
        }
        
        StateHasChanged();
    }

    // 趋势选项
    List<string> GetTendency_TitleValue(bool isTitle = true)
    {
        List<string> list = [];
        if (isTitle)
        {
            list.Add("全部");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方很看涨}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方看涨}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方很看跌}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方看跌}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方很看涨}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方看涨}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方很看跌}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方看跌}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.接近买价成交}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.接近卖价成交}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.中性}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.未知}");
        }
        else
        {
            list.Add("0");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方很看涨)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方看涨)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方很看跌)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.买方看跌)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方很看涨)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方看涨)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方很看跌)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.卖方看跌)}");

            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.接近买价成交)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.接近卖价成交)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.中性)}");
            list.Add($"{Convert.ToInt32(eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.SentimentEnum.未知)}");
        }
        return list;
    }

    // C/P选项
    List<string> GetRd_cP_TitleValue(bool isTitle = true)
    {
        List<string> list = [];
        if (isTitle)
        {
            list.Add("全部");
            list.Add("CALL");
            list.Add("PUT");
        }
        else
        {
            list.Add("0");
            list.Add("1");
            list.Add("2");
        }
        return list;
    }

    // 成交价选项
    List<string> GetRd_direction_TitleValue(bool isTitle = true)
    {
        List<string> list = [];
        if (isTitle)
        {
            list.Add("全部");
        }
        else
        {
            list.Add("0");
        }
        list.Add("高于卖价");
        list.Add("就在买价");
        list.Add("就在卖价");
        list.Add("接近买价");
        list.Add("接近卖价");
        list.Add("低于买价");
        list.Add("低于卖价");
        return list;
    }

    // 价内外选项
    List<string> GetRd_inoutmoney_TitleValue(bool isTitle = true)
    {
        List<string> list = [];
        if (isTitle)
        {
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.全部}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.价内}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.等价}");
            list.Add($"{eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.InOutEnum.价外}");
        }
        else
        {
            list.Add("-2");
            list.Add("-1");
            list.Add("0");
            list.Add("1");
        }
        return list;
    }

    // 是否平仓选项
    List<string> GetRd_unique_TitleValue(bool isTitle = true)
    {
        List<string> list = [];
        if (isTitle)
        {
            list.Add("全部");
            list.Add("是");
            list.Add("否");
        }
        else
        {
            list.Add("0");
            list.Add("1");
            list.Add("2");
        }
        return list;
    }

    private async Task ApplyFilter()
    {
        if (MudDialog is not null)
        {
            // 构建结果对象，包含过滤器和时间范围
            var result = new FilterResult
            {
                Filter = CurrentFilter,
                DateStart = DateStart,
                DateEnd = DateEnd
            };
            MudDialog.Close(DialogResult.Ok(result));
        }
    }

    private void Cancel()
    {
        if (MudDialog is not null)
        {
            MudDialog.Cancel();
        }
    }

    // 结果类
    public class FilterResult
    {
        public eZeroCore.Web.Stk.Data.Dt_StockOption.Filters Filter { get; set; } = new();
        public DateTime? DateStart { get; set; }
        public DateTime? DateEnd { get; set; }
    }

    string CssCard = "card m-1 mb-2 border border-warning-subtle d-flex flex-row align-items-center";
    string CssHeader = "flex-shrink-0 w-110px text-end me-2 py-3";
    string CssBody = "card-body flex-grow-1 bg-dark-subtle border-start p-0 px-1";

    int? Db_Filter_Premium
    {
        get
        {
            int i = CurrentFilter.premium;
            if (i == 0)
            {
                return null;
            }
            else
            {
                return i;
            }
        }
        set
        {
            if (value is null)
            {
                CurrentFilter.premium = 0;
            }
            else
            {
                CurrentFilter.premium = value.Value;
            }
        }
    }

    int? Db_Filter_PremiumMax
    {
        get
        {
            int i = CurrentFilter.premiumMax;
            if (i == 0)
            {
                return null;
            }
            else
            {
                return i;
            }
        }
        set
        {
            if (value is null)
            {
                CurrentFilter.premiumMax = 0;
            }
            else
            {
                CurrentFilter.premiumMax = value.Value;
            }
        }
    }

    double? DbAbsStockPrice
    {
        get { return CurrentFilter.absStockPrice; }
        set
        {
            CurrentFilter.absStockPrice = value;
        }
    }
    double? DbAbsStockPriceMax
    {
        get { return CurrentFilter.absStockPriceTMax; }
        set
        {
            CurrentFilter.absStockPriceTMax = value;
        }
    }

    int? MinStrike
    {
        get { return CurrentFilter.MinStrike == 0 ? null : CurrentFilter.MinStrike; }
        set
        {
            CurrentFilter.MinStrike = value!.Value;
        }
    }
    int? MaxStrike
    {
        get { return CurrentFilter.MaxStrike == 0 ? null : CurrentFilter.MaxStrike; }
        set
        {
            CurrentFilter.MaxStrike = value!.Value;
        }
    }

    int? Openinterest
    {
        get { return CurrentFilter.openinterest == 0 ? null : CurrentFilter.openinterest; }
        set
        {
            CurrentFilter.openinterest = value!.Value;
        }
    }
    string? OpeninterestMax
    {
        get
        {
            if (CurrentFilter.openinterestMax is null || CurrentFilter.openinterestMax == 0)
            {
                return "∞";
            }
            return CurrentFilter.openinterestMax.ToString();
        }
        set
        {
            CurrentFilter.openinterestMax = eZeroCore.ENumeric.ConvertToInt(value);
            if (CurrentFilter.openinterestMax <= CurrentFilter.openinterest)
            {
                CurrentFilter.openinterestMax = null;
            }
        }
    }

    string AddStocks
    {
        get
        {
            return string.Join(',', CurrentFilter.stocks);
        }
        set
        {
            CurrentFilter.stocks = GetSplit(value).Distinct().ToList();
        }
    }
    string SearchSkip
    {
        get
        {
            return string.Join(',', CurrentFilter.searchSkip);
        }
        set
        {
            CurrentFilter.searchSkip = GetSplit(value).Distinct().ToList();
        }
    }

    string[] GetSplit(string currentInput)
    {
        if (!string.IsNullOrWhiteSpace(currentInput))
        {
            // 拆分输入，假设使用逗号、分号、空格作为分隔符
            currentInput = currentInput.Replace("，", ",");
            currentInput = currentInput.Replace("；", ";");
            return currentInput
                .Split(new[] { ',', ';', ' ' }, StringSplitOptions.RemoveEmptyEntries);
        }
        return [];
    }
}

<MudDialogContent>
    <ChildContent>
        <div class="mb-3">
            <h6 class="mb-0">
                <i class="fal fa-filter me-2"></i>
                期权异动过滤器
            </h6>
        </div>

        <!-- 滚动容器 - 限制高度 -->
        <div style="max-height: 65vh; overflow-y: auto; overflow-x: hidden; padding-right: 8px;">

            <!-- 过滤器名称 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    过滤器名称
                </div>
                <div class="@CssBody">
                    <input @bind-value=@CurrentFilter.FilterName maxlength="8"
                           class="form-control form-control-sm Upper" type="text" placeholder="命名此自定义过滤器">
                </div>
            </div>

            <!-- 趋势 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    趋势
                </div>
                <div class="@CssBody">
                    <E_CkBoxList SetDefaultSelectedIndexValue="@CurrentFilter.Tendencys" @ref=@_rd_tendency OnSelectionChanged="@OnTendencyChanged" SelectFirstClearOther="true"
                                 Titles="GetTendency_TitleValue()"
                                 Values="GetTendency_TitleValue(false)" />
                </div>
            </div>

            <!-- C/P -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    C/P
                </div>
                <div class="@CssBody">
                    <E_CkBoxList UseRadioButtons="true" SetDefaultSelectedValue="@CurrentFilter.cP.ToString()" @ref=@_rd_cP OnSelectionChanged="@OnCPChanged"
                                 Titles="GetRd_cP_TitleValue()"
                                 Values="GetRd_cP_TitleValue(false)" />
                </div>
            </div>

            <!-- 成交价 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    成交价
                </div>
                <div class="@CssBody">
                    <E_CkBoxList UseRadioButtons="true" SetDefaultSelectedValue="@CurrentFilter.direction.ToString()" @ref=@_rd_direction OnSelectionChanged="@OnDirectionChanged"
                                 Titles="GetRd_direction_TitleValue()"
                                 Values="GetRd_direction_TitleValue(false)" />
                </div>
            </div>

            <!-- 价内外 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    价内外
                </div>
                <div class="@CssBody">
                    <E_CkBoxList UseRadioButtons="true" SetDefaultSelectedValue="@(((int)CurrentFilter.inoutmoney).ToString())" @ref=@_rd_inoutmoney OnSelectionChanged="@OnInOutMoneyChanged"
                                 Titles="GetRd_inoutmoney_TitleValue()"
                                 Values="GetRd_inoutmoney_TitleValue(false)" />
                </div>
            </div>

            <!-- 是否平仓 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    是否平仓
                </div>
                <div class="@CssBody">
                    <E_CkBoxList UseRadioButtons="true" SetDefaultSelectedValue="@CurrentFilter.unique.ToString()" @ref=@_rd_unique OnSelectionChanged="@OnUniqueChanged"
                                 Titles="GetRd_unique_TitleValue()"
                                 Values="GetRd_unique_TitleValue(false)" />
                </div>
            </div>

            <!-- 权利金 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    权利金
                </div>
                <div class="@CssBody">
                    <div class="mb-2">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">最小值</span>
                            <input @bind-value=@Db_Filter_Premium type="text" class="form-control" placeholder="0">
                        </div>
                    </div>
                    <div>
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">最大值</span>
                            <input @bind-value=@Db_Filter_PremiumMax type="text" class="form-control" placeholder="无限制">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行权价溢价 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    行权价溢价
                </div>
                <div class="@CssBody">
                    <div class="mb-2">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">最小值</span>
                            <input @bind-value=@DbAbsStockPrice type="text" class="form-control" placeholder="0.0">
                        </div>
                    </div>
                    <div>
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">最大值</span>
                            <input @bind-value=@DbAbsStockPriceMax type="text" class="form-control" placeholder="无限制">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 行权价范围 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    行权价范围
                </div>
                <div class="@CssBody">
                    <div class="mb-2">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">最小值</span>
                            <input @bind-value=@MinStrike type="text" class="form-control" placeholder="0">
                        </div>
                    </div>
                    <div>
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">最大值</span>
                            <input @bind-value=@MaxStrike type="text" class="form-control" placeholder="无限制">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 未平仓数量 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    未平仓数量
                </div>
                <div class="@CssBody">
                    <div class="mb-2">
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">最小值</span>
                            <input @bind-value=@Openinterest type="text" class="form-control" placeholder="0">
                        </div>
                    </div>
                    <div>
                        <div class="input-group input-group-sm">
                            <span class="input-group-text">最大值</span>
                            <input @bind-value=@OpeninterestMax type="text" class="form-control" placeholder="∞">
                        </div>
                    </div>
                </div>
            </div>

            <!-- 到期天数 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    到期天数
                </div>
                <div class="@CssBody">
                    <MudDateRangePicker Culture="@CultureInfo.GetCultureInfo("zh-Hans")"
                                        PickerVariant="PickerVariant.Dialog"
                                        ShowToolbar="false"
                                        TitleDateFormat="yyyy,MM,dd"
                                        FirstDayOfWeek="DayOfWeek.Sunday"
                                        Clearable="true"
                                        DateFormat="MM/dd/yy"
                                        @bind-DateRange="ExpireDateRange" />
                </div>
            </div>

            <!-- 隐含波动率 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    隐含波动率
                </div>
                <div class="@CssBody">
                    <EZSilder MinMaxValue=ivol_default
                              SetValue=ivol
                              Step="10"
                              OnChangeAfter=OnIvolChange />
                </div>
            </div>

            <!-- 合约数 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    合约数
                </div>
                <div class="@CssBody">
                    <EZSilder MinMaxValue=size_default
                              SetValue=size
                              Step="10"
                              OnChangeAfter=OnSizeChange />
                </div>
            </div>

            <!-- 分数 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    分数
                </div>
                <div class="@CssBody">
                    <EZSilder MinMaxValue=score_default
                              SetValue=score
                              Step="1"
                              OnChangeAfter=OnScoreChange />
                </div>
            </div>

            <!-- 包含股票 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    包含股票
                </div>
                <div class="@CssBody">
                    <input @bind-value=@AddStocks class="form-control" type="text" placeholder="包含代号（用美式,分割）">
                </div>
            </div>

            <!-- 排除股票 -->
            <div class="@CssCard">
                <div class="@CssHeader">
                    排除股票
                </div>
                <div class="@CssBody">
                    <input @bind-value=@SearchSkip class="form-control" type="text" placeholder="排除代号（用美式,分割）">
                </div>
            </div>

        </div>
    </ChildContent>
    
    <MudDialogActions>
        <div class="d-flex gap-2 w-100" style="margin-right:80px">
            <MudButton Variant="Variant.Outlined" 
                       Color="Color.Warning" 
                       OnClick="ResetFilter" 
                       StartIcon="@Icons.Material.Filled.Refresh"
                       Class="flex-fill">
                重置
            </MudButton>
            <MudButton Variant="Variant.Filled" 
                       Color="Color.Warning" 
                       OnClick="ApplyFilter" 
                       StartIcon="@Icons.Material.Filled.FilterAlt"
                       Class="flex-fill">
                应用过滤
            </MudButton>
            <MudButton Variant="Variant.Text" 
                       OnClick="Cancel"
                       Class="flex-fill">
                取消
            </MudButton>
        </div>
    </MudDialogActions>
</MudDialogContent> 