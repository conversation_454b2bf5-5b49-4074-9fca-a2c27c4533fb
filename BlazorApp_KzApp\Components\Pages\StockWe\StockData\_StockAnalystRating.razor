﻿@using System.Text
@using System.Globalization
@using eZeroCore.Web.Stk
@using System.Text.Json
@using System.Text.Json.Serialization
@inject IJSRuntime JS

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }
    string DomId = $"Stock_Analyst_Rating_{nameof(_StockAnalystRating)}";
    string DomPieId = $"Stock_Analyst_Rating_Pie{nameof(_StockAnalystRating)}";

    string className = "d-xl-flex justify-content-between align-items-center mt-3";
    string SearchString { get; set; } = "";

    [CascadingParameter]
    public string DefaultStock { get; set; } = "";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await Init(DefaultStock);
        }
    }

    eZeroCore.Web.Stk.Data.Stock_Analyst_Rating.BarData barData = new();
    public async Task Init(string stock="")
    {
        await Generic.LoadingDom(JS, DomId);
        SearchString = stock;
        barData = await eZeroCore.Web.Stk.Data.Stock_Analyst_Rating.GetChart_BarAsync(stock);
        await JS.InvokeVoidAsync($"Stock_Analyst_Rating.init", DomId, barData.DatesJson, barData.RatingsJson, barData.SeriesDataJson,barData.PrcieDataJson);
        await JS.InvokeVoidAsync($"Stock_Analyst_Pie.init", DomPieId, stock.ToUpper(), barData.SeriesDataJson);
        await Generic.LoadingDom(JS, DomId, false);
        await Task.CompletedTask;
        StateHasChanged();
    }
}

<_UIFramework FullWidth="true" HeaderContentClass="@className" Class="bg-body">
    <HeaderContent>
        <div class="py-2 text-center">
            <b>
                评级分布
            </b>
            <span class="badge badge-phoenix badge-phoenix-info py-1">
                @SearchString
            </span>
        </div>
        <div></div>
    </HeaderContent>
    <ChildContent>
        <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum.none" Class="col-12 col-md-4">
            <div id="@DomPieId" style="width: 100%; height:360px;" class="eChartDom"></div>
        </_UI_Col_Framework>
        <_UI_Col_Framework NumberCols="_UI_Col_Framework.NumberColsEnum.none" Class="col-12 col-md-8">
            <div id="@DomId" style="width: 100%; height:360px;" class="eChartDom"></div>
        </_UI_Col_Framework>
    </ChildContent>
</_UIFramework>

