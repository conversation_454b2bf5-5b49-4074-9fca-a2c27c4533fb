﻿@inject IJSRuntime JS

@code {
    [Parameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public EventCallback<bool> NotifySendStkData { get; set; }

    [Parameter]
    public EventCallback<string> OnClickUserName { get; set; }

    [Parameter]
    public EventCallback OnTickMin { get; set; }

    async Task OpenChat(bool open)
    {
        await JS.InvokeVoidAsync("eSc.OpenChatStk", open ? true : false);
    }
    /*
     * <a class="btn btn-danger rounded-circle icon-md show fixed-bottom chatBoxOffcanvasIconBtn"
       data-bs-toggle="offcanvas" href="#offcanvasChatBox" @onclick="()=>{_=OpenChat(true);}">
    <i class="fas fa-comment-alt-smile"></i>
    </a>
    */
}


<!-- <a class="btn btn-danger rounded-circle icon-md show fixed-bottom chatBoxOffcanvasIconBtn"
    target="_blank" href="/chat">
    <i class="fas fa-comment-alt-smile"></i>
</a> -->


<a class="rounded-circle show fixed-bottom chatBoxOffcanvasIconBtn chat-icon-custom"
   style="margin-bottom:50px;"
   target="_blank" href="/chat">
   
    <i class="fas fa-comment-alt-smile"></i>
</a>

@*
<div class="offcanvas offcanvas-end" style="@(CObj.IsMobile?"width:100%;":"width:50%;max-width:1280px;") z-index:1500;" tabindex="-1" id="offcanvasChatBox">
    <div class="offcanvas-body p-1 overflow-hidden">

        <ChatMainFramework CObj=@CObj OnTickMin="@OnTickMin" OffcanvasPanelId="offcanvasChatBox" NotifySendStkData="@NotifySendStkData" LeftRoomList=false OffcanvasFullHeight=true OnClickUserName=@OnClickUserName />

    </div>
</div>
*@