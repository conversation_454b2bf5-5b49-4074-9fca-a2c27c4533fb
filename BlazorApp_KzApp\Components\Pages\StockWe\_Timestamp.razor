﻿@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public string AddString { get; set; } = "";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender) await TimerTaskEvent();
    }

    string nowTime { get; set; } = "";
    public async Task TimerTaskEvent(DateTime? date = null)
    {
        if (date is null)
        {
            date = DateTime.UtcNow;
        }
        nowTime = await Task.FromResult(CObj.GetUserDatetTimeString(date, timeZoneId: DateTimeConvert.TimeZoneId.New_York, ShowYear: false));
        StateHasChanged();
    }

    [Parameter]
    public bool Inline { get; set; }

    [Parameter]
    public string ClassName { get; set; } = "";
}

@if (!string.IsNullOrEmpty(nowTime))
{
    <small class="FontNumber d-@(Inline?"inline":"block") badge badge-phoenix badge-phoenix-info @(string.IsNullOrEmpty(AddString)?"w-110px":"") @ClassName">
        <span class="text-body d-none">
            @CObj.UserZoneId
        </span>
        <span class="@(string.IsNullOrEmpty(AddString)?"":"me-2")">
            @AddString
        </span>
        @nowTime
    </small>
}