﻿@using Microsoft.AspNetCore.Components.Authorization
@rendermode InteractiveServer
@code {
    private Type DefaultLayout = typeof(_StkLayout.MainLayout); // 默认 Layout
    
    private string BasePath
    {
        get
        {
            return $"{eZeroCore.AppSettings.SystemMode}";
        }
    }
}
<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Program).Assembly">
        <Found Context="routeData">
            <RouteView RouteData="routeData" DefaultLayout="@DefaultLayout" />
            <FocusOnNavigate RouteData="routeData" Selector="body" />
        </Found>
        <NotFound>
            <h3>404</h3>
        </NotFound>
    </Router>
</CascadingAuthenticationState>

