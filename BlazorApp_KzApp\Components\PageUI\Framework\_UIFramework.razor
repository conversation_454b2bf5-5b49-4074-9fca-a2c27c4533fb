@inject BlzHelperObj CObj
@code {    

    [Parameter]
    public RenderFragment? HeaderContent { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string HeaderContentClass { get; set; } = "col col-md-auto ms-auto";

    [Parameter]
    public string Title { get; set; } = "";

    [Parameter]
    public string TitleClass { get; set; } = "";

    [Parameter]
    public string Class { get; set; } = "";

    [Parameter]
    public string RowClass { get; set; } = "g3";

    [Parameter]
    public string BodyClass { get; set; } = "";

    [Parameter]
    public bool IncludeRowDiv { get; set; } = true;

    [Parameter]
    public bool FullWidth { get; set; }

    string CssClass = "card shadow-none border";

    public string ContentDomId { get; set; } = Guid.NewGuid().ToString();

    protected override Task OnInitializedAsync()
    {
        if (FullWidth)
        {
            CssClass = "mx-n4 px-4 mx-lg-n6 bg-body-emphasis border-top border-bottom border-translucent position-relative top-1";
        }
        return base.OnInitializedAsync();
    }

}

<div class="@CssClass @Class">
    @if (HeaderContent is not null || !string.IsNullOrWhiteSpace(Title))
    {
        <div class="card-header p-2 py-0 UIFrameworkHeader border-bottom">
            <div class="row @RowClass justify-content-between align-items-center">
                @if (!string.IsNullOrEmpty(Title))
                {
                    <div class="col-12 col-md">
                        <h5 class="text-body mb-0 ms-2 @TitleClass">
                            @Title
                        </h5>
                    </div>
                }
                <div class="@HeaderContentClass">
                    @if (HeaderContent is not null)
                    {
                        @HeaderContent
                    }
                </div>
            </div>
        </div>
    }
    <div id="@ContentDomId" class="card-body @(string.IsNullOrEmpty(BodyClass)?"p-3":BodyClass)">
        @if (IncludeRowDiv)
        {
            <div class="row @RowClass">
                @ChildContent
            </div>
        }
        else
        {
            @ChildContent
        }
    </div>
</div>