﻿@using ZycCore.Models
@using ZycCore.Models.Dto
@using <PERSON><PERSON><PERSON>
@inject NavigationManager NavigationManagerObj
@inject IJSRuntime JS

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public string StockCodeString { get; set; } = string.Empty;

    // 新闻列表相关变量
    private List<ZycCore.Models.StockNews.Data> newsItems = new();
    private string sCacheName = "";
    int ListPageSize = 20;
    int PageIndex = 0;
    ZycCore.Models.StockNews.List newsList = new();
    bool isLoading = false;
    int totalCount = 0;
    bool hasMoreData => totalCount > newsItems.Count;

    protected override async Task OnInitializedAsync()
    {
        await LoadNews();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (!string.IsNullOrEmpty(StockCodeString))
        {
            PageIndex = 0;
            newsItems.Clear();
            totalCount = 0;
            await <PERSON>adNews();
        }
    }

    async Task LoadNews()
    {
        if (string.IsNullOrEmpty(StockCodeString)) return;
        
        isLoading = true;
        PageIndex++;

        StockNewsDto dto = new()
        {
            DateStart = DateTime.UtcNow.AddDays(-14), // 过去两周
            DateEnd = DateTime.UtcNow,
            StockCode = StockCodeString.Trim(),
            SearchTitle = "",
            AddWhere = "and SummaryChinese <> ''"
        };

        try
        {
            newsList = new ZycCore.Models.StockNews.List();
            var loadDataArgs = new LoadDataArgs
            {
                Skip = (PageIndex - 1) * ListPageSize,
                Top = ListPageSize
            };

            var result = await newsList.DataTableListAsync(loadDataArgs, ListPageSize, dto);
            
            if (PageIndex == 1)
            {
                totalCount = newsList.TotalCount;
            }
            
            if (result.Count > 0)
            {
                newsItems.AddRange(result);
                sCacheName = newsList.CacheName;
            }
        }
        catch
        {
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}

<_UIFramework FullWidth="true" HeaderContentClass="d-xl-flex justify-content-between align-items-center " Class="bg-body stock-data">
    <HeaderContent>
        <div class="py-2 text-center">
            <b>相关新闻</b>
            <span class="badge badge-phoenix badge-phoenix-info py-1 ms-2">
                @StockCodeString
            </span>
        </div>
        <div></div>
    </HeaderContent>
    <ChildContent>
        @if (isLoading && newsItems.Count == 0)
        {
            <div class="d-flex justify-content-center py-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            </div>
        }
        else if (newsItems.Count == 0)
        {
            <div class="alert alert-info py-2">
                没有找到与 @StockCodeString 相关的新闻
            </div>
        }
        else
        {
            <div class="news-container">
                @foreach (var news in newsItems)
                {
                    <div class="card mb-1 border-0 shadow-sm news-card">
                        <div class="card-body py-1 px-2">
                            <h6 class="card-title mb-0" style="font-size: 0.95rem;">@news.TitleChinese</h6>
                            <div style="font-size: 0.85rem; color: #6ea8fe; margin-bottom: 2px;">
                                @news.CreateDate?.ToString("yyyy-MM-dd HH:mm")
                            </div>
                            <p class="card-text mb-1" style="font-size: 0.9rem;">
                                @(string.IsNullOrEmpty(news.SummaryChinese) ? "暂无摘要" : (news.SummaryChinese.Length > 150 ? news.SummaryChinese.Substring(0, 150) + "..." : news.SummaryChinese))
                            </p>
                        </div>
                    </div>
                }
            </div>

            @if (hasMoreData)
            {
                <div class="text-center mt-2 mb-1">
                    <button class="btn btn-sm btn-outline-primary px-4" @onclick="LoadNews" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            <span>加载中...</span>
                        }
                        else
                        {
                            <span>加载更多 (@newsItems.Count/@totalCount)</span>
                        }
                    </button>
                </div>
            }
            else if (totalCount > 0)
            {
                <div class="text-center mt-2 mb-1">
                    <small class="text-muted">已加载全部 @totalCount 条新闻</small>
                </div>
            }
        }
    </ChildContent>
</_UIFramework>

