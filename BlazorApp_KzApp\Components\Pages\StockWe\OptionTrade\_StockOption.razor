@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.PageUI.RzDataGrid
@using <PERSON><PERSON><PERSON>
@using eZeroCore.Web.Stk.Data
@using System.Text.Json
@using MudBlazor
@inject IJSRuntime JS
@inject IDialogService DialogService
@code {
    Settings settings = new();

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    DateRange? _OnSetDateRange = null;
    [CascadingParameter]
    public DateRange? OnSetDateRange
    {
        get { return _OnSetDateRange; }
        set
        {
            if (_OnSetDateRange is null && value is not null ||
                (_OnSetDateRange is not null && !_OnSetDateRange.Equals(value)))
            {
                _OnSetDateRange = value;
                _ = RefreshData();
            }
        }
    }

    [Parameter]
    public string SearchString { get; set; } = "";

    [Parameter]
    public EventCallback<bool> OnChangePageNumberOrOrder { get; set; }

    eZeroCore.Users.User User { get; set; } = new();
    eZeroCore.Web.Stk.Data.Dt_StockOption.Filters UserFilter { get; set; } = new();
    protected override async Task OnInitializedAsync()
    {
        User = new(CObj.UserId);
        UserFilter = await GetFilterValue();
        await InitDataTable();
    }

    async Task<eZeroCore.Web.Stk.Data.Dt_StockOption.Filters> GetFilterValue()
    {
        await Task.Delay(0);
        await JS.InvokeVoidAsync("console.log", $"【--GetFilterValue: {User.Settings.Dt_StockOption_SaveIndex} --】");
        switch (User.Settings.Dt_StockOption_SaveIndex)
        {
            default:
            case 0:
                return User.Settings.StkOptFilterA;
            case 1:
                return User.Settings.StkOptFilterB;
            case 2:
                return User.Settings.StkOptFilterC;
        }
    }

    public async Task RefreshData(bool Search = false)
    {
        //await JS.InvokeVoidAsync("console.log", $"【--RefreshData {Search} --】");
        if (Search)
        {
            if (AppSettings.IsTestModeAll)
            {
                await JS.InvokeVoidAsync("console.log", $"OnSearch:【{SearchString}】3");
            }
        }
        await InitDataTable();
        // 更新 UI 以反映新的数据
        await InvokeAsync(StateHasChanged);
    }

    bool isLoading = false;
    List<eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption> stockOptions = [];
    int PageSize = 50;
    int TotalCount { get; set; }
    //int _PageNumber = 1;
    //int PageNumber = 1;
    public async Task InitDataTable(LoadDataArgs? args = null)
    {
        #region...
        //isLoading = true;
        if (framework is not null)
        {
            await Generic.LoadingDom(JS, framework.ContentDomId);
        }
        if (UserFilter is null)
        {
            UserFilter = new();
        }
        if (OnSetDateRange is not null && OnSetDateRange.Start is not null)
        {
            UserFilter.startDate = OnSetDateRange.Start.Value;
        }
        else
        {
            UserFilter.startDate = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime();
        }
        if (OnSetDateRange is not null && OnSetDateRange.End is not null)
        {
            UserFilter.endDate = OnSetDateRange.End.Value;
        }
        else
        {
            UserFilter.endDate = null;
        }
        StateHasChanged();
        await Task.Delay(10);
        eZeroCore.Web.Stk.Data.Dt_StockOption.DataList list = new()
        {
            PageSize = PageSize,
            SearchStock = SearchString,
            Filter = UserFilter,
            ExcludedExpired = ExcludedExpired
        };

        stockOptions = await list.RzListAsync(args, PageSize);
        TotalCount = list.TotalCount;

        bool liveMode = (list.SetPageNumber > 1 || (!string.IsNullOrEmpty(list.SortName) && !list.SortName.Equals("createdate", StringComparison.OrdinalIgnoreCase)));

        await OnChangePageNumberOrOrder.InvokeAsync(!liveMode);

        //isLoading = false;
        if (framework is not null)
        {
            await Generic.LoadingDom(JS, framework.ContentDomId, false);
        }
        #endregion
    }

    private void OnRowRender(RowRenderEventArgs<eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption> args)
    {
        string className = "rz-data-row";
        if ((DateTime.UtcNow - args.Data.CreateDateUtc).Minutes < 2)
        {
            if (args.Data.CallPut.Equals("put", StringComparison.OrdinalIgnoreCase))
            {
                className = "rz-data-row background-fade-red";
            }
            else
            {
                className = "rz-data-row background-fade-blue";
            }
        }
        args.Attributes["class"] = className;
    }

    _Stock_Option_Filter filterObj;

    async Task ToggleFilterDrawer(int Index)
    {
        User.Settings.Dt_StockOption_SaveIndex = Index;
        if (filterObj is not null)
        {
            UserFilter = await GetFilterValue();
            await InitDataTable();
            //await filterObj.ToggleEndDrawer(UserFilter);
            //await User.UpdateUserSettingsAsync();
        }
    }

    async Task OnChangeFilter(eZeroCore.Web.Stk.Data.Dt_StockOption.Filters filters)
    {
        //UserFilter = filters;
        await RefreshData();
    }

    async Task SaveFilter()
    {
        if (CObj.IsKyrison)
        {
            await JS.InvokeVoidAsync("console.log", $"【--SaveFilter: {User.Settings.Dt_StockOption_SaveIndex} --】", string.Join(',', UserFilter.stocks));
            await JS.InvokeVoidAsync("console.log", $"【--SaveFilter: {string.Join(',', User.Settings.StkOptFilterA.stocks)} --】");
            await JS.InvokeVoidAsync("console.log", $"【--SaveFilter: {string.Join(',', User.Settings.StkOptFilterB.stocks)} --】");
            await JS.InvokeVoidAsync("console.log", $"【--SaveFilter: {string.Join(',', User.Settings.StkOptFilterC.stocks)} --】");

        }
        //await JS.InvokeVoidAsync("console.log", $"【--SaveFilter: {User.Settings.Dt_StockOption_SaveIndex} --】",  JsonSerializer.Serialize(UserFilter));
        await User.UpdateUserSettingsAsync();
    }

    _UIFramework framework;

    bool RenderDone = false;
    string IconClassBase = "fal fa-filter d-none d-md-inline-block me-1";
    string FilterBtnBaseClass = "btn fs-9 py-1 mb-0 px-2";

    public bool _ExcludedExpired = false;
    public bool ExcludedExpired
    {
        get { return _ExcludedExpired; }
        set
        {
            _ExcludedExpired = value;
            _ = RefreshData();
        }
    }

    // 移动端过滤器对话框
    private async Task OpenMobileFilterDialog()
    {
        var parameters = new DialogParameters<_StockOptionFilterMobileDialog>
        {
            { x => x.CObj, CObj },
            { x => x.CurrentFilter, UserFilter },
            { x => x.DateStart, OnSetDateRange?.Start },
            { x => x.DateEnd, OnSetDateRange?.End }
        };

        var options = new MudBlazor.DialogOptions()
        {
            MaxWidth = MaxWidth.Small,
            FullWidth = true,
            CloseButton = true,
            Position = MudBlazor.DialogPosition.BottomCenter,
            CloseOnEscapeKey = true
        };

        var dialog = await DialogService.ShowAsync<_StockOptionFilterMobileDialog>("期权过滤器", parameters, options);
        var result = await dialog.Result;

        if (!result.Canceled && result.Data != null)
        {
            var filterResult = result.Data as _StockOptionFilterMobileDialog.FilterResult;
            if (filterResult != null)
            {
                // 应用过滤器结果
                UserFilter = filterResult.Filter;
                
                // 保存过滤器到用户设置
                switch (User.Settings.Dt_StockOption_SaveIndex)
                {
                    case 0:
                        User.Settings.StkOptFilterA = UserFilter;
                        break;
                    case 1:
                        User.Settings.StkOptFilterB = UserFilter;
                        break;
                    case 2:
                        User.Settings.StkOptFilterC = UserFilter;
                        break;
                }
                
                await User.UpdateUserSettingsAsync();
                await RefreshData();
            }
        }
    }

}

<_UIFramework @ref=@framework FullWidth="true" HeaderContentClass="d-xl-flex justify-content-between align-items-center">
    <HeaderContent>
        <div class="pb-0 pt-2 pb-md-2 text-center d-flex flex-row justify-content-between align-items-center">
            <div class="d-flex flex-row align-items-center">
                <div>
                    期权异动实时订单流
                </div>
                <div class="ms-3">
                    <MudSwitch Size="Size.Small" @bind-Value="@ExcludedExpired" Label="排除已过期" Color="Color.Success" />
                </div>
            </div>
            <!-- 移动端过滤器按钮 -->
            @if (CObj.IsMobile)
            {
                <div>
                    <button @onclick="OpenMobileFilterDialog" type="button" class="btn btn-sm btn-phoenix-warning">
                        <i class="fa-solid fa-sliders me-1"></i>
                        期权过滤器
                    </button>
                </div>
            }
        </div>
        @if (!CObj.IsMobile)
        {
            <div class="btn-group d-block d-md-inline-block text-center">
                <button type="button" @onclick="()=>ToggleFilterDrawer(0)"
                        class="@FilterBtnBaseClass @(User.Settings.Dt_StockOption_SaveIndex==0?"btn-warning":"btn-phoenix-warning")">
                    <i class="@IconClassBase"></i>
                    <span class="@(CObj.IsMobile ? "line-clamp-1" : "")">
                        @(string.IsNullOrWhiteSpace(User.Settings.StkOptFilterA.FilterName) ? "过滤器A" : User.Settings.StkOptFilterA.FilterName.ToUpper())
                    </span>
                </button>
                <button type="button" @onclick="()=>ToggleFilterDrawer(1)"
                        class="@FilterBtnBaseClass
                            @(User.Settings.Dt_StockOption_SaveIndex==1?"btn-warning":"btn-phoenix-warning")">
                    <i class="@IconClassBase"></i>
                    <span class="@(CObj.IsMobile ? "line-clamp-1" : "")">
                        @(string.IsNullOrWhiteSpace(User.Settings.StkOptFilterB.FilterName) ? "过滤器B" : User.Settings.StkOptFilterB.FilterName.ToUpper())
                    </span>
                </button>
                <button type="button" @onclick="()=>ToggleFilterDrawer(2)"
                        class="@FilterBtnBaseClass
                            @(User.Settings.Dt_StockOption_SaveIndex==2?"btn-warning":"btn-phoenix-warning")">
                    <i class="@IconClassBase"></i>
                    <span class="@(CObj.IsMobile ? "line-clamp-1" : "")">
                        @(string.IsNullOrWhiteSpace(User.Settings.StkOptFilterC.FilterName) ? "过滤器C" : User.Settings.StkOptFilterC.FilterName.ToUpper())
                    </span>
                </button>
                <button @onclick="async () => { if (filterObj is not null) await filterObj.ToggleEndDrawer(); }" type="button" class="p-1 pt-2">
                    <i class="fa-solid fa-sliders mb-2 p-2 p-md-0 fs-8 fs-md-10"></i>
                </button>
            </div>
        }
    </HeaderContent>
    <ChildContent>


        <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
            <_Stock_Option_Filter Filter=UserFilter OnSave="@SaveFilter" OnChange="@OnChangeFilter" @ref=@filterObj />
        </MudHidden>

        <RzDataTable TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption"
                     ClassName="StkData"
                     OverflowXScroll="true"
                     Slim="true"
                     AllowPaging=!CObj.UserIsLowLvAdmin
                     AllowColumnResize="true"
                     Data="@stockOptions" PageSize="@PageSize"
                     IsLoading="@isLoading"
                     TheadVisable="@(!CObj.IsMobile)"
                     RowRenderCallback="@OnRowRender"
                     Count="@TotalCount" LoadData="@InitDataTable">


            <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
                <RadzenDataGridColumn TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Width="80px" Sortable="true" Title="时间" Property="CreateDate" Frozen="true" Resizable="false">
                    <Template Context="data">
                        @data.CreateDate.ToString("HH:mm:ss")
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Width="66px" Sortable="true" Title="日期" Property="CreateDate" Frozen="true" Resizable="false">
                    <Template Context="data">
                        @data.CreateDate.ToString("MM-dd")
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Width="80px" Title="代号" Sortable="true" Property="Stock" Frozen="true" Resizable="false">
                    <Template Context="data">
                        @data.Stock
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Width="70px" Title="C/P" Frozen="true" Resizable="false">
                    <Template Context="data">
                        @(new MarkupString(_StockOption_Mobile.GetPutOrCall(data.CallPut)))
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Width="100px" Title="趋势" Sortable="false" Frozen="true" Resizable="false">
                    <Template Context="data">
                        @{
                            var className = "";
                            if (data.TrendSentiment.Contains('涨'))
                            {
                                className = "text-success-light";
                            }
                            else if (data.TrendSentiment.Contains('跌'))
                            {
                                className = "text-danger-lighter";
                            }
                            else
                            {
                                className = "";
                            }
                        }
                        <lable class="@className">
                            @data.TrendSentiment
                        </lable>

                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="权利金" Width="80px" Property="Premium" Frozen="true" Resizable="false">
                    <Template Context="data">
                        @eZeroCore.ENumeric.ConvertNumberToMorKString(data.Premium)
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Title="到期日" Property="ExpireDate" Width="100px" Frozen="true" Resizable="false">
                    <Template Context="data">
                        @data.ExpireDate.ToString(settings.SettingsEntity.ShortDateFormat)
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="100px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="行权价" Property="Strike">
                    <Template Context="data">
                        @eZeroCore.ENumeric.OutMonetaryStr(data.Strike, true)
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="100px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="股价" Property="StockPrice">
                    <Template Context="data">
                        @eZeroCore.ENumeric.OutMonetaryStr(data.StockPrice, true)
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="85px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="合约数" Property="Size">
                    <Template Context="data">
                        @data.Size
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="90px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="合约价" Property="ContractPrice">
                    <Template Context="data">
                        @eZeroCore.ENumeric.OutMonetaryStr(data.ContractPrice, true)
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="80px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="Bid" Property="Bid">
                    <Template Context="data">
                        @data.Bid.ToString("N2")
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="80px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="Ask" Property="Ask">
                    <Template Context="data">
                        @data.Ask.ToString("N2")
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="90px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="未平仓量" Property="OpenInterest">
                    <Template Context="data">
                        @data.OpenInterest.ToString()
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="70px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="分数" Property="Score">
                    <Template Context="data">
                        @data.Score
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="60px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Title="平仓" Property="UniqueCount" Sortable="false">
                    <Template Context="data">
                        @if (data.UniqueCount > 0)
                        {
                            <lable>未知</lable>
                        }
                        else
                        {
                            <lable>是</lable>
                        }
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="86px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="到期天" Property="ExpireDate">
                    <Template Context="data">
                        @data.Day_Expire
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Width="66px" Title="范围" Sortable="false">
                    <Template Context="data">
                        @(new MarkupString(_StockOption_Mobile.GetInOutMoneyHtml(data.InOutMoney)))
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="120px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="隐含波动率" Property="Ivol">
                    <Template Context="data">
                        @data.Ivol.ToString("N2")
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="120px" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" TextAlign="TextAlign.Right" Title="累计成交量" Property="CumulativeVol">
                    <Template Context="data">
                        @eZeroCore.ENumeric.ConvertNumberToMorKString(data.CumulativeVol)
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn Width="60px" Sortable="false" TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Title="类别" Property="Trade">
                    <Template Context="data">
                        @if (data.Trade == eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption.TradeEnum.none)
                        {
                            <span>&nbsp;</span>
                        }
                        else
                        {
                            @data.Trade
                        }
                    </Template>
                </RadzenDataGridColumn>
            </MudHidden>

            <MudHidden Breakpoint="Breakpoint.MdAndUp">
                <RadzenDataGridColumn TItem="eZeroCore.Web.Stk.Data.Dt_StockOption.StockOption" Sortable="false" Resizable="false">
                    <Template Context="data">

                        <_StockOption_Mobile Data="data" />

                    </Template>
                </RadzenDataGridColumn>
            </MudHidden>           

        </RzDataTable>

    </ChildContent>
</_UIFramework>