﻿@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj CObj { get; set; }

    [Parameter]
    public bool HomePageMenuVisable { get; set; } = true;

    [Parameter]
    public bool Visible { get; set; } = true;


    string AdministratorMenusParentCID { get; set; } = "";
    string HomePageName = "";
    string HomePageUrl = "";
    string ChannelId = "";
    string ChannelCreateUserId = "";
    bool UserIsLowLvAdmin { get; set; }
    List<eZeroCore.Web.Column> AdminCols = [];
    List<eZeroCore.Web.Column> AgencyCols = [];
    List<eZeroCore.Web.Column> SchoolCols = [];
    bool hiddenItemVisible = false;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        #region

        if(firstRender)
        {
            string WeicityAdminMenusPCID = "COL9875AA5599";

            switch (eZeroCore.Settings.GetSysMode)
            {
                case Settings.SysMode.WeiCity:
                    AdministratorMenusParentCID = WeicityAdminMenusPCID;
                    break;
                case Settings.SysMode.Stk:
                    AdministratorMenusParentCID = "COL9F88B53211";
                    break;                
            }


            HomePageName = CObj.ThisChannel?.SettingsEntity.HomePageTabName ?? "";
            if (string.IsNullOrWhiteSpace(HomePageName))
            {
                HomePageName = "HomePage";
            }
            HomePageUrl = CObj.ThisChannel?.GetChannelUrl() ?? "/";
            //Test myc735d3d0be2
            ChannelId = CObj.ThisChannel?.ColumnId ?? "";

            if (CObj.IsWeiCity && string.IsNullOrWhiteSpace(ChannelId))
            {
                HomePageUrl = CObj.MyChannel?.GetChannelUrl() ?? "/";
            }

            if (CObj.UserIsAdmin)
            {
                eZeroCore.Web.ColumnList cols = new()
                {
                    HiddenVisible = true,
                    ParentId = AdministratorMenusParentCID,
                    OrderBy = eZeroCore.Web.ColumnList.OrderByEnum.Followorder,
                    SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending,
                    AddWhere = $" RoleEnum not in ({Convert.ToInt32(eZeroCore.Web.Document.RoleEnumForDoc.WeiCityChannel)})"
                };
                AdminCols = await cols.ListAsync(eZeroCore.Db.CacheHelperV2.CacheNameType.Col_MenusTree);
            }

            if (!CObj.IsStk)
            {
                if (CObj.UserIsAdmin || (CObj.IsACEI && CObj.UserIsAgency))
                {
                    if (!string.IsNullOrEmpty(eZeroCore.Web.ACEI.Solutions.AgencyMenusParentCID))
                    {
                        eZeroCore.Web.ColumnList cols = new()
                        {
                            HiddenVisible = CObj.UserIsAdmin,
                            ParentId = eZeroCore.Web.ACEI.Solutions.AgencyMenusParentCID,
                            OrderBy = eZeroCore.Web.ColumnList.OrderByEnum.Followorder,
                            SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending
                        };
                        AgencyCols = await cols.ListAsync(eZeroCore.Db.CacheHelperV2.CacheNameType.Col_MenusTree);
                    }
                }
                if (CObj.IsWeiCity)
                {
                    if (CObj.UserIsAdmin || CObj.UserIsAgency)
                    {
                        eZeroCore.Web.ColumnList cols = new()
                        {
                            HiddenVisible = CObj.UserIsAdmin,
                            ParentId = eZeroCore.Web.School.Solutions.MenusParentCID,
                            OrderBy = eZeroCore.Web.ColumnList.OrderByEnum.Followorder,
                            SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending
                        };
                        SchoolCols = await cols.ListAsync(eZeroCore.Db.CacheHelperV2.CacheNameType.Col_MenusTree);
                    }
                }
            }

            ChannelCreateUserId = CObj.ThisChannel?.CreateMemberId ?? "";
            hiddenItemVisible = CObj.UserId.Equals(ChannelCreateUserId, StringComparison.OrdinalIgnoreCase) || CObj.UserIsAdmin;

            StateHasChanged();
        }

        #endregion
    }

    public static string BackEndIcon = "fa-solid fa-building-lock fa-fw";
    public static string BackEndName(BlzHelperObj cobj)
    {
        return cobj.UserLang.GetLangValue("后台管理(BMS)");
    } 

    [Parameter]
    public bool OnClickUseEvent { get; set; }
}

<NavbarVerticalMenus_Framework Visible=@Visible>

    @if (HomePageMenuVisable)
    {
        <div>
            &nbsp;
        </div>
        <LeftMenus_NavItemGroup OnClickUseEvent=@OnClickUseEvent MatchUrl="@HomePageUrl" Icon="fal fa-city">
            @HomePageName
        </LeftMenus_NavItemGroup>
    }

    @if (!CObj.IsStk)
    {
        <LeftMenus_NavItemGroup OnClickUseEvent=@OnClickUseEvent HiddenItemVisible=hiddenItemVisible GroupName="@(string.IsNullOrWhiteSpace(ChannelId) ? "" : "Library")" ChlId="@ChannelId" />
    }
    else
    {
        <LeftMenus_NavItemGroup GroupName="数据中心" ChlId="COL8EF88AE697" />
    }

    @*
        <_NavItem IsRoot="true" MatchUrl="/EditorTest"
              Icon="fal fa-home text-primary">
            TestEditor
        </_NavItem>
    *@

    @if (CObj.UserIsAdmin)
    {
        <LeftMenus_NavItemGroup GroupName="@BackEndName(CObj)"
                                ChildcontentIncludeNavItem="false">

            @foreach (eZeroCore.Web.Column col in AdminCols)
            {
                if (!string.IsNullOrWhiteSpace(col.Redirect) &&
                !string.IsNullOrWhiteSpace(col.SettingsEntity.Icon))
                {
                    if (col.SettingsEntity.ContentForSettingsAndLowAdmin || !CObj.UserIsLowLvAdmin)
                    {
                        <_NavItem IsRoot="true" MatchUrl="@col.Redirect" Icon="@($"{col.SettingsEntity.Icon} text-danger")">
                            @col.ColumnName
                        </_NavItem>
                    }

                }
            }


        </LeftMenus_NavItemGroup>
    }
    @if (SchoolCols.Count > 0)
    {
        <LeftMenus_NavItemGroup GroupName="SchoolDashboard"
                                ChildcontentIncludeNavItem="false">

            @foreach (eZeroCore.Web.Column col in SchoolCols)
            {
                if (!string.IsNullOrWhiteSpace(col.Redirect) &&
                !string.IsNullOrWhiteSpace(col.SettingsEntity.Icon))
                {
                    <_NavItem IsRoot="true" MatchUrl="@col.Redirect" Icon="@($"{col.SettingsEntity.Icon} text-info-light")">
                        @col.ColumnName
                    </_NavItem>
                }
            }


        </LeftMenus_NavItemGroup>
    }
    @if (AgencyCols.Count > 0)
    {
        <LeftMenus_NavItemGroup GroupName="AgencyDashboard"
                                ChildcontentIncludeNavItem="false">

            @foreach (eZeroCore.Web.Column col in AgencyCols)
            {
                if (!string.IsNullOrWhiteSpace(col.Redirect) &&
                !string.IsNullOrWhiteSpace(col.SettingsEntity.Icon))
                {
                    <_NavItem IsRoot="true" MatchUrl="@col.Redirect" Icon="@($"{col.SettingsEntity.Icon} text-success-lighter")">
                        @col.ColumnName
                    </_NavItem>
                }
            }


        </LeftMenus_NavItemGroup>
    }


</NavbarVerticalMenus_Framework>