﻿@inject IJSRuntime JS
@inject NavigationManager NavigationManagerObj
@inject BlzHelperObj CObj

@code {    

    [CascadingParameter] IMudDialogInstance? MudDialog { get; set; }

    eZeroCore.Users.User userObj { get; set; } = new();

    protected override Task OnInitializedAsync()
    {
        userObj = new(CObj.UserId);
        SelectedLanguage = (int)userObj.LangEnum;
        Source_SelectedLanguage = SelectedLanguage;

        return base.OnInitializedAsync();
    }


    async Task Ok()
    {
        if (MudDialog is not null)
        {
            if (SelectedLanguage != Source_SelectedLanguage)
            {
                if (SelectedLanguage < 2 && !CObj.IsACEI)
                {
                    userObj.LangEnum = (eZeroCore.AppLang.LangType)SelectedLanguage;
                    await userObj.SaveAsync();
                    await JS.InvokeVoidAsync($"location.reload");
                }
                else
                {
                    string langCode = ((GoogleTranslateLanguage)SelectedLanguage).ToString().Replace("_", "-");
                    string translateUrl = $"https://translate.google.com/translate?sl=auto&tl={langCode}&u={Uri.EscapeDataString(CObj.ThisUrl_DomainUrl_NotLoginOrRegister)}";
                    //await JS.InvokeVoidAsync($"eval", $"location.href={translateUrl}");
                    NavigationManagerObj.NavigateTo(translateUrl, true);
                }
            }
            //MudDialog.Close(DialogResult.Ok<string>("ok"));
        }
    }

    private void Cancel()
    {
        if (MudDialog is not null)
        {
            MudDialog.Cancel();
        }
    }

    int SelectedLanguage = 0;
    int Source_SelectedLanguage = 0;

    public enum GoogleTranslateLanguage
    {
        zh_CN, // 中文（简体）
        en,    // 英文
        ja,    // 日语
        ko,    // 韩语
        de,    // 德语
        fr,    // 法语
        es,    // 西班牙语
        it,    // 意大利语
        ru,    // 俄语
        pt,    // 葡萄牙语
        ar,    // 阿拉伯语
        nl,    // 荷兰语
        el,    // 希腊语
        pl,    // 波兰语
        hu,    // 匈牙利语
        th,    // 泰语
        vi,    // 越南语
        id,    // 印尼语
        ms,     // 马来语
        zh_TW, // 繁體中文
    }


}
<MudDialogContent>
    <ChildContent>
        <div>
            <select class="form-select" @bind=@SelectedLanguage>
                @*
                    <option selected="@(userObj.LangEnum==eZeroCore.AppLang.LangType.None)" value="-1">Auto</option>
                *@
                <option selected="@(userObj.LangEnum==eZeroCore.AppLang.LangType.Us_En)" value="1">English</option>
                <option value="4">
                    Deutsch
                </option>
                <option value="5">
                    Français
                </option>
                <option value="6">
                    Español
                </option>
                <option value="7">
                    Italiano
                </option>
                <option value="8">
                    Русский
                </option>
                <option value="9">
                    Português
                </option>
                <option value="10">
                    العربية
                </option>
                <option value="11">
                    Nederlands
                </option>
                <option value="12">
                    Ελληνικά
                </option>
                <option value="13">
                    Polski
                </option>
                <option value="14">
                    Magyar
                </option>
                <option selected="@(userObj.LangEnum== eZeroCore.AppLang.LangType.Zh_Cn)" value="0">
                    简体中文
                </option>
                <option value="19">
                    繁體中文
                </option>
                <option value="2">
                    日本語
                </option>
                <option value="3">
                    한국어
                </option>
                <option value="15">
                    ไทย
                </option>
                <option value="16">
                    Tiếng Việt
                </option>
                <option value="17">
                    Bahasa Indonesia
                </option>
                <option value="18">
                    Bahasa Melayu
                </option>
            </select>
        </div>
    </ChildContent>
    <MudDialogActions>
        <div class="p-2 px-3">
            <Ez_Btn Color="General.Color.info" OnBtnClick="Cancel">
                @CObj.UserLang.GetLangValue("取消")
            </Ez_Btn>
            <Ez_Btn Color="General.Color.primary" OnBtnClick="Ok">
                @CObj.UserLang.GetLangValue("确定")
            </Ez_Btn>
        </div>
    </MudDialogActions>
</MudDialogContent>