@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }
    [Parameter]
    public RenderFragment? ChildContent { get; set; }
    [Parameter]
    public RenderFragment? TitleContent { get; set; }

    [Parameter]
    public RenderFragment? FooterContent { get; set; }

    [Parameter]
    public string Title { get; set; } = "";
    [Parameter]
    public string TitleClass { get; set; } = "fs-8";
    [Parameter]
    public string Class { get; set; } = "bg-body d-flex justify-content-between align-items-center";

    [Parameter]
    public string AddClass { get; set; } = "";
}


<div class="@Class @AddClass">
    <div class="fw-semibold p-2">
        <label class="form-label @TitleClass" style="text-transform: none!important;">
            @if (TitleContent is not null)
            {
                @TitleContent
            }
            else
            {
                @Title
            }
        </label>
        @if (FooterContent is not null)
        {
            <small class="d-block p-2 ps-3">
                @FooterContent
            </small>
        }
    </div>
    @ChildContent
</div>