@using AspNetCoreWeb.Blazor.CityV3.LayoutFramework
@using AspNetCoreWeb.Blazor.CityV3.ZBk
@using AspNetCoreWeb.Blazor.CityV3.ZBk.Docs
@using AspNetCoreWeb.Blazor.Stk._V3.Doc
@inject IJSRuntime JS

@code {
    [Parameter]
    public ComponentPassObj CObj { get; set; } = new();

    eZeroCore.Web.Column.ContentType contentType = eZeroCore.Web.Column.ContentType.Article;

    bool isDepthreport = false;
    protected override Task OnInitializedAsync()
    {
        if (CObj.ThisColumn is not null)
        {
            contentType = CObj.ThisColumn.ContentTypeEnum;
            isDepthreport = eZeroCore.Web.Stk.WebDefaultValue.PayDocumentsColId.Equals(CObj.ThisColumn.ColumnId, StringComparison.OrdinalIgnoreCase);
        }
        return base.OnInitializedAsync();
    }
}

<Main_Framework CObj="@CObj" Container_Fluid="false" IsPageContent="true">

    @if (contentType != eZeroCore.Web.Column.ContentType.Video)
    {
        if(isDepthreport)
        {
            <_DepthReportPayment>
                <_UI_Left_Right_Website Border="false">

                    <ChildContent>

                        <_DocListV3 PageSize="18" />

                    </ChildContent>

                    <RightContent>
                        <_DocListV3 PageSize="6" OrderByPageview="true" AllowPaging="false" AllowSearch="false">
                            <i class="fa-solid fa-fire mx-1 text-danger"></i>
                            @CObj.UserLang.GetLangValue("热门内容")
                        </_DocListV3>
                    </RightContent>

                </_UI_Left_Right_Website>
            </_DepthReportPayment>
        }
        else
        {
            <_UI_Left_Right_Website Border="false">

                <ChildContent>

                    <_DocListV3 PageSize="18" />

                </ChildContent>

                <RightContent>
                    <_DocListV3 PageSize="6" OrderByPageview="true" AllowPaging="false" AllowSearch="false">
                        <i class="fa-solid fa-fire mx-1 text-danger"></i>
                        @CObj.UserLang.GetLangValue("热门内容")
                    </_DocListV3>
                </RightContent>

            </_UI_Left_Right_Website>
        }
    }
    else
    {
        <_ColumnStkVideos />
    }

    <AspNetCoreWeb.Blazor.Stk.FrontEnd._FooterV2 IsContentPage="true" MobileFooterMenus="true" />

</Main_Framework>