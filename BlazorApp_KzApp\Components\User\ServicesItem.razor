﻿@inject BlzHelperObj CObj
@code {

    [Parameter]
    public eZeroCore.Web.Document DocObj { get; set; } = new();

    [Parameter]
    public bool Popular { get; set; }

}

<div class="card h-100 rounded-end-xl-0 rounded-start @(Popular?"border border-2 border-primary":"")">
    @if (Popular)
    {
        <div class="position-absolute d-flex flex-center bg-primary-subtle rounded-top py-1 end-0 start-0 badge-pricing">
            <p class="text-primary-dark mb-0">
                @CObj.UserLang.GetLangValue("最流行")
            </p>
        </div>
    }
    <div class="card-body px-1">
        <div class="px-5">
            <div class="text-center pt-5">
                <i class="@DocObj.SettingsEntity.Icon fa-fw fa-3x"></i>
                <h3 class="fw-semibold my-4">
                    @DocObj.FullTitle
                </h3>
            </div>
            @if (DocObj.Prices > 0)
            {
                <div class="text-center">
                    <h1 class="fw-semibold text-primary">
                        <span class="fw-bolder">
                            RM@(DocObj.Prices / 100)
                        </span>
                        <small class="text-body-emphasis ms-n2 fs-7 fw-bolder text-primary-emphasis" style="font-size:0.8rem">
                            /Mth.
                        </small>
                    </h1>
                    <h5 class="mb-4 text-body"></h5>
                    <button class="btn btn-lg mb-6 w-100 btn-outline-primary">
                        @CObj.UserLang.GetLangValue("立即订阅")
                    </button>
                </div>
            }
        </div>
        <ul class="fa-ul pricing-list">
            @foreach (string s in DocObj.DocContentSummary.Split('\n'))
            {
                if (!string.IsNullOrWhiteSpace(s))
                {
                    <li class="mb-4 d-flex align-items-center">
                        <span class="fa-li">
                            <span class="fas fa-check text-primary"></span>
                        </span>
                        <span class="text-body-secondary">
                            @s
                        </span>
                    </li>
                }
            }

        </ul>
    </div>
</div>
