﻿@using BlazorApp_KzApp.Components.Pages.FrontEnd
@using MudBlazor
@using ZycCore.Models.Dto
@inject NavigationManager NavigationManagerObj

@page "/Board/{param?}"

@code {
    [Parameter]
    public string param { get; set; } = "";

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    // 功能卡片数据结构
    public class FeatureCard
    {
        public string Icon { get; set; } = "";
        public string Title { get; set; } = "";
        public string Url { get; set; } = "";
        public string IconColor { get; set; } = "";
        public bool IsFree { get; set; } = false;

        public string Description { get; set; } = "";
        public string ButtonText { get; set; } = "";       

        public eZeroCore.Web.BlazorServerUI.General.Color Color { get; set; } = eZeroCore.Web.BlazorServerUI.General.Color.primary;

        public string CardClass { get; set; } = "card-primary";
    }

    private List<FeatureCard> coreFeatures = new();
    private List<FeatureCard> modelFeatures = new();

    // 父ID
    private const string FEATURES_PARENT_ID = "COL8EF88AE697";

    protected override async Task OnInitializedAsync()
    {
        await LoadFeaturesFromDatabase();
    }

    private async Task LoadFeaturesFromDatabase()
    {
        try
        {
            var columns = await new eZeroCore.Web.ColumnList
                {
                    ParentId = FEATURES_PARENT_ID,
                    HiddenVisible = false,
                    OrderBy = eZeroCore.Web.ColumnList.OrderByEnum.Followorder,
                    SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending
                }.ListAsync();

            // 功能列表
            var coreFeaturesTemp = new List<FeatureCard>();
            var modelFeaturesTemp = new List<FeatureCard>();

            // 处理每个栏目
            foreach (var column in columns)
            {
                // 初始化默认值
                string icon = !string.IsNullOrEmpty(column.SettingsEntity._Icon) ? column.SettingsEntity._Icon : "fas fa-link";
                string iconColor = "#3b82f6";
                string cardClass = "card-primary";
                string buttonText = "查看详情";
                var cardColor = eZeroCore.Web.BlazorServerUI.General.Color.primary;
                bool isFree = false;
                bool isMultiFactor = !string.IsNullOrEmpty(column.SEOPack_keywords) &&
                                    column.SEOPack_keywords.Trim().Equals("false", StringComparison.OrdinalIgnoreCase);
                string description = "";

                // 解析SEOPack_Title配置
                if (!string.IsNullOrEmpty(column.SEOPack_Title))
                {
                    try
                    {
                        string configText = column.SEOPack_Title;

                        // 提取各项配置
                        if (configText.Contains("ButtonText ="))
                        {
                            int start = configText.IndexOf("ButtonText =") + 12;
                            int end = configText.IndexOf(",", start);
                            if (end == -1) end = configText.Length;
                            if (end > start)
                                buttonText = configText.Substring(start, end - start).Trim().Replace("\"", "");
                        }

                        if (configText.Contains("IconColor ="))
                        {
                            int start = configText.IndexOf("IconColor =") + 11;
                            int end = configText.IndexOf(",", start);
                            if (end == -1) end = configText.Length;
                            if (end > start)
                                iconColor = configText.Substring(start, end - start).Trim().Replace("\"", "");
                        }

                        if (configText.Contains("CardClass ="))
                        {
                            int start = configText.IndexOf("CardClass =") + 11;
                            int end = configText.IndexOf(",", start);
                            if (end == -1) end = configText.Length;
                            if (end > start)
                                cardClass = configText.Substring(start, end - start).Trim().Replace("\"", "");
                        }

                        if (configText.Contains("Description ="))
                        {
                            int start = configText.IndexOf("Description =") + 13;
                            int end = configText.IndexOf(",", start);
                            if (end == -1) end = configText.Length;
                            if (end > start)
                                description = configText.Substring(start, end - start).Trim().Replace("\"", "");
                        }

                        if (configText.Contains("Color ="))
                        {
                            string colorName = "";
                            if (configText.Contains("danger")) colorName = "danger";
                            else if (configText.Contains("success")) colorName = "success";
                            else if (configText.Contains("warning")) colorName = "warning";
                            else if (configText.Contains("info")) colorName = "info";
                            else if (configText.Contains("secondary")) colorName = "secondary";

                            switch (colorName)
                            {
                                case "danger":
                                    cardColor = eZeroCore.Web.BlazorServerUI.General.Color.danger;
                                    break;
                                case "success":
                                    cardColor = eZeroCore.Web.BlazorServerUI.General.Color.success;
                                    break;
                                case "warning":
                                    cardColor = eZeroCore.Web.BlazorServerUI.General.Color.warning;
                                    break;
                                case "info":
                                    cardColor = eZeroCore.Web.BlazorServerUI.General.Color.info;
                                    break;
                                case "secondary":
                                    cardColor = eZeroCore.Web.BlazorServerUI.General.Color.secondary;
                                    break;
                                default:
                                    // 默认使用primary
                                    break;
                            }
                        }

                        isFree = configText.Contains("IsFree = true");
                    }
                    catch { }
                }

                // 处理URL
                string url = column.Redirect ?? "";
                if (!url.Contains("https://"))
                    url = "https://stockwe.com" + url;

                // 创建功能卡片
                var feature = new FeatureCard
                    {
                        Icon = icon,
                        Title = column.ColumnName,
                        Url = url,
                        IconColor = iconColor,
                        Description = description,
                        ButtonText = buttonText,
                        Color = cardColor,
                        CardClass = cardClass,
                        IsFree = isFree
                    };

                if (isMultiFactor)
                    modelFeaturesTemp.Add(feature);
                else
                    coreFeaturesTemp.Add(feature);
            }

            coreFeatures = coreFeaturesTemp;
            modelFeatures = modelFeaturesTemp;
        }
        catch
        {
            // 出错时添加提示卡片
            coreFeatures = new List<FeatureCard> {
                new() {
                    Icon = "fas fa-exclamation-circle",
                    Title = "数据加载错误",
                    Url = "#",
                    Description = "加载功能列表时发生错误",
                    ButtonText = "刷新",
                    Color = eZeroCore.Web.BlazorServerUI.General.Color.danger,
                    CardClass = "card-danger",
                    IconColor = "#ef4444"
                }
            };
            modelFeatures = new List<FeatureCard>();
        }
    }

    // 移除此方法，改为直接使用a标签
    // private void NavigateToFeature(string url) => NavigationManagerObj.NavigateTo(url, true);
}

<Main_Framework>
    <SubscriptionV3 PaymentVisable="@(!eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj.UserId)))">
        <div class="dashboard-page">
            <div class="feature-container">

                <!--  PC/平板端布局 -->
                <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
                    @if (coreFeatures.Any())
                    {
                        <!-- 第一部分：核心功能标题 -->
                        <div class="section-header">
                            <i class="fas fa-star" style="color: #10b981; font-size: 1.75rem;"></i>
                            <span class="section-title">@CObj.UserLang.GetLangValue("核心功能")</span>
                        </div>

                        <!-- 第二部分：功能卡片网格 -->
                        <div class="feature-grid">
                            @foreach (var card in coreFeatures)
                            {
                                <a href="@card.Url" class="feature-card @card.CardClass" style="text-decoration:none; color:inherit;">
                                    @if (card.IsFree)
                                    {
                                        <div class="free-badge">FREE</div>
                                    }
                                    <div class="card-icon-container">
                                        <div class="card-icon-wrapper">
                                            <i class="@card.Icon card-icon" style="color: @card.IconColor;"></i>
                                        </div>
                                    </div>
                                    <div class="card-title">
                                        @CObj.UserLang.GetLangValue(card.Title)
                                    </div>
                                    <div class="card-description">
                                        @CObj.UserLang.GetLangValue(card.Description)
                                    </div>
                                    <div class="card-button-container">
                                        <Ez_Btn Color="@card.Color"
                                                SmallBtn="false"
                                                Style="height: 36px; width: 110px; font-size: 0.8rem; border-radius: 8px; font-weight: 500;"
                                                BtnTitle="@card.ButtonText"
                                                WaitTimeMs="800"
                                                WaitingString="跳转中..."
                                                OnBtnClick="@(() => {})">
                                        </Ez_Btn>
                                    </div>
                                </a>
                            }
                        </div>
                    }

                    @if (modelFeatures.Any())
                    {
                        <!-- 第三部分：多因子模型系统标题 -->
                        <div class="section-header">
                            <i class="fas fa-cogs" style="color: #f59e0b; font-size: 1.75rem;"></i>
                            <span class="section-title">@CObj.UserLang.GetLangValue("多因子模型系统")</span>
                        </div>

                        <!-- 第四部分：多因子模型系统功能卡片 -->
                        <div class="model-grid">
                            @foreach (var card in modelFeatures)
                            {
                                <a href="@card.Url" class="feature-card @card.CardClass" style="text-decoration:none; color:inherit;">
                                    <div class="card-icon-container">
                                        <div class="card-icon-wrapper">
                                            <i class="@card.Icon card-icon" style="color: @card.IconColor;"></i>
                                        </div>
                                    </div>
                                    <div class="card-title">
                                        @CObj.UserLang.GetLangValue(card.Title)
                                    </div>
                                    <div class="card-description">
                                        @CObj.UserLang.GetLangValue(card.Description)
                                    </div>
                                    <div class="card-button-container">
                                        <Ez_Btn Color="@card.Color"
                                                SmallBtn="false"
                                                Style="height: 36px; width: 110px; font-size: 0.8rem; border-radius: 8px; font-weight: 500;"
                                                BtnTitle="@card.ButtonText"
                                                WaitTimeMs="800"
                                                WaitingString="跳转中..."
                                                OnBtnClick="@(() => {})">
                                        </Ez_Btn>
                                    </div>
                                </a>
                            }
                        </div>
                    }
                </MudHidden>

                <!-- 移动端布局 -->
                <MudHidden Breakpoint="Breakpoint.MdAndUp">
                    @if (coreFeatures.Any())
                    {
                        <div class="mobile-section">
                            <div class="section-header-mobile">
                                <div class="section-icon">🔥</div>
                                <div class="section-title-text">@CObj.UserLang.GetLangValue("核心功能")</div>
                                <div class="section-subtitle">@CObj.UserLang.GetLangValue("智能投资决策")</div>
                            </div>
                            <div class="grid-main">
                                @foreach (var card in coreFeatures)
                                {
                                    <a href="@card.Url" class="grid-item-main" style="text-decoration:none; color:inherit;">
                                        @if (card.IsFree)
                                        {
                                            <div class="free-badge-mobile">FREE</div>
                                        }
                                        <div class="item-icon-main">
                                            <i class="@card.Icon" style="color: @card.IconColor;"></i>
                                        </div>
                                        <div class="item-title-main">@card.Title</div>
                                    </a>
                                }
                            </div>
                        </div>
                    }

                    @if (modelFeatures.Any())
                    {
                        <!-- 移动端：多因子模型系统 (卡片列表) -->
                        <div class="mobile-section ai-section">
                            <div class="section-header-mobile ai">
                                <div class="section-icon">⚙️</div>
                                <div class="section-title-text">@CObj.UserLang.GetLangValue("多因子模型系统")</div>
                            </div>
                            <div class="ai-grid">
                                @foreach (var card in modelFeatures)
                                {
                                    <a href="@card.Url" class="ai-card" style="text-decoration:none; color:inherit;">
                                        <div class="ai-card-icon">
                                            <i class="@card.Icon" style="color: @card.IconColor;"></i>
                                        </div>
                                        <div class="ai-card-content">
                                            <div class="ai-card-title">@CObj.UserLang.GetLangValue(card.Title)</div>
                                            <div class="ai-card-desc">@CObj.UserLang.GetLangValue(card.Description)</div>
                                        </div>
                                        <div class="ai-card-arrow">
                                            <i class="fas fa-chevron-right"></i>
                                        </div>
                                    </a>
                                }
                            </div>
                        </div>
                    }

                    <!-- 移动端：底部安全区域 -->
                    <div class="mobile-bottom-safe"></div>
                </MudHidden>

            </div>
        </div>

        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <_FooterMenus />
        </MudHidden>

        <OffcanvasChatBox CObj="@CObj" />

    </SubscriptionV3>
</Main_Framework>
