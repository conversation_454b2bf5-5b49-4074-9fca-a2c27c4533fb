@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }
    [Parameter]
    public RenderFragment? ChildContent { get; set; }
    [Parameter]
    public bool IconVisible { get; set; } = true;

}

<div class="card-header text-start fs-9 p-2 pb-3 mb-2 text-capitalize">
    @if(IconVisible)
    {
        <i class="fa-regular fa-lightbulb me-2 text-warning"></i>
    }
    @ChildContent
</div>