﻿@using System.Text.Json
@inject IJSRuntime JS
@inject NavigationManager NavigationManagerObj
@code {

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string ColumnId { get; set; } = "";

    eZeroCore.Users.User User { get; set; } = new();

    bool VisabledSub { get; set; }

    string sThisUrl { get; set; } = "";

    string sContent { get; set; } = "";

    static string sOrderM_Id { get; set; } = "";
    static string sOrderY_Id { get; set; } = "";
    /// <summary>
    /// 按需服务-人工订单
    /// </summary>
    static string sOrderCostomize { get; set; } = "";

    //eZeroCore.Web.Document docItemM { get; set; } = new();
    //eZeroCore.Web.Document docItemY { get; set; } = new();

    eZeroCore.Web.Document MainItemDocObj { get; set; } = new("dcio6b3a8f38");

    protected override async Task OnInitializedAsync()
    {
        List<string> NewsOrders = eZeroCore.Web.Stk.UserHelper.GetNewsReaderItemIds();
        sOrderM_Id = NewsOrders.FirstOrDefault()!;
        sOrderY_Id = NewsOrders[1];
        sOrderCostomize = NewsOrders.LastOrDefault()!;

        User = new(CObj.UserId);
        VisabledSub = await VisabledSubInfo(User);
        sThisUrl = eZeroCore.Web.BlazorUI.WebQuest.GetUrlWithoutParams(NavigationManagerObj, false);
        sContent = eZeroCore.Web.Column.GetColumnContent("COL88663A577E", true, 60) ?? "";

        //docItemM = new(sOrderM_Id);
        //docItemY = new(sOrderY_Id);

        await Task.Delay(0);
    }

    protected override async void OnAfterRender(bool firstRender)
    {
        if (CObj.PageValue.Contains(".prod_"))
        {
            string subId = CObj.PageValue.Split('.').LastOrDefault() ?? "";
            if (!string.IsNullOrWhiteSpace(subId))
            {
                await CheckOutWithSubId(subId);
            }
        }
        base.OnAfterRender(firstRender);
    }

    async Task<bool> VisabledSubInfo(eZeroCore.Users.User user)
    {
        if (!string.IsNullOrWhiteSpace(ColumnId) && !ColumnId.Equals(eZeroCore.Web.Stk.WebDefaultValue.PayDocumentsColId, StringComparison.OrdinalIgnoreCase))
        {
            return false;    
        }
        if (user.CheckIsAdministrator() || user.CheckInRoleGroupLowLvAdmin())
        {
            return false;
        }
        else if (string.IsNullOrEmpty(eZeroCore.Users.UserLogs.AlreadyPurchased(user.UserId, sOrderM_Id))
            && string.IsNullOrEmpty(eZeroCore.Users.UserLogs.AlreadyPurchased(user.UserId, sOrderY_Id))
            && string.IsNullOrEmpty(eZeroCore.Users.UserLogs.AlreadyPurchased(user.UserId, sOrderCostomize))
        )
        {
            return true;
        }
        await Task.Delay(0);
        return false;
    }

    async Task CheckOutWithSubId(string subId)
    {
        var item = new eZeroCore.Users.Orders.Stripe.PurchaseItem
            {
                UserEmail = User.Email,
                IsSubscription = true,
                ProdIdForSubscriptio = subId,
                SuccessUrl = $"{sThisUrl.Replace($".{subId}", "")}",
                CancelUrl = sThisUrl
            };
        if (!CObj.IsLogin)
        {
            sThisUrl = sThisUrl.Split('.').FirstOrDefault() ?? "";
            NavigationManagerObj.NavigateTo(eZeroCore.Settings.GetLoginOrRegUrl(true, $"?from={sThisUrl}.{subId}"), true);
        }
        else
        {
            string sUrl = await eZeroCore.Users.Orders.PaymentOrders(item, JS);
            if (!string.IsNullOrEmpty(sUrl))
            {
                // 使用JavaScript进行重定向
                await JS.InvokeVoidAsync("eval", $"location.href='{sUrl}'");
            }
        }
    }
}

@if (VisabledSub)
{
    <!-- Title -->
    <div class="col-lg-12 text-center AdPageContent mb-4 px-0 @(CObj.IsMobile?"mt-3":"")">
        @(new MarkupString(sContent))
    </div>
    <!-- Row START -->
    <div class="row g-4 g-lg-5">

        @foreach(eZeroCore.Web.ServicesPrice services in MainItemDocObj.SettingsEntity.Services)
        {
            <div class="col-md-6 text-center d-grid gap-2 mx-auto">
                <div>
                    <span class="h4">
                        @(eZeroCore.ENumeric.OutMonetaryString((Decimal)services.Price,format:"C2"))
                    </span>
                    <small class="text-light">
                        /@services.ServiceName
                    </small>
                </div>
                <Ez_Btn OnBtnClick="async ()=>{ await CheckOutWithSubId(services.ProjectID);}" ClassName="btn btn-lg btn-phoenix-success me-1 mb-1">
                    @CObj.UserLang.GetLangValue("- 立即订阅 -")
                </Ez_Btn>
            </div>
        }
       
    </div>
}
else
{
    @ChildContent
}
