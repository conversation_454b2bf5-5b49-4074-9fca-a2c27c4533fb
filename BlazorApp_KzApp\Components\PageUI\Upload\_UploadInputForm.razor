@inject IJSRuntime JS
@inject BlzHelperObj CObj
@code {    

    [Parameter]
    public string? SetDefaultUrl { get; set; }

    [Parameter]
    public eZeroCore.Azure.UploadResult.FileType FileType { get; set; } = eZeroCore.Azure.UploadResult.FileType.Image;

    [Parameter]
    public EventCallback<string> UploadCallBack { get; set; }

    [Parameter]
    public string Placeholder { get; set; } = "";

    [Parameter]
    public string ClassName { get; set; }


    protected override void OnParametersSet()
    {        
        if (!string.IsNullOrEmpty(SetDefaultUrl) && BindUrlValue != SetDefaultUrl)
        {
            //_ = Generic.JsConsole(JS, "_UploadInputForm OnParametersSet");
            BindUrlValue = SetDefaultUrl;
        }
    }

    string BindUrlValue { get; set; } = "";

    async void UploadQualification(List<eZeroCore.Azure.UploadResult> uploadResults)
    {
        #region...
        string fileUrl = uploadResults.FirstOrDefault()?.StoredFileUrl ?? "";
        if (!string.IsNullOrWhiteSpace(fileUrl))
        {
            await UploadCallBack.InvokeAsync(fileUrl);
        }
        #endregion
    }
}

<div class="d-flex @ClassName">
    <input maxlength="600" type="text" readonly
           @bind-value=@(BindUrlValue)
           placeholder="@Placeholder"
           class="form-control flex-fill" />
    <Uploader CallBack="UploadQualification"
              FileType=@FileType
              Class="btn btn-phoenix-primary px-3"
              AllowMultiple=false>
        <i class="fa-solid fa-arrow-up-from-bracket"></i>
    </Uploader>
    <a href="@BindUrlValue" target="_blank" class="btn btn-phoenix-primary px-3">
        <i class="fa-solid fa-arrow-up-right-from-square"></i>
    </a>
</div>