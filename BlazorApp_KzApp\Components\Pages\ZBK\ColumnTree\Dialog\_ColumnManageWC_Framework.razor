﻿@code {
    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public RenderFragment? DialogActions { get; set; }

    [Parameter]
    public bool InDialogMode { get; set; } = true;
}

@if (InDialogMode)
{
    <MudDialogContent>

        <ChildContent>
            @ChildContent
        </ChildContent>
        <MudDialogActions>
            @DialogActions
        </MudDialogActions>

    </MudDialogContent>
}
else
{
    @ChildContent
}