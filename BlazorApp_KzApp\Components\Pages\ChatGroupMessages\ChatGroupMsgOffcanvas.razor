@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; } 
    string DomId { get; set; } = Guid.NewGuid().ToString();

    [Parameter]
    public EventCallback<string> OnClickUserLogo { get; set; }

    [Parameter]
    public EventCallback OnSendTickMin { get; set; }

    [Parameter]
    public EventCallback<int> OnSendTickSec { get; set; }

    [Parameter]
    public string RedirectUrl { get; set; } = "";

    [Parameter]
    public string? SetGroupId { get; set; }

    [Parameter]
    public string? Icon { get; set; }

    EzOffcanvas? ezOffcanvas;    
    async Task OnShowOrHideOffcanvas(bool show = true)
    {
        if(ezOffcanvas is not null)
        {
            await ezOffcanvas.ShowOffcanvas(show);
        }
    }

    async Task OnClickUser(string userid)
    {
        await OnShowOrHideOffcanvas(false);
        await OnClickUserLogo.InvokeAsync(userid);
    }
}

<EzOffcanvas Icon="@Icon" RedirectUrl="@RedirectUrl" ButtonTitle="Group ChatMessage" @ref=@ezOffcanvas HeaderVisable="false" CssClass="ChatBoxOffcanvas" Overflow_YHidden="true">

    <ChatBoxMain SetGroupId="@SetGroupId" OnlySignalR="!string.IsNullOrEmpty(RedirectUrl)" OnSendTickMin="@OnSendTickMin" OnSendTickSec="@OnSendTickSec" UseOffcanvas=true OnClickUserLogo="@OnClickUser" ShowOrHideOffcanvas="@OnShowOrHideOffcanvas" />

</EzOffcanvas>





