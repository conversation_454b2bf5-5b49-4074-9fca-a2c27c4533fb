@using BlazorApp_KzApp.Components.Pages.ChatGroupMessages
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.Pages.FrontEnd
@inject IJSRuntime JS

@page "/StockQuant/{param?}"

@code {
    [Parameter]
    public string param { get; set; } = "";

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    string SelectedStock { get; set; } = "";

    _CandlestickChart _Candlestick;
    _ModelSelector _modelSelector;

    // 初始化
    protected override async Task OnInitializedAsync()
    {
        await Task.CompletedTask;
        //return base.OnInitializedAsync();
    }

    // 页面加载完成后
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !string.IsNullOrEmpty(SelectedStock))
        {
            await OnSelectStock(SelectedStock);
        }
    }

	// 发送分钟线事件，每小时0分和30分钟发送一次
    async Task OnSendTickMinEvent()
    {
        DateTime time = DateTime.Now;
        if (time.Minute == 0 || time.Minute == 30)
        {
            _ = _modelSelector.Init();  // 初始化模型选择器
        }
        _ = _Candlestick.Init(SelectedStock, true); //初始化K线图
        _ = _Candlestick.SecTickEvent();    //触发秒级行情事件
        await JS.InvokeVoidAsync("console.log", $"{nameof(OnSendTickMinEvent)}:{DateTime.Now:f}");
        await Task.Delay(0);
    }

    // 选择股票
    async Task OnSelectStock(string stock)
    {
        SelectedStock = stock.ToUpper();
        if (_Candlestick is not null && !string.IsNullOrEmpty(SelectedStock))
        {
            await _Candlestick.Init(SelectedStock);
        }
        if (left_Right_Framework is not null)
        {
            await left_Right_Framework.CloseLeftContent();
        }
        await Task.CompletedTask;
    }

    _UI_Left_Right_Framework left_Right_Framework;

}


<Main_Framework ContentClass="mb-0" >

    <SubscriptionV3 PaymentVisable="@(!eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj.UserId)))">

        <_UI_Left_Right_Framework ChildContentClass="position-sticky" ChildContentStyle="top:30px" LeftContentBorder="false" @ref=@left_Right_Framework>
            <LeftContent>
                <_ModelSelector @ref=@_modelSelector OnSelectedStock="@OnSelectStock" />
            </LeftContent>
            <ChildContent>
                <_CandlestickChart DefaultSelectedMultiplier="eZeroCore.Web.Stk.APIData.Multiplier.Day_1" @ref=@_Candlestick />
            </ChildContent>
        </_UI_Left_Right_Framework>

        <_FooterMenus ActivateIndex="0" />
        
        <ChatGroupMsgOffcanvas RedirectUrl="/chat" OnSendTickMin="OnSendTickMinEvent" />

    </SubscriptionV3>

</Main_Framework>