@using System.Text
@using System.Globalization
@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public DateTime? DateTimeStart { get; set; }

    eZeroCore.Web.StkV2.StockModelList list { get; set; } = new();
    List<eZeroCore.Web.StkV2.StockModel> stocks { get; set; } = new();

    const string defaultInfoColId = "COL7D913586B1";
    eZeroCore.Web.Column colInfo { get; set; } = new(defaultInfoColId);
    async Task ModeInfo()
    {
        if (SelectedModal?.Model <= 2)
        {
            colInfo = new(defaultInfoColId);
        }
        else
        {
            colInfo = new("COL18377CF158");
        }
        await Task.CompletedTask;
    }

    protected override Task OnInitializedAsync()
    {
        SelectedModal = aIModels[1];
        return base.OnInitializedAsync();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (!firstRender)
        {
            return;
        }
        /*
        if (SelectedModal is null)
            {
            SelectedModal = aIModels.FirstOrDefault() ?? new();
        }
        */
        await Init();
        if (stocks.Count > 0 && stocks.FirstOrDefault() is not null)
        {
            SelectedStock = stocks.FirstOrDefault()!.stock;
        }
        await OnSelectedStock.InvokeAsync(SelectedStock);
    }

    async Task ClickSelectStock(string stock)
    {
        //await JS.InvokeVoidAsync("console.log", $"{stock}");
        if (!SelectedStock.Equals(stock))
        {
            SelectedStock = stock;
            await OnSelectedStock.InvokeAsync(SelectedStock);
        }
    }

    public async Task Init(bool onSelect = false, DateTime? newDateTime = null)
    {
        #region...

        if (AppSettings.IsTestModeAll)
        {
            //dateTimeStart = DateTime.Now.AddDays(-3);
        }
        if (newDateTime is not null)
        {
            DateTimeStart = newDateTime;
        }
        list = new()
        {
            TbRequest = new()
            {
                length = 12,
                Model = SelectedModal?.Model ?? 1,
                OrderName = "score",
                SetStartDate = DateTimeStart
            }
        };
        stocks = list.List();
        if (onSelect)
        {
            try
            {
                await ClickSelectStock(stocks.FirstOrDefault()!.stock);
            }
            catch { }
        }
        await ModeInfo();
        if (CObj.UserIsAdmin)
        {
            await JS.InvokeVoidAsync("console.log", $"AI选股Sql：{list.Sql}");
        }
        StateHasChanged();
        await Task.Delay(0);
        #endregion
    }

    [Parameter]
    public EventCallback<string> OnSelectedStock { get; set; }

    public string SelectedStock { get; set; } = "";

    AIModel? SelectedModal { get; set; }

    List<AIModel> aIModels
    {
        get
        {
            return GetModels();
        }
    }

    async Task OnSelectedModel(AIModel model)
    {
        SelectedModal = model;
        await Task.Delay(0);
        await Init(true);
    }

}

<div class="table-responsive scrollbar" style="min-height:500px;">

    <div class="rz-form-field-content px-2 p-1 mt-1 w-100 Selector">
        <div class="dropdown w-100">
            <div class="dropdown-toggle w-100 hand" data-bs-toggle="dropdown" aria-expanded="false">
                <span>
                    @SelectedModal?.Name
                </span>
                <b class="golden-text ms-2">
                    @SelectedModal?.SubTitle
                </b>
            </div>
            <ul class="dropdown-menu">
                @foreach (AIModel md in aIModels)
                {
                    <li @key=md.GetHashCode()>
                        <button @onclick="async () => { await OnSelectedModel(md); }"
                                class="dropdown-item @(md.Model == SelectedModal?.Model ? "Active" : "")" type="button">
                            <span>
                                @md.Name
                            </span>
                            <b class="golden-text ms-2">
                                @md.SubTitle
                            </b>
                        </button>
                    </li>
                }
            </ul>
        </div>
    </div>

    <table class="table table-sm fs-9 mb-0 table-hover">
        <thead>
            <tr>
                <th class="@_modelHtml.ThClass" scope="col">
                    @if (CObj.UserIsAdministrator && DateTimeStart is not null)
                    {
                        @DateTimeStart.Value.Date.ToString("yyyy-MM-dd")
                    }
                    else
                    {
                        <span>
                            &nbsp;
                        </span>
                    }
                </th>
                <th class="@_modelHtml.ThClass" scope="col">
                    @if (SelectedModal?.Model <= 2)
                    {
                        <span>
                            @CObj.UserLang.GetLangValue("买入价")
                        </span>
                    }
                    else if (SelectedModal?.Model >= 3 && SelectedModal?.Model < 4)
                    {
                        <span>
                            @CObj.UserLang.GetLangValue("等级")
                        </span>
                    }
                </th>
                <th class="@_modelHtml.ThClass" scope="col" style="width:120px;">
                    @if (SelectedModal?.Model <= 2)
                    {
                        <span>
                            @CObj.UserLang.GetLangValue("入场时间（美国东岸）")
                        </span>
                    }
                    else if (SelectedModal?.Model >= 3 && SelectedModal?.Model < 4)
                    {
                        <span>
                            @CObj.UserLang.GetLangValue("统计前30分钟数据")
                        </span>
                    }
                </th>
            </tr>
        </thead>
        <tbody class="list">
            @foreach (eZeroCore.Web.StkV2.StockModel model in stocks)
            {
                <tr @onclick="async () => { await ClickSelectStock(model.stock); }" class="hover-actions-trigger btn-reveal-trigger position-static hand @(model.stock.Equals(SelectedStock, StringComparison.OrdinalIgnoreCase) ? "bg-warning-subtle" : "")" style="height:50px;">
                    <_modelHtml AIModel="@model" />
                </tr>
            }
            @if (stocks.Count < 1)
            {
                <tr>
                    <td colspan="3">
                        <div class="p-3 mb-3 text-center">
                            @CObj.UserLang.GetLangValue("暂无新数据")
                        </div>
                    </td>
                </tr>
            }
        </tbody>
    </table>

    @(new MarkupString(colInfo.ColumnContentSummary))
    @(new MarkupString(colInfo.ColumnContent.Replace("<br/>", "")))

</div>
@code {

    public class AIModel
    {
        public string Name { get; set; } = "";
        public string SubTitle { get; set; } = "";
        public decimal Model { get; set; } = 1;
    }

    string sAddonString => CObj.UserLang.GetLangValue("AI智能选股 ");
    public List<AIModel> GetModels()
    {
        return [
            new AIModel(){
            Name=sAddonString+CObj.UserLang.GetLangValue("火力全开 日内看涨模型"),
            SubTitle=CObj.UserLang.GetLangValue("稳"),
            Model=1
        },
    new AIModel(){
            Name=sAddonString+CObj.UserLang.GetLangValue("火力全开 日内看涨模型"),
            SubTitle=CObj.UserLang.GetLangValue("猛"),
            Model=2
        },
    new AIModel(){
            Name=sAddonString+CObj.UserLang.GetLangValue("排山倒海（期权）日内上涨模型"),
            SubTitle="",
            Model=3
        },
    new AIModel(){
            Name=sAddonString+CObj.UserLang.GetLangValue("排山倒海（期权）日内下跌模型"),
            SubTitle="",
            Model=3.5m
        }
        ];
    }

}