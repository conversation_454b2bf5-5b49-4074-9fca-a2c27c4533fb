@inject NavigationManager NavigationManagerObj

@code {


    eZeroCore.Web.Column? colObj;
    protected override Task OnInitializedAsync()
    {
        colObj = new("COL2BA2DA6ABD");
        return base.OnInitializedAsync();
    }

}

<Main_Framework Container_Fluid="false" IsPageContent="true" LeftMenusVisible="false">

    <section class="pt-5 pb-9 px-md-10">

        @(new MarkupString(colObj?.ColumnContent ?? ""))

    </section>

    <_FooterV2 IsContentPage="true" />

</Main_Framework>