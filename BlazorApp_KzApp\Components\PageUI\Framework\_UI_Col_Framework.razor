@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public RenderFragment? TitleContent { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string Title { get; set; } = "";

    [Parameter]
    public NumberColsEnum NumberCols { get; set; } = NumberColsEnum._2;

    [Parameter]
    public string Class { get; set; } = "";
    [Parameter]
    public string Icon { get; set; } = "fas fa-list-alt";
    [Parameter]
    public string LabelClass { get; set; } = "";

    [Parameter]
    public bool NoLableIcon { get; set; }

    [Parameter]
    public bool Required { get; set; }

    string className = "";
    protected override void OnInitialized()
    {
        switch (NumberCols)
        {
            case NumberColsEnum.none:
                className = "col-auto flex-fill";
                break;
            case NumberColsEnum._1:
                className = "col-12";
                break;
            case NumberColsEnum._2:
                className = "col-12 col-xl-6";
                break;
            case NumberColsEnum._3:
                className = "col-12 col-xl-4";
                break;
            case NumberColsEnum._4:
                className = "col-12 col-xl-3";
                break;

        }
        base.OnInitialized();
    }

}

<div class="@className mb-0 mb-xl-2 @Class">
    @if (TitleContent is not null || !string.IsNullOrWhiteSpace(Title))
    {
        if (NoLableIcon)
        {
            @TitleContent
        }
        else
        {
            <label class="form-label p-1 pb-2 ps-1 @LabelClass">
                @if (!string.IsNullOrWhiteSpace(Title) || TitleContent is not null)
                {
                    <i class="@Icon me-1 @(Required?"text-warning-lighter":"")"></i>
                }
                @if (TitleContent is not null)
                {
                    @TitleContent
                }
                else
                {
                    @Title
                }
            </label>
        }
    }
    @ChildContent
</div>

@code
{
    public enum NumberColsEnum
    {
        none,
        _1,
        _2,
        _3,
        _4
    }
}