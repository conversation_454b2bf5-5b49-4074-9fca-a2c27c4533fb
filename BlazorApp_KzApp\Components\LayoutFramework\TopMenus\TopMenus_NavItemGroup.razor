@inject IJSRuntime JS
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web.Virtualization

@code {

     [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public eZeroCore.Web.Column ParentColumn { get; set; } = new();

    [Parameter]
    public string Url { get; set; } = "javascript:;";

    [Parameter]
    public bool ExistMenu { get; set; } = true;

    [Parameter]
    public string GroupName { get; set; } = "";
}

 @*/lib/Ph117/public/assets/*@

 @if (!string.IsNullOrEmpty(ParentColumn.ColumnId))
{
    <li class="nav-item dropdown">

        @if (ExistMenu)
        {
            <a class="nav-link dropdown-toggle lh-1 mt-1" href="javascript:;" role="button" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-haspopup="true" aria-expanded="false">
                <i class="far fa-newspaper me-2"></i>
                @GroupName.Truncate(Main_Framework.Settings.TopRootMenusMaxString)
            </a>
            <ul class="dropdown-menu navbar-dropdown-caret">

                <Virtualize ItemsProvider="new MudUI.MenusVirtualize(CObj, ParentColumn.ColumnId).LoadColumnsAsync" Context="column" ItemSize="6">
                    <ItemContent>
                        <_MenuLiItem ParentColumn="@column" @key="@($"Top_{column.ColumnId}")" />
                    </ItemContent>
                    <Placeholder>
                        Loading...
                    </Placeholder>
                </Virtualize>

            </ul>
        }
        else
        {
            <a class="nav-link lh-1 mt-1" href="@(Url)">
                <i class="far fa-newspaper me-2"></i>
                @GroupName.Truncate(Main_Framework.Settings.TopRootMenusMaxString)
            </a>
        }

    </li>
}
