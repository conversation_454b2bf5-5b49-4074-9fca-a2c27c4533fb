@using Bla<PERSON>App_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.Pages.ZBK.Docs
@using <PERSON><PERSON><PERSON>
@using BlazorApp_KzApp.Components.Pages.FrontEnd
@inject NavigationManager NavManagerObj
@inject IJSRuntime JS

@page "/Doc/{param?}"
@page "/Video/{param?}"

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public string param { get; set; } = "";

    eZeroCore.Web.Document SelectDoc { get; set; } = new();
    string sColumnUrl = "";
    protected override async Task OnInitializedAsync()
    {
        string sIdOrUrlWrite = param;
        if (string.IsNullOrEmpty(sIdOrUrlWrite))
        {
            sIdOrUrlWrite = CObj.PageValue.Split('.').FirstOrDefault() ?? "";
        }
        SelectDoc = eZeroCore.Web.Document.GetDoc(sIdOrUrlWrite);
        
        // 确保CObj.ThisDoc被正确设置，使Main_Framework等其他组件能识别当前文档
        if (CObj != null && SelectDoc != null && !string.IsNullOrEmpty(SelectDoc.DocId))
        {
            CObj.ThisDoc = SelectDoc;
            CObj.PageValue = param;
            if (CObj.GetType().GetProperty("ContentType") != null)
            {
                CObj.ContentType = eZeroCore.Web.Weicity.WeiPage.ContentType.Doc;
            }
        }
        
        eZeroCore.Web.Document.PageViewAddOn(SelectDoc.DocId);
        if (CObj.ThisColumn is not null)
        {
            sColumnUrl = CObj.ThisColumn.GetColumnUrl();
        }
        await Task.Delay(0);
    }

    async Task OnSelectedTab(int index)
    {
        if (index == 1)
        {
            await JS.InvokeVoidAsync("WeiCity.refreshToolsMenu");
        }
        //await JS.InvokeVoidAsync("console.log", $"OnSelectedTab:{index}");
        await Task.Delay(0);
    }
}

<Main_Framework Container_Fluid="false" IsPageContent="true" PageValue="@param">

    <_UIFramework Class="mt-2 bg-body" BodyClass="p-1 px-3">
        
        <_DepthReportPayment ColumnId="@SelectDoc.ColumnId">

            <RadzenTabs SelectedIndexChanged="@OnSelectedTab" RenderMode="TabRenderMode.Client" class="mt-1 p-0 rounded">
                <Tabs>
                    <RadzenTabsItem>
                        <Template>
                            <Ez_Btn ClassName="btn btn-subtle-primary me-1 py-1" OnBtnClick="async ()=>{ NavManagerObj.NavigateTo(sColumnUrl,true); }">
                                <i class="fa-solid fa-arrow-left-long"></i>
                            </Ez_Btn>
                            @if(CObj.UserIsAdmin)
                            {
                                //badge badge-phoenix badge-phoenix-info
                                <span data-url="@sColumnUrl" class="fs-8">
                                    @SelectDoc.ColumnName
                                </span>
                            }  
                            else
                            {
                                <span @onclick="async () => { NavManagerObj.NavigateTo(sColumnUrl, true); }" class="fs-8">
                                    @SelectDoc.ColumnName
                                </span>
                            }
                        </Template>
                        <ChildContent>
                            <_DocContent DocObj="@SelectDoc" />
                        </ChildContent>
                    </RadzenTabsItem>
                    @if (CObj.UserIsAdmin)
                    {
                        <RadzenTabsItem Text="@CObj.UserLang.GetLangValue("编辑")">

                            <_DocContentEditor DocObj="@SelectDoc" />

                        </RadzenTabsItem>
                    }
                </Tabs>
            </RadzenTabs>

        </_DepthReportPayment>

    </_UIFramework>

    <_FooterV2 IsContentPage="true" MobileFooterMenus="true" />

</Main_Framework>