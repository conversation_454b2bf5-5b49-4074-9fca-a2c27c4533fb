﻿@code {

    [Parameter]
    public EventCallback<string> SelectedValueChanged { get; set; }

    [Parameter]
    public string SetSelectedAreaId { get; set; } = "";

    string SelectedAreaID { get; set; } = "";
    string SelectedRootID { get; set; } = "";
    string SelectedL2ID { get; set; } = "";

    string setRoot = "";
    string setLv2 = "";

    private async Task SelectionChanged(ChangeEventArgs e)
    {
        if (e.Value is not null && !string.IsNullOrEmpty(e.Value.ToString()))
        {
            SelectedAreaID = e.Value.ToString()!;
            SetL2AreaListSelector();
        }
        else
        {
            SelectedAreaID = "";
            SelectedL2ID = "";
            Lv2Items.Clear();
            //StateHasChanged();
        }
        await SelectedValueChanged.InvokeAsync(SelectedAreaID);
    }
    
    void SetL2AreaListSelector()
    {
        eZeroCore.Web.NationsAndArea.Area.AreaList areaList = new()
            {
                ParentId = SelectedAreaID
            };
        Lv2Items = areaList.List();
    }
    private async Task SelectionL2Changed(ChangeEventArgs e)
    {
        if (e.Value is not null && !string.IsNullOrEmpty(e.Value.ToString()))
        {
            SelectedAreaID = e.Value.ToString()!;
        }
        else
        {
            SelectedAreaID = SelectedRootID;
        }
        await SelectedValueChanged.InvokeAsync(SelectedAreaID);
    }

    protected override Task OnInitializedAsync()
    {
        init();
        return base.OnInitializedAsync();
    }

    void init()
    {
        eZeroCore.Web.NationsAndArea.Area.AreaList areaListRoot = new();
        RootItems = areaListRoot.List();
        if (!string.IsNullOrEmpty(SetSelectedAreaId))
        {
            eZeroCore.Web.NationsAndArea.Area area = new(SetSelectedAreaId);
            setRoot = area.RootID;
            SelectedRootID = setRoot;
            SelectedAreaID = SelectedRootID;
            SetL2AreaListSelector();
            if (!setRoot.Equals(SetSelectedAreaId, StringComparison.OrdinalIgnoreCase))
            {
                setLv2 = eZeroCore.Web.NationsAndArea.Area.GetIdTreeRootID(area.IDTree, 1) ?? "";
                SelectedL2ID = setLv2;
                SelectedAreaID = SelectedL2ID;
            }
        }
    }

    private List<eZeroCore.Web.NationsAndArea.Area> RootItems = new();
    private List<eZeroCore.Web.NationsAndArea.Area> Lv2Items = new();
}

<h2 class="d-none">
    InTest:
    SetSelectedAreaId:@SetSelectedAreaId
    <br />
    setRoot:@setRoot
    <br />
    setLv2:@setLv2
    <br />
    SelectedAreaID=@SelectedAreaID
    <br />
    SelectedRootID=@SelectedRootID
    <br />
    SelectedL2ID=@SelectedL2ID
</h2>


<div class="d-flex justify-content-center g-3">
    <div class="w-100 p-2">
        <select class="form-select" @bind=SelectedRootID @oninput="SelectionChanged">
            <option value="">Select State</option>
            @foreach (var item in RootItems)
            {
                <option value="@item.ID" selected="@item.ID.Equals(setRoot,StringComparison.OrdinalIgnoreCase)">@item.AreaName</option>
            }
        </select>
    </div>
    <div class="w-100 p-2">
        <select class="form-select" disabled="@(string.IsNullOrEmpty(SelectedRootID))" @bind=SelectedL2ID @oninput="SelectionL2Changed">
            <option value="">Select Area</option>
            @if (Lv2Items.Count > 0)
            {
                @foreach (var item in Lv2Items)
                {
                    <option value="@item.ID" selected="@item.ID.Equals(setLv2,StringComparison.OrdinalIgnoreCase)">@item.AreaName</option>
                }
            }
        </select>
    </div>
</div>