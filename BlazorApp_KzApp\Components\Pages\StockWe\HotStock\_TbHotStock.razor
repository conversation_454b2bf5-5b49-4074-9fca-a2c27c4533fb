﻿@using <PERSON><PERSON><PERSON>A<PERSON>_KzApp.Components.Pages.ChatGroupMessages
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.PageUI.RzDataGrid
@using ZycCore.Models
@using ZycCore.Models.Dto
@using Ra<PERSON>zen
@using System.Globalization
@using MudBlazor

@* @inject EventSearchService EventService *@
@inject IJSRuntime JS
@implements IDisposable

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public DateTime SetStartDate { get; set; } = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime();

    private List<StockTwits.Data> Datas = new();
    bool isLoading = false;
    int PageSize = 10;
    int TotalCount { get; set; }
    int page { get; set; }

    string tbDomId = Guid.NewGuid().ToString();

    protected override async Task OnInitializedAsync()
    {
        // EventService.OnEnterSearchPressed += OnEnterPressed;
        await Generic.Loading(JS);
        await LoadData();
        await Generic.Loading(JS, false);
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadData();
    }

    private async Task LoadData(LoadDataArgs? args = null)
    {
        isLoading = true;
        StateHasChanged();
        await Task.Delay(10);
        
        StockTwitsDto dto = new()
        {
            DateStart = SetStartDate,
            DateEnd = SetStartDate,
            StockCode = Stock,
            type = activeButtonIndex
        };
        
        StockTwits.List list = new();
        Datas = await list.DataTableListAsync(args, PageSize, dto);
        TotalCount = list.TotalCount;
        page = list.SetPageNumber;
        isLoading = false;
    }

    public string Stock { get; set; } = "";
    
    public async Task SearchByStockCode(string stock)
    {
        Stock = stock.Trim();
        await Generic.Loading(JS);
        await LoadData();
        await Generic.Loading(JS, false);
    }

    void OnEnterPressed(string inputValue)
    {
        InvokeAsync(async () =>
        {
            if (!string.IsNullOrEmpty(inputValue))
            {
                Stock = inputValue;
            }
            else
            {
                Stock = "";
            }

            await LoadData();
        });
    }
    
    private int activeButtonIndex = 0;
    private string IsActive(int index)
    {
        return index == activeButtonIndex ? "active" : "";
    }
    
    public async Task InitDataTable(int type = 0)
    {
        activeButtonIndex = type;
        await LoadData();
    }
    
    private string FormatNumber(decimal? value, int decimals = 0)
    {
        if (value == null) return "--";
        return value.Value.ToString($"N{decimals}", CultureInfo.InvariantCulture);
    }

    public void Dispose()
    {
        // EventService.OnEnterSearchPressed -= OnEnterPressed;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await Task.Delay(500);
            await JS.InvokeVoidAsync("eLoading", "1");
        }
    }
}

<_UIFramework IncludeRowDiv="false" Class="mt-3" HeaderContentClass="d-flex justify-content-between py-1">
    <HeaderContent>
        <div style="margin-top: 10px;display: flex; align-items: center;color:rgba(245,156,26,1);font-weight:900;margin:6px;">
            <span style="font-weight: 900; font-size: 14px;display: flex; align-items: center;color:rgba(245,156,26,1);">
                <RadzenIcon Icon="whatshot" Style="margin-right: 4px;" />
                今天热门股
            </span>
        </div>
    </HeaderContent>
    <ChildContent>
        <div class="btn-group btn-group-sm mb-3" role="group">
            <button @onclick="()=>InitDataTable(0)" type="button"
                    class="btn btn-outline-warning @(IsActive(0))">
                关注
            </button>
            <button @onclick="()=>InitDataTable(1)" type="button"
                    class="btn btn-outline-warning @(IsActive(1))">
                最活跃
            </button>
            <button @onclick="()=>InitDataTable(2)" type="button"
                    class="btn btn-outline-warning @(IsActive(2))">
                趋势增加
            </button>
        </div>

        <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert="true">
            <RzDataTable TItem="StockTwits.Data"
                         Slim="true"
                         AllowPaging=true
                         AllowColumnResize="true"
                         Data="@Datas" PageSize="@PageSize"
                         IsLoading="@isLoading"
                         Count="@TotalCount" LoadData="@LoadData">

                <RadzenDataGridColumn TItem="StockTwits.Data" property="CreateDate" Sortable="false" Width="100px" Title="日期" Frozen="true">
                    <Template Context="data">
                        <span>
                            @data.CreateDate?.ToString("MM/dd/yyyy HH:mm")
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="StockTwits.Data" property="Rank" Width="80px" Sortable="false" Title="排行榜">
                    <Template Context="data">
                        <span>
                            @data.Rank
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="StockTwits.Data" property="Stock" Width="80px" Sortable="true" Title="代号" Frozen="true">
                    <Template Context="data">
                        <span >
                            @data.Stock
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="StockTwits.Data" property="Name" Width="200px" Sortable="true" Title="公司名">
                    <Template Context="data">
                        <div style="font-size:12px; font-weight:500; color:white; word-wrap: break-word; word-break: break-all; line-height: 1.2;">
                            @data.Name
                        </div>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="StockTwits.Data" property="Count" Width="100px" Sortable="true" Title="活跃数">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            @FormatNumber(data.Count)
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="StockTwits.Data" property="Price" Width="100px" Sortable="true" Title="股价">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            $@FormatNumber(data.Price, 2)
                        </span>
                    </Template>
                </RadzenDataGridColumn>

                <RadzenDataGridColumn TItem="StockTwits.Data" property="PriceChange" Width="100px" Sortable="true" Title="股价变动">
                    <Template Context="data">
                        <span style="display:block; text-align:right;">
                            @(data.PriceChange)%
                        </span>
                    </Template>
                </RadzenDataGridColumn>
            </RzDataTable>
        </MudHidden>

        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <div class="mobile-container">
                @if (isLoading)
                {
                    <div style="display:flex; justify-content:center; margin:12px 0;">
                        <MudProgressCircular Color="Color.Primary" Indeterminate="true" />
                    </div>
                }
                else if (Datas?.Count > 0)
                {
                    @foreach (var item in Datas)
                    {
                        var rowIndex = Datas.IndexOf(item);

                        <div class="mobileDatatable" style="padding:1px 0; border-bottom:1px solid rgba(255,255,255,0.1);margin-bottom:3px">
                            <!-- 移动端表格头部组件 -->
                            <_MobileTableHeader TimeValue="@item.CreateDate">
                                <CustomContent>
                                    @item.Name
                                </CustomContent>
                            </_MobileTableHeader>

                            <!-- 使用左右布局组件 -->
                            <_LeftRightLayout LeftWidth="27%">
                                <LeftContent>
                                    <!-- 股票代号 -->
                                    <div style="text-align:center;">
                                        <span style="background-color:#212529; color:white; padding:1px 4px; border-radius:3px; font-size:17px; font-weight:bold;">
                                            @item.Stock
                                        </span>
                                    </div>
                                </LeftContent>
                                <RightContent>
                                    <!-- 使用Grid布局 -->
                                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; grid-template-rows: auto; gap: 4px 12px; margin-left: 8px;">
                                        
                                        <!-- 第一行：排名、活跃数、股价、股价变动 -->
                                        <div style="grid-column: 1; grid-row: 1;">
                                            <div class="gd-label">排名</div>
                                            <div class="gd-value">@item.Rank</div>
                                        </div>

                                        <div style="grid-column: 2; grid-row: 1;">
                                            <div class=gd-label>活跃数</div>
                                            <div class="gd-value">@FormatNumber(item.Count)</div>
                                        </div>

                                        <div style="grid-column: 3; grid-row: 1;">
                                            <div class="gd-label">股价</div>
                                            <div class="gd-value">$@FormatNumber(item.Price, 2)</div>
                                        </div>

                                        <div style="grid-column: 4; grid-row: 1;">
                                            <div class="gd-label">股价变动</div>
                                            <div class="gd-value">
                                                @{
                                                    string priceChangeColor = "#6c757d";
                                                    if (item.PriceChange > 0)
                                                        priceChangeColor = "#28a745";
                                                    else if (item.PriceChange < 0)
                                                        priceChangeColor = "#dc3545";
                                                }
                                                <span style="color:@priceChangeColor">@(item.PriceChange)%</span>
                                            </div>
                                        </div>
                                    </div>
                                </RightContent>
                            </_LeftRightLayout>
                        </div>
                    }

                    <div style="display:flex; justify-content:center; margin-top:16px;">
                        <MudPagination Count="@((int)Math.Ceiling((double)TotalCount / PageSize))" 
                                       SelectedChanged="async (int page) => { await LoadData(new LoadDataArgs { Skip = (page - 1) * PageSize, Top = PageSize }); }" 
                                       Selected="page" />
                    </div>
                }
                else
                {
                    <div style="text-align:center; margin:16px 0; color:#6c757d;">
                        <span>暂无数据</span>
                    </div>
                }
            </div>
        </MudHidden>
    </ChildContent>
</_UIFramework>
