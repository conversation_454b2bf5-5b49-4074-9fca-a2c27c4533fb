{"version": 3, "file": "travel-agency-dashboard.js", "sources": ["../../../src/js/theme/charts/echarts/echarts-utils.js", "../../../src/js/theme/charts/echarts/booking-value-chart.js", "../../../src/js/theme/charts/echarts/bookings-chart.js", "../../../src/js/theme/charts/echarts/cancel-booking-chart.js", "../../../src/js/theme/charts/echarts/commission-chart.js", "../../../src/js/theme/charts/echarts/country-wise-visitors-chart.js", "../../../src/js/theme/charts/echarts/financial-activities-chart.js", "../../../src/js/theme/charts/echarts/gross-profit.js", "../../../src/js/theme/charts/echarts/holidays-next-month.js", "../../../src/js/pages/travel-agency-dashboard.js"], "sourcesContent": ["// import * as echarts from 'echarts';\r\nconst { merge } = window._;\r\n\r\n// form config.js\r\nexport const echartSetOption = (\r\n  chart,\r\n  userOptions,\r\n  getDefaultOptions,\r\n  responsiveOptions\r\n) => {\r\n  const { breakpoints, resize } = window.phoenix.utils;\r\n  const handleResize = options => {\r\n    Object.keys(options).forEach(item => {\r\n      if (window.innerWidth > breakpoints[item]) {\r\n        chart.setOption(options[item]);\r\n      }\r\n    });\r\n  };\r\n\r\n  const themeController = document.body;\r\n  // Merge user options with lodash\r\n  chart.setOption(merge(getDefaultOptions(), userOptions));\r\n\r\n  const navbarVerticalToggle = document.querySelector(\r\n    '.navbar-vertical-toggle'\r\n  );\r\n  if (navbarVerticalToggle) {\r\n    navbarVerticalToggle.addEventListener('navbar.vertical.toggle', () => {\r\n      chart.resize();\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    });\r\n  }\r\n\r\n  resize(() => {\r\n    chart.resize();\r\n    if (responsiveOptions) {\r\n      handleResize(responsiveOptions);\r\n    }\r\n  });\r\n  if (responsiveOptions) {\r\n    handleResize(responsiveOptions);\r\n  }\r\n\r\n  themeController.addEventListener(\r\n    'clickControl',\r\n    ({ detail: { control } }) => {\r\n      if (control === 'phoenixTheme') {\r\n        chart.setOption(window._.merge(getDefaultOptions(), userOptions));\r\n      }\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    }\r\n  );\r\n};\r\n// -------------------end config.js--------------------\r\n\r\nconst echartTabs = document.querySelectorAll('[data-tab-has-echarts]');\r\nif (echartTabs) {\r\n  echartTabs.forEach(tab => {\r\n    tab.addEventListener('shown.bs.tab', e => {\r\n      const el = e.target;\r\n      const { hash } = el;\r\n      const id = hash || el.dataset.bsTarget;\r\n      const content = document.getElementById(id.substring(1));\r\n      const chart = content?.querySelector('[data-echart-tab]');\r\n      if (chart) {\r\n        window.echarts.init(chart).resize();\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport const tooltipFormatter = (params, dateFormatter = 'MMM DD') => {\r\n  let tooltipItem = ``;\r\n  params.forEach(el => {\r\n    tooltipItem += `<div class='ms-1'>\r\n        <h6 class=\"text-body-tertiary\"><span class=\"fas fa-circle me-1 fs-10\" style=\"color:${\r\n          el.borderColor ? el.borderColor : el.color\r\n        }\"></span>\r\n          ${el.seriesName} : ${\r\n      typeof el.value === 'object' ? el.value[1] : el.value\r\n    }\r\n        </h6>\r\n      </div>`;\r\n  });\r\n  return `<div>\r\n            <p class='mb-2 text-body-tertiary'>\r\n              ${\r\n                window.dayjs(params[0].axisValue).isValid()\r\n                  ? window.dayjs(params[0].axisValue).format(dateFormatter)\r\n                  : params[0].axisValue\r\n              }\r\n            </p>\r\n            ${tooltipItem}\r\n          </div>`;\r\n};\r\n\r\nexport const handleTooltipPosition = ([pos, , dom, , size]) => {\r\n  // only for mobile device\r\n  if (window.innerWidth <= 540) {\r\n    const tooltipHeight = dom.offsetHeight;\r\n    const obj = { top: pos[1] - tooltipHeight - 20 };\r\n    obj[pos[0] < size.viewSize[0] / 2 ? 'left' : 'right'] = 5;\r\n    return obj;\r\n  }\r\n  return null; // else default behaviour\r\n};\r\n", "import { echartSetOption } from './echarts-utils';\r\n/* -------------------------------------------------------------------------- */\r\n/*                     Echart booking value                                 */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst bookingValueChartInit = () => {\r\n  const { getColor, getData, getDates } = window.phoenix.utils;\r\n  const $echartBookingValue = document.querySelector('.echart-booking-value');\r\n  const tooltipFormatter = params => {\r\n    const currentDate = window.dayjs(params[0].axisValue);\r\n    const prevDate = window.dayjs(params[0].axisValue).subtract(1, 'month');\r\n\r\n    const result = params.map((param, index) => ({\r\n      value: param.value,\r\n      date: index > 0 ? prevDate : currentDate,\r\n      color: param.color\r\n    }));\r\n\r\n    let tooltipItem = ``;\r\n    result.forEach((el, index) => {\r\n      tooltipItem += `<h6 class=\"fs-9 ${\r\n        index > 0 && 'mb-0'\r\n      }\"><span class=\"fas fa-circle me-2\" style=\"color:${el.color}\"></span>\r\n      ${el.date.format('MMM DD')} : <span class=\"fw-normal\">${el.value}</span>\r\n    </h6>`;\r\n    });\r\n    return `<div class='ms-1'>\r\n              ${tooltipItem}\r\n            </div>`;\r\n  };\r\n\r\n  if ($echartBookingValue) {\r\n    const userOptions = getData($echartBookingValue, 'echarts');\r\n    const chart = window.echarts.init($echartBookingValue);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: 10,\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        formatter: params => tooltipFormatter(params),\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      xAxis: [\r\n        {\r\n          type: 'category',\r\n          data: getDates(\r\n            new Date('11/1/2023'),\r\n            new Date('11/7/2023'),\r\n            1000 * 60 * 60 * 24\r\n          ),\r\n          show: true,\r\n          boundaryGap: false,\r\n          axisLine: {\r\n            show: true,\r\n            lineStyle: { color: getColor('secondary-bg') }\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            formatter: value => window.dayjs(value).format('DD MMM'),\r\n            showMinLabel: true,\r\n            showMaxLabel: false,\r\n            color: getColor('secondary-color'),\r\n            align: 'left',\r\n            interval: 5,\r\n            fontFamily: 'Nunito Sans',\r\n            fontWeight: 600,\r\n            fontSize: 12.8\r\n          }\r\n        }\r\n      ],\r\n      yAxis: {\r\n        show: false,\r\n        type: 'value',\r\n        boundaryGap: false\r\n      },\r\n      series: [\r\n        {\r\n          type: 'line',\r\n          data: [150, 100, 300, 200, 250, 180, 250],\r\n          showSymbol: false,\r\n          symbol: 'circle',\r\n          lineStyle: {\r\n            width: 2,\r\n            color: getColor('warning')\r\n          },\r\n          emphasis: {\r\n            lineStyle: {\r\n              color: getColor('warning')\r\n            }\r\n          },\r\n          itemStyle: {\r\n            color: getColor('warning')\r\n          },\r\n          zlevel: 1\r\n        }\r\n      ],\r\n      grid: { left: 5, right: 5, top: 5, bottom: 0 }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default bookingValueChartInit;\r\n", "import {\r\n  echartSetOption,\r\n  tooltipFormatter,\r\n  handleTooltipPosition\r\n} from './echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                     Echart Bar booking                                 */\r\n/* -------------------------------------------------------------------------- */\r\nconst { echarts } = window;\r\n\r\nconst bookingsChartInit = () => {\r\n  const { getColor, getData, getPastDates, getItemFromStore, rgbaColor } =\r\n    window.phoenix.utils;\r\n  const $bookingsChart = document.querySelector('.echart-bookings');\r\n\r\n  const fullfilledData = [\r\n    [3500, 2500, 2600, 3400, 2300, 3200, 2800, 2800],\r\n    [2736, 3874, 4192, 1948, 3567, 4821, 2315, 3986],\r\n    [2789, 3895, 2147, 4658, 1723, 3210, 4386, 1974]\r\n  ];\r\n\r\n  const cencelledData = [\r\n    [-1500, -2700, -1100, -1400, -1600, -1400, -1100, -2700],\r\n    [-3874, -2631, -4422, -1765, -3198, -4910, -2087, -4675],\r\n    [-2789, -3895, -2147, -4658, -1723, -3210, -4386, -1974]\r\n  ];\r\n\r\n  if ($bookingsChart) {\r\n    const userOptions = getData($bookingsChart, 'echarts');\r\n    const chart = echarts.init($bookingsChart);\r\n    const getDefaultOptions = () => ({\r\n      color: getColor('body-highlight-bg'),\r\n      legend: {\r\n        data: ['Fulfilled', 'Cancelled'],\r\n        itemWidth: 16,\r\n        itemHeight: 16,\r\n        icon: 'circle',\r\n        itemGap: 32,\r\n        left: 0,\r\n        inactiveColor: getColor('quaternary-color'),\r\n        textStyle: {\r\n          color: getColor('secondary-color'),\r\n          fontWeight: 600,\r\n          fontFamily: 'Nunito Sans'\r\n        }\r\n      },\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position: (...params) => handleTooltipPosition(params),\r\n        formatter: params => tooltipFormatter(params),\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        axisLabel: {\r\n          color: getColor('secondary-text-emphasis'),\r\n          formatter: value => window.dayjs(value).format('MMM DD'),\r\n          fontFamily: 'Nunito Sans',\r\n          fontWeight: 600,\r\n          fontSize: 12.8\r\n        },\r\n        data: getPastDates(8),\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('border-color-translucent')\r\n          }\r\n        },\r\n        axisTick: false\r\n      },\r\n      yAxis: {\r\n        axisLabel: {\r\n          color: getColor('body-color'),\r\n          formatter: value => `${Math.abs(Math.round(value / 1000))}K`,\r\n          fontWeight: 700,\r\n          fontFamily: 'Nunito Sans'\r\n        },\r\n        splitLine: {\r\n          interval: 10,\r\n          lineStyle: {\r\n            color: getColor('border-color-translucent')\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Fulfilled',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          data: fullfilledData[0],\r\n          barWidth: '27%',\r\n          itemStyle: {\r\n            borderRadius: [4, 4, 0, 0],\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? getColor('info')\r\n                : getColor('info-light')\r\n          }\r\n        },\r\n        {\r\n          name: 'Cancelled',\r\n          type: 'bar',\r\n          stack: 'one',\r\n          barWidth: '27%',\r\n          data: cencelledData[0],\r\n          itemStyle: {\r\n            borderRadius: [0, 0, 4, 4],\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('info'), 0.5)\r\n                : getColor('info-lighter')\r\n          }\r\n        }\r\n      ],\r\n      grid: { left: 0, right: 8, top: 52, bottom: 0, containLabel: true }\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    const bookingSelect = document.querySelector('[data-booking-options]');\r\n    if (bookingSelect) {\r\n      bookingSelect.addEventListener('change', e => {\r\n        const { value } = e.currentTarget;\r\n        const data1 = fullfilledData[value];\r\n        const data2 = cencelledData[value];\r\n        chart.setOption({\r\n          series: [\r\n            {\r\n              data: data1\r\n            },\r\n            {\r\n              data: data2\r\n            }\r\n          ]\r\n        });\r\n      });\r\n    }\r\n  }\r\n};\r\n\r\nexport default bookingsChartInit;\r\n", "import { echartSetOption, handleTooltipPosition } from './echarts-utils';\r\n\r\n// dayjs.extend(advancedFormat);\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts cancel booking                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst cancelBookingChartInit = () => {\r\n  const { getColor, getData, getDates, getItemFromStore } =\r\n    window.phoenix.utils;\r\n  const cancelBookingChartEl = document.querySelector('.chart-cancel-booking');\r\n\r\n  if (cancelBookingChartEl) {\r\n    const userOptions = getData(cancelBookingChartEl, 'echarts');\r\n    const chart = window.echarts.init(cancelBookingChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: getColor('primary'),\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        position: (...params) => handleTooltipPosition(params),\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        formatter: params => {\r\n          return `<strong>${window\r\n            .dayjs(params.name)\r\n            .format('DD MMM')}:</strong> ${params.value}`;\r\n        },\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n        data: getDates(\r\n          new Date('11/1/2023'),\r\n          new Date('11/6/2023'),\r\n          1000 * 60 * 60 * 24\r\n        )\r\n      },\r\n      yAxis: {\r\n        show: false\r\n      },\r\n      series: [\r\n        {\r\n          type: 'bar',\r\n          barWidth: 3,\r\n          data: [120, 150, 100, 120, 110, 160],\r\n          symbol: 'none',\r\n          itemStyle: {\r\n            borderRadius: [0.5, 0.5, 0, 0],\r\n            colos:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? getColor('info')\r\n                : getColor('info-light')\r\n          }\r\n        }\r\n      ],\r\n      grid: { right: 5, left: 0, bottom: 0, top: 0 }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default cancelBookingChartInit;\r\n", "// import * as echarts from 'echarts';\r\nimport { echartSetOption, handleTooltipPosition } from './echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts commission                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst { echarts } = window;\r\n\r\nconst commissionChartInit = () => {\r\n  const { getData, getColor } = window.phoenix.utils;\r\n  const $echartCommission = document.querySelector('.echart-commission');\r\n\r\n  if ($echartCommission) {\r\n    const userOptions = getData($echartCommission, 'options');\r\n    const chart = echarts.init($echartCommission);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        position: (...params) => handleTooltipPosition(params),\r\n        transitionDuration: 0,\r\n        formatter: params => {\r\n          return `<strong>${params.seriesName}:</strong> ${params.value}%`;\r\n        },\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      series: [\r\n        {\r\n          type: 'gauge',\r\n          name: 'Commission',\r\n          startAngle: 90,\r\n          endAngle: -270,\r\n          radius: '90%',\r\n          pointer: {\r\n            show: false\r\n          },\r\n          progress: {\r\n            show: true,\r\n            overlap: false,\r\n            roundCap: true,\r\n            clip: false,\r\n            itemStyle: {\r\n              color: getColor('primary')\r\n            }\r\n          },\r\n          axisLine: {\r\n            lineStyle: {\r\n              width: 3,\r\n              color: [[1, getColor('secondary-bg')]]\r\n            }\r\n          },\r\n          splitLine: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          data: [\r\n            {\r\n              value: 70\r\n            }\r\n          ],\r\n          detail: {\r\n            show: false\r\n          }\r\n        }\r\n      ]\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default commissionChartInit;\r\n", "import { echartSetOption, handleTooltipPosition } from './echarts-utils';\r\n\r\n// dayjs.extend(advancedFormat);\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts cancel booking                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst countryWiseVisitorsChartInit = () => {\r\n  const { getColor, getData, getRandomNumber, getItemFromStore } =\r\n    window.phoenix.utils;\r\n  const countryWiseVisitorsChartEl = document.querySelector(\r\n    '.echart-country-wise-visitors'\r\n  );\r\n\r\n  const data = [\r\n    127, 156, 183, 110, 195, 129, 176, 147, 163, 199, 158, 115, 191, 105, 143,\r\n    179, 120, 168, 137, 185, 154, 122, 197, 112, 144, 170, 193, 118, 166, 151,\r\n    187, 134, 162, 107, 192, 152, 114, 198\r\n  ];\r\n  const axisData = [\r\n    1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21,\r\n    22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38\r\n  ];\r\n\r\n  const tooltipFormatter = params => `\r\n    <div>\r\n        <h6 class=\"fs-9 text-700 mb-0\"><span class=\"fas fa-circle me-1 text-primary-light\"></span>\r\n          Users : <span class=\"fw-normal\">${params[0].value}</span>\r\n        </h6>\r\n    </div>\r\n    `;\r\n\r\n  if (countryWiseVisitorsChartEl) {\r\n    const userOptions = getData(countryWiseVisitorsChartEl, 'echarts');\r\n    const chart = window.echarts.init(countryWiseVisitorsChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        position(pos, params, dom, rect, size) {\r\n          return handleTooltipPosition(pos, params, dom, rect, size);\r\n        },\r\n        formatter: tooltipFormatter,\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      xAxis: {\r\n        type: 'category',\r\n\r\n        axisLabel: {\r\n          show: false\r\n        },\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        axisLine: {\r\n          show: false\r\n        },\r\n        boundaryGap: [0.2, 0.2],\r\n        data: axisData\r\n      },\r\n      yAxis: {\r\n        type: 'value',\r\n        scale: true,\r\n        boundaryGap: false,\r\n        axisLabel: {\r\n          show: false\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        min: 100,\r\n        max: 200\r\n      },\r\n      series: [\r\n        {\r\n          type: 'bar',\r\n          barMaxWidth: 8,\r\n          barGap: 5,\r\n          data,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? getColor('primary')\r\n                : getColor('primary-light'),\r\n            borderRadius: [2, 2, 0, 0]\r\n          }\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 0,\r\n        left: 0,\r\n        bottom: 0,\r\n        top: 0\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n\r\n    const userCounterDom = document.querySelector('.real-time-user');\r\n\r\n    setInterval(() => {\r\n      const rndData = getRandomNumber(130, 200);\r\n      data.shift();\r\n      data.push(rndData);\r\n      axisData.shift();\r\n      axisData.push(getRandomNumber(37, 100));\r\n      userCounterDom.innerHTML = rndData;\r\n\r\n      chart.setOption({\r\n        xAxis: {\r\n          data: axisData\r\n        },\r\n        series: [\r\n          {\r\n            data\r\n          }\r\n        ]\r\n      });\r\n    }, 2000);\r\n  }\r\n};\r\n\r\nexport default countryWiseVisitorsChartInit;\r\n", "import {\r\n  echartSetOption,\r\n  handleTooltipPosition,\r\n  tooltipFormatter\r\n} from './echarts-utils';\r\n\r\n// dayjs.extend(advancedFormat);\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Financial Activities                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst financialActivitiesChartInit = () => {\r\n  const { getColor, getData, getItemFromStore } = window.phoenix.utils;\r\n  const $financialActivitiesChartEl = document.querySelector(\r\n    '.echart-financial-Activities'\r\n  );\r\n\r\n  const profitData = [\r\n    [350000, 390000, 410700, 450000, 390000, 410700],\r\n    [245000, 310000, 420000, 480000, 530000, 580000],\r\n    [278450, 513220, 359890, 444567, 201345, 589000]\r\n  ];\r\n  const revenueData = [\r\n    [-810000, -640000, -630000, -590000, -620000, -780000],\r\n    [-482310, -726590, -589120, -674832, -811245, -455678],\r\n    [-432567, -688921, -517389, -759234, -601876, -485112]\r\n  ];\r\n  const expansesData = [\r\n    [-450000, -250000, -200000, -120000, -230000, -270000],\r\n    [-243567, -156789, -398234, -120456, -321890, -465678],\r\n    [-235678, -142345, -398765, -287456, -173890, -451234]\r\n  ];\r\n\r\n  if ($financialActivitiesChartEl) {\r\n    const userOptions = getData($financialActivitiesChartEl, 'options');\r\n    const chart = window.echarts.init($financialActivitiesChartEl);\r\n    const profitLagend = document.querySelector(`#${userOptions.optionOne}`);\r\n    const revenueLagend = document.querySelector(`#${userOptions.optionTwo}`);\r\n    const expansesLagend = document.querySelector(\r\n      `#${userOptions.optionThree}`\r\n    );\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [getColor('primary'), getColor('tertiary-bg')],\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        position: (...params) => handleTooltipPosition(params),\r\n        formatter: params => tooltipFormatter(params),\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      legend: {\r\n        data: ['Profit', 'Revenue', 'Expanses'],\r\n        show: false\r\n      },\r\n      xAxis: {\r\n        axisLabel: {\r\n          show: true,\r\n          margin: 12,\r\n          color: getColor('secondary-text-emphasis'),\r\n          formatter: value =>\r\n            `${Math.abs(Math.round((value / 1000) * 10) / 10)}k`,\r\n          fontFamily: 'Nunito Sans',\r\n          fontWeight: 700\r\n        },\r\n        splitLine: {\r\n          lineStyle: {\r\n            color: getColor('border-color-translucent')\r\n          }\r\n        }\r\n      },\r\n      yAxis: {\r\n        axisTick: {\r\n          show: false\r\n        },\r\n        data: [\r\n          'NOV-DEC',\r\n          'SEP-OCT',\r\n          'JUL-AUG',\r\n          'MAY-JUN',\r\n          'MAR-APR',\r\n          'JAN-FEB'\r\n        ],\r\n        axisLabel: {\r\n          color: getColor('secondary-text-emphasis'),\r\n          margin: 8,\r\n          fontFamily: 'Nunito Sans',\r\n          fontWeight: 700\r\n        },\r\n        axisLine: {\r\n          lineStyle: {\r\n            color: getColor('border-color-translucent')\r\n          }\r\n        }\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Profit',\r\n          stack: 'Total',\r\n          type: 'bar',\r\n          barWidth: 8,\r\n          roundCap: true,\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          itemStyle: {\r\n            borderRadius: [0, 4, 4, 0],\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? getColor('primary')\r\n                : getColor('primary-light')\r\n          },\r\n          data: profitData[0]\r\n        },\r\n        {\r\n          name: 'Revenue',\r\n          type: 'bar',\r\n          barWidth: 8,\r\n          barGap: '100%',\r\n          stack: 'Total',\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          itemStyle: {\r\n            borderRadius: [4, 0, 0, 4],\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? getColor('success')\r\n                : getColor('success-light')\r\n          },\r\n          data: revenueData[0]\r\n        },\r\n        {\r\n          name: 'Expanses',\r\n          type: 'bar',\r\n          barWidth: 8,\r\n          emphasis: {\r\n            focus: 'series'\r\n          },\r\n          itemStyle: {\r\n            borderRadius: [4, 0, 0, 4],\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? getColor('info')\r\n                : getColor('info-light')\r\n          },\r\n          data: expansesData[0]\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 20,\r\n        left: 3,\r\n        bottom: 0,\r\n        top: 16,\r\n        containLabel: true\r\n      },\r\n      animation: false\r\n    });\r\n\r\n    const responsiveOptions = {\r\n      xs: {\r\n        yAxis: {\r\n          axisLabel: {\r\n            show: false\r\n          }\r\n        },\r\n        grid: {\r\n          left: 15\r\n        }\r\n      },\r\n      sm: {\r\n        yAxis: {\r\n          axisLabel: {\r\n            margin: 32,\r\n            show: true\r\n          }\r\n        },\r\n        grid: {\r\n          left: 3\r\n        }\r\n      },\r\n      xl: {\r\n        yAxis: {\r\n          axisLabel: {\r\n            show: false\r\n          }\r\n        },\r\n        grid: {\r\n          left: 15\r\n        }\r\n      },\r\n      xxl: {\r\n        yAxis: {\r\n          axisLabel: {\r\n            show: true\r\n          }\r\n        },\r\n        grid: {\r\n          left: 3\r\n        }\r\n      }\r\n    };\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions, responsiveOptions);\r\n\r\n    profitLagend.addEventListener('click', () => {\r\n      profitLagend.classList.toggle('opacity-50');\r\n      chart.dispatchAction({\r\n        type: 'legendToggleSelect',\r\n        name: 'Profit'\r\n      });\r\n    });\r\n\r\n    revenueLagend.addEventListener('click', () => {\r\n      revenueLagend.classList.toggle('opacity-50');\r\n      chart.dispatchAction({\r\n        type: 'legendToggleSelect',\r\n        name: 'Revenue'\r\n      });\r\n    });\r\n\r\n    expansesLagend.addEventListener('click', () => {\r\n      expansesLagend.classList.toggle('opacity-50');\r\n      chart.dispatchAction({\r\n        type: 'legendToggleSelect',\r\n        name: 'Expanses'\r\n      });\r\n    });\r\n\r\n    const cetegorySelect = document.querySelector('[data-activities-options]');\r\n    if (cetegorySelect) {\r\n      cetegorySelect.addEventListener('change', e => {\r\n        const { value } = e.currentTarget;\r\n        const data1 = profitData[value];\r\n        const data2 = revenueData[value];\r\n        const data3 = expansesData[value];\r\n        chart.setOption({\r\n          series: [\r\n            {\r\n              data: data1\r\n            },\r\n            {\r\n              data: data2\r\n            },\r\n            {\r\n              data: data3\r\n            }\r\n          ]\r\n        });\r\n      });\r\n    }\r\n  }\r\n};\r\n\r\nexport default financialActivitiesChartInit;\r\n", "import { echartSetOption } from './echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                     Echart Bar booking                                 */\r\n/* -------------------------------------------------------------------------- */\r\nconst { echarts } = window;\r\n\r\nconst grossProfitInit = () => {\r\n  const { getColor, getData, rgbaColor, getItemFromStore } =\r\n    window.phoenix.utils;\r\n  const $grossProfit = document.querySelector('.echart-gross-profit');\r\n\r\n  const data = [\r\n    {\r\n      name: 'Flight',\r\n      value: 30,\r\n      itemStyle: {\r\n        color:\r\n          getItemFromStore('phoenixTheme') === 'dark'\r\n            ? getColor('primary')\r\n            : getColor('primary-light')\r\n      },\r\n      children: [\r\n        {\r\n          name: '1st class',\r\n          value: 5,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('primary'), 0.8)\r\n                : rgbaColor(getColor('primary-light'), 0.7)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: getColor('primary-dark')\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: 'Business',\r\n          value: 15,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('primary'), 0.7)\r\n                : rgbaColor(getColor('primary-light'), 0.5)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('primary-dark'), 0.9)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('primary-dark'), 0.8)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('primary-dark'), 0.7)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: 'Economy',\r\n          value: 10,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('primary'), 0.6)\r\n                : rgbaColor(getColor('primary-light'), 0.3)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('primary-dark'), 0.6)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('primary-dark'), 0.5)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      name: 'Package',\r\n      value: 50,\r\n      itemStyle: {\r\n        color:\r\n          getItemFromStore('phoenixTheme') === 'dark'\r\n            ? getColor('info')\r\n            : getColor('info-light')\r\n      },\r\n      children: [\r\n        {\r\n          name: 'Flight + Hotel',\r\n          value: 5,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('info'), 0.4)\r\n                : rgbaColor(getColor('info-light'), 0.3)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('info-dark'), 0.2)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: 'Flight + Event',\r\n          value: 20,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('info'), 0.5)\r\n                : rgbaColor(getColor('info-light'), 0.4)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('info-dark'), 0.3)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('info-dark'), 0.4)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('info-dark'), 0.5)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('info-dark'), 0.6)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: 'Flight + Hotel + Event',\r\n          value: 10,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('info'), 0.6)\r\n                : rgbaColor(getColor('info-light'), 0.55)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('info-dark'), 0.66)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('info-dark'), 0.7)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: 'Hotel + Event',\r\n          value: 5,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('info'), 0.7)\r\n                : rgbaColor(getColor('info-light'), 0.75)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('info-dark'), 0.8)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: 'Custom',\r\n          value: 10,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('info'), 0.8)\r\n                : rgbaColor(getColor('info-light'), 0.9)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('info-dark'), 0.9)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: getColor('info-dark')\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      name: 'Hotel',\r\n      value: 25,\r\n      itemStyle: {\r\n        color:\r\n          getItemFromStore('phoenixTheme') === 'dark'\r\n            ? getColor('success')\r\n            : getColor('success-light')\r\n      },\r\n      children: [\r\n        {\r\n          name: 'Rooms',\r\n          value: 10,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('success'), 0.8)\r\n                : rgbaColor(getColor('success-light'), 0.9)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: getColor('success-dark')\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('success-dark'), 0.88)\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: 'Resorts',\r\n          value: 15,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('success'), 0.7)\r\n                : rgbaColor(getColor('success-light'), 0.5)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('success-dark'), 0.77)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('success-dark'), 0.66)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('success-dark'), 0.55)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    },\r\n    {\r\n      name: 'Trip',\r\n      value: 15,\r\n      itemStyle: {\r\n        color:\r\n          getItemFromStore('phoenixTheme') === 'dark'\r\n            ? getColor('warning')\r\n            : getColor('warning-light')\r\n      },\r\n      children: [\r\n        {\r\n          name: 'Nature',\r\n          value: 5,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('warning'), 0.8)\r\n                : rgbaColor(getColor('warning-light'), 0.8)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: getColor('warning-dark')\r\n              }\r\n            }\r\n          ]\r\n        },\r\n        {\r\n          name: 'Events',\r\n          value: 10,\r\n          itemStyle: {\r\n            color:\r\n              getItemFromStore('phoenixTheme') === 'dark'\r\n                ? rgbaColor(getColor('warning'), 0.7)\r\n                : rgbaColor(getColor('warning-light'), 0.5)\r\n          },\r\n          children: [\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('warning-dark'), 0.7)\r\n              }\r\n            },\r\n            {\r\n              name: 'label-3',\r\n              value: 5,\r\n              itemStyle: {\r\n                color: rgbaColor(getColor('warning-dark'), 0.5)\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    }\r\n  ];\r\n\r\n  const colors = [\r\n    getColor('primary-light'),\r\n    getColor('info-light'),\r\n    getColor('success-light'),\r\n    getColor('warning-light')\r\n  ];\r\n\r\n  if ($grossProfit) {\r\n    const userOptions = getData($grossProfit, 'echarts');\r\n    const chart = echarts.init($grossProfit);\r\n    const getDefaultOptions = () => ({\r\n      color: colors,\r\n      tooltip: {\r\n        trigger: 'item',\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      series: [\r\n        {\r\n          type: 'sunburst',\r\n          center: ['50%', '50%'],\r\n          data,\r\n          sort(a, b) {\r\n            if (a.depth === 1) {\r\n              return b.getValue() - a.getValue();\r\n            }\r\n            return a.dataIndex - b.dataIndex;\r\n          },\r\n          label: {\r\n            show: false\r\n          },\r\n          levels: [\r\n            {},\r\n            {\r\n              r0: 0,\r\n              r: 53,\r\n              itemStyle: {\r\n                borderWidth: 2,\r\n                borderColor: getColor('body-bg')\r\n              },\r\n              label: {\r\n                show: false\r\n              },\r\n              blur: {\r\n                itemStyle: {\r\n                  borderWidth: 6.5\r\n                }\r\n              }\r\n            },\r\n            {\r\n              r0: 65,\r\n              r: 110,\r\n              itemStyle: {\r\n                borderWidth: 2,\r\n                borderColor: getColor('body-bg')\r\n              },\r\n              label: {\r\n                show: false\r\n              }\r\n            },\r\n            {\r\n              r0: 120,\r\n              r: 125,\r\n              itemStyle: {\r\n                borderWidth: 2,\r\n                borderColor: getColor('body-bg')\r\n              },\r\n              label: {\r\n                show: false\r\n              }\r\n            }\r\n          ]\r\n        }\r\n      ]\r\n    });\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default grossProfitInit;\r\n", "import { echartSetOption } from './echarts-utils';\r\n\r\nconst holidaysNextMonthChartInit = () => {\r\n  const { getColor, getData, getItemFromStore, rgbaColor } =\r\n    window.phoenix.utils;\r\n  const $holidaysNextMonthchartEl = document.querySelector(\r\n    '.echart-holidays-next-month'\r\n  );\r\n  const { echarts } = window;\r\n  const numbers = [\r\n    84, 572, 193, 427, 649, 318, 765, 112, 490, 231, 674, 815, 447, 56, 903,\r\n    178, 629, 394, 742, 295, 518, 67, 936, 129, 681, 862, 410, 553, 268, 719,\r\n    42, 589, 334, 786, 155, 607, 878, 525, 449, 206, 659, 99, 472, 724, 261,\r\n    834, 389, 613, 157, 702, 451, 82, 545, 293, 736, 870, 104, 681, 321, 574,\r\n    136, 689, 840, 470, 127, 598, 354, 807, 215, 767, 498, 51, 904, 176, 629,\r\n    389, 731, 268, 611, 155, 702, 453, 82, 537, 294, 747, 881, 109, 662, 405,\r\n    858, 515, 47, 936, 189, 641, 312, 764, 236, 579, 135, 688, 429, 71, 624,\r\n    370, 822, 173, 725, 476, 29, 880, 125, 677, 338, 791, 216, 568, 115, 666,\r\n    409, 861, 502, 44, 907, 160, 612, 374, 826, 279, 731, 182, 735, 478, 27,\r\n    879, 120, 672, 335, 788, 227, 580, 123, 676, 421, 74, 627, 381, 834, 185,\r\n    738, 489, 32, 885, 128, 681, 342, 794, 245, 598, 137, 690, 433, 76, 629,\r\n    380, 832, 194, 747, 498, 41, 894, 142, 695, 346, 799, 250, 603, 108, 661,\r\n    414, 867, 508, 59, 912, 165, 616, 369, 821, 282, 735, 179, 732, 474, 26,\r\n    879, 124, 676, 329, 782, 233, 586, 118, 671, 414, 867, 299, 651, 156, 708,\r\n    453, 100, 553, 304, 757, 901, 145, 697, 448, 96, 549, 300, 753, 896, 149,\r\n    701, 452, 105, 558, 309, 762, 907, 161, 713, 464, 73, 526, 277, 730, 875,\r\n    122, 575, 326, 779, 924, 171, 724, 475, 28, 831, 184, 737, 882, 129, 582,\r\n    333, 786, 930, 176, 729, 480, 35, 838, 191, 744, 889, 136, 589, 340, 793,\r\n    936, 183, 736, 487, 42, 845, 198, 751, 896, 143, 596, 347, 800, 945, 190,\r\n    743, 498, 49, 852, 205, 758, 903, 150, 603, 354, 807, 952, 197, 750, 505,\r\n    56, 859, 212, 765, 910, 157, 610, 361, 814, 959, 204, 757, 512, 63, 866,\r\n    219, 772, 917, 164, 617, 368, 821, 966, 211, 764, 519, 70, 873, 226, 779,\r\n    924, 171, 724, 475, 28, 831, 184, 737, 882, 129, 582, 333, 786, 930, 176,\r\n    729, 480, 35, 838, 191, 744, 889, 136, 589, 340, 793, 936, 183, 736, 487,\r\n    42, 845, 198, 751, 896, 143, 596, 347, 800, 945, 190, 743, 498, 49, 852,\r\n    205, 758, 903, 150, 603, 354, 807, 952, 197, 750, 505, 56, 859, 212, 765,\r\n    910, 157, 610, 361, 814, 959, 204, 757, 512, 63, 866, 219, 772, 917, 164,\r\n    617, 368, 821, 966, 211, 764, 519, 70, 873, 226, 779, 924, 171, 724, 475,\r\n    28, 831, 184, 737, 882, 129, 582, 333, 786, 930, 176, 729, 480, 35, 838,\r\n    191, 744, 889, 136, 589, 340, 793, 936, 183, 736, 487, 42, 845, 198, 751,\r\n    896, 143, 596, 347, 800, 945, 190, 743, 498, 49, 852, 205, 758, 903, 150,\r\n    603, 354, 807, 952, 197, 750, 505, 56, 859, 212, 765, 910, 157, 610, 361,\r\n    814, 959, 204, 757, 512, 63, 866, 219, 772, 917, 164, 617, 368, 821, 966,\r\n    211, 764, 519, 70, 873, 226, 779, 924, 171, 724, 475, 28, 831\r\n  ];\r\n  function getVirtualData(year) {\r\n    const date = +echarts.time.parse(`${year}-01-01`);\r\n    const end = +echarts.time.parse(`${+year + 1}-01-01`);\r\n    const dayTime = 3600 * 24 * 1000;\r\n    const data = [];\r\n    let index = 0;\r\n    for (let time = date; time < end; time += dayTime) {\r\n      data.push([\r\n        echarts.time.format(time, '{yyyy}-{MM}-{dd}', false),\r\n        numbers[index]\r\n      ]);\r\n      index += 1;\r\n    }\r\n    return data;\r\n  }\r\n\r\n  if ($holidaysNextMonthchartEl) {\r\n    const userOptions = getData($holidaysNextMonthchartEl, 'echarts');\r\n    const chart = window.echarts.init($holidaysNextMonthchartEl);\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'item',\r\n        axisPointer: {\r\n          type: 'none'\r\n        },\r\n        padding: [7, 10],\r\n        backgroundColor: getColor('body-highlight-bg'),\r\n        borderColor: getColor('border-color'),\r\n        textStyle: { color: getColor('light-text-emphasis') },\r\n        borderWidth: 1,\r\n        transitionDuration: 0,\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      visualMap: {\r\n        min: 0,\r\n        max: 1000,\r\n        calculable: true,\r\n        show: false,\r\n        color: [\r\n          getColor('warning'),\r\n          // getColor('warning-light'),\r\n          getItemFromStore('phoenixTheme') === 'dark'\r\n            ? rgbaColor(getColor('warning'), 0.5)\r\n            : getColor('warning-light'),\r\n          // getColor('warning-lighter')\r\n          getItemFromStore('phoenixTheme') === 'dark'\r\n            ? rgbaColor(getColor('warning'), 0.75)\r\n            : getColor('warning-light')\r\n        ]\r\n      },\r\n      calendar: {\r\n        orient: 'vertical',\r\n        range: '2017-03',\r\n        width: '99%',\r\n        height: '85.5%',\r\n        left: '2',\r\n        right: 'auto',\r\n        top: 42,\r\n        yearLabel: {\r\n          show: false\r\n        },\r\n        monthLabel: {\r\n          show: false\r\n        },\r\n        dayLabel: {\r\n          firstDay: 0,\r\n          nameMap: ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'],\r\n          margin: 24,\r\n          color: getColor('secondary-text-emphasis'),\r\n          fontFamily: 'Nunito Sans',\r\n          fontWeight: 700\r\n        },\r\n        splitLine: {\r\n          show: false\r\n        },\r\n        itemStyle: {\r\n          color: getColor('dark-text-emphasis'),\r\n          borderColor: getColor('border-color')\r\n        }\r\n      },\r\n      series: {\r\n        type: 'scatter',\r\n        coordinateSystem: 'calendar',\r\n        symbolSize(val) {\r\n          return val[1] / 35;\r\n        },\r\n        data: getVirtualData('2017'),\r\n        itemStyle: {\r\n          color: getColor('warning'),\r\n          opacity: 0.8\r\n        }\r\n      }\r\n    });\r\n\r\n    const responsiveOptions = {\r\n      xl: {\r\n        calendar: {\r\n          height: '83%'\r\n        }\r\n      },\r\n      xxl: {\r\n        calendar: {\r\n          height: '85.5%'\r\n        }\r\n      }\r\n    };\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions, responsiveOptions);\r\n  }\r\n};\r\n\r\nexport default holidaysNextMonthChartInit;\r\n", "import bookingValueChartInit from '../theme/charts/echarts/booking-value-chart';\r\nimport bookingsChartInit from '../theme/charts/echarts/bookings-chart';\r\nimport cancelBookingChartInit from '../theme/charts/echarts/cancel-booking-chart';\r\nimport commissionChartInit from '../theme/charts/echarts/commission-chart';\r\nimport countryWiseVisitorsChartInit from '../theme/charts/echarts/country-wise-visitors-chart';\r\nimport financialActivitiesChartInit from '../theme/charts/echarts/financial-activities-chart';\r\nimport grossProfitInit from '../theme/charts/echarts/gross-profit';\r\nimport holidaysNextMonthChartInit from '../theme/charts/echarts/holidays-next-month';\r\n\r\nconst { docReady } = window.phoenix.utils;\r\n\r\ndocReady(bookingValueChartInit);\r\ndocReady(commissionChartInit);\r\ndocReady(cancelBookingChartInit);\r\ndocReady(countryWiseVisitorsChartInit);\r\ndocReady(financialActivitiesChartInit);\r\ndocReady(holidaysNextMonthChartInit);\r\ndocReady(bookingsChartInit);\r\ndocReady(grossProfitInit);\r\n"], "names": ["echarts"], "mappings": ";;;;;EAAA;EACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;AAC3B;EACA;EACO,MAAM,eAAe,GAAG;EAC/B,EAAE,KAAK;EACP,EAAE,WAAW;EACb,EAAE,iBAAiB;EACnB,EAAE,iBAAiB;EACnB,KAAK;EACL,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACvD,EAAE,MAAM,YAAY,GAAG,OAAO,IAAI;EAClC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;EACzC,MAAM,IAAI,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE;EACjD,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;AAC3D;EACA,EAAE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,aAAa;EACrD,IAAI,yBAAyB;EAC7B,GAAG,CAAC;EACJ,EAAE,IAAI,oBAAoB,EAAE;EAC5B,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,MAAM;EAC1E,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;EACrB,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,MAAM,CAAC,MAAM;EACf,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;EACnB,IAAI,IAAI,iBAAiB,EAAE;EAC3B,MAAM,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACtC,KAAK;EACL,GAAG,CAAC,CAAC;EACL,EAAE,IAAI,iBAAiB,EAAE;EACzB,IAAI,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACpC,GAAG;AACH;EACA,EAAE,eAAe,CAAC,gBAAgB;EAClC,IAAI,cAAc;EAClB,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK;EACjC,MAAM,IAAI,OAAO,KAAK,cAAc,EAAE;EACtC,QAAQ,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;EAC1E,OAAO;EACP,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK;EACL,GAAG,CAAC;EACJ,CAAC,CAAC;EACF;AACA;EACA,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;EACvE,IAAI,UAAU,EAAE;EAChB,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,IAAI;EAC5B,IAAI,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI;EAC9C,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;EAC1B,MAAM,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;EAC1B,MAAM,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE,aAAa,CAAC,mBAAmB,CAAC,CAAC;EAChE,MAAM,IAAI,KAAK,EAAE;EACjB,QAAQ,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;EAC5C,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACO,MAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,aAAa,GAAG,QAAQ,KAAK;EACtE,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;EACvB,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI;EACvB,IAAI,WAAW,IAAI,CAAC;AACpB,2FAA2F;AAC3F,UAAU,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,KAAK;AACpD,SAAS;AACT,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG;AAC7B,MAAM,OAAO,EAAE,CAAC,KAAK,KAAK,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK;AAC3D,KAAK;AACL;AACA,YAAY,CAAC,CAAC;EACd,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,CAAC;AACV;AACA,cAAc;AACd,gBAAgB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;AAC3D,oBAAoB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;AAC3E,oBAAoB,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;AACvC,eAAe;AACf;AACA,YAAY,EAAE,WAAW,CAAC;AAC1B,gBAAgB,CAAC,CAAC;EAClB,CAAC,CAAC;AACF;EACO,MAAM,qBAAqB,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK;EAC/D;EACA,EAAE,IAAI,MAAM,CAAC,UAAU,IAAI,GAAG,EAAE;EAChC,IAAI,MAAM,aAAa,GAAG,GAAG,CAAC,YAAY,CAAC;EAC3C,IAAI,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;EACrD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EAC9D,IAAI,OAAO,GAAG,CAAC;EACf,GAAG;EACH,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;;EC5GD;EACA;EACA;AACA;EACA,MAAM,qBAAqB,GAAG,MAAM;EACpC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAC/D,EAAE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;EAC9E,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI;EACrC,IAAI,MAAM,WAAW,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;EAC1D,IAAI,MAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAC5E;EACA,IAAI,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,MAAM;EACjD,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;EACxB,MAAM,IAAI,EAAE,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,WAAW;EAC9C,MAAM,KAAK,EAAE,KAAK,CAAC,KAAK;EACxB,KAAK,CAAC,CAAC,CAAC;AACR;EACA,IAAI,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;EACzB,IAAI,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,KAAK;EAClC,MAAM,WAAW,IAAI,CAAC,gBAAgB;AACtC,QAAQ,KAAK,GAAG,CAAC,IAAI,MAAM;AAC3B,OAAO,gDAAgD,EAAE,EAAE,CAAC,KAAK,CAAC;AAClE,MAAM,EAAE,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,2BAA2B,EAAE,EAAE,CAAC,KAAK,CAAC;AACvE,SAAS,CAAC,CAAC;EACX,KAAK,CAAC,CAAC;EACP,IAAI,OAAO,CAAC;AACZ,cAAc,EAAE,WAAW,CAAC;AAC5B,kBAAkB,CAAC,CAAC;EACpB,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,mBAAmB,EAAE;EAC3B,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;EAChE,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;EAC3D,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,EAAE;EACnB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,QAAQ;EACxB,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC;EACjC,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC;EACjC,YAAY,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC/B,WAAW;EACX,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC,EAAE;EAC1D,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EACpE,YAAY,YAAY,EAAE,IAAI;EAC9B,YAAY,YAAY,EAAE,KAAK;EAC/B,YAAY,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC9C,YAAY,KAAK,EAAE,MAAM;EACzB,YAAY,QAAQ,EAAE,CAAC;EACvB,YAAY,UAAU,EAAE,aAAa;EACrC,YAAY,UAAU,EAAE,GAAG;EAC3B,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,WAAW,EAAE,KAAK;EAC1B,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EACnD,UAAU,UAAU,EAAE,KAAK;EAC3B,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,CAAC;EACpB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,CAAC;EACnB,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE;EACpD,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECvGD;EACA;EACA;EACA,MAAM,WAAEA,SAAO,EAAE,GAAG,MAAM,CAAC;AAC3B;EACA,MAAM,iBAAiB,GAAG,MAAM;EAChC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE;EACxE,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACzB,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAC;AACpE;EACA,EAAE,MAAM,cAAc,GAAG;EACzB,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACpD,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACpD,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACpD,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,aAAa,GAAG;EACxB,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;EAC5D,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;EAC5D,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC;EAC5D,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,cAAc,EAAE;EACtB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;EAC3D,IAAI,MAAM,KAAK,GAAGA,SAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;EAC/C,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAC1C,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;EACxC,QAAQ,SAAS,EAAE,EAAE;EACrB,QAAQ,UAAU,EAAE,EAAE;EACtB,QAAQ,IAAI,EAAE,QAAQ;EACtB,QAAQ,OAAO,EAAE,EAAE;EACnB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,aAAa,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACnD,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,iBAAiB,CAAC;EAC5C,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,UAAU,EAAE,aAAa;EACnC,SAAS;EACT,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,yBAAyB,CAAC;EACpD,UAAU,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;EAClE,UAAU,UAAU,EAAE,aAAa;EACnC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,QAAQ,EAAE,IAAI;EACxB,SAAS;EACT,QAAQ,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;EAC7B,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,0BAA0B,CAAC;EACvD,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE,KAAK;EACvB,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC;EACvC,UAAU,SAAS,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EACtE,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,UAAU,EAAE,aAAa;EACnC,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,QAAQ,EAAE,EAAE;EACtB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,0BAA0B,CAAC;EACvD,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,WAAW;EAC3B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,KAAK;EACtB,UAAU,IAAI,EAAE,cAAc,CAAC,CAAC,CAAC;EACjC,UAAU,QAAQ,EAAE,KAAK;EACzB,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,QAAQ,CAAC,MAAM,CAAC;EAClC,kBAAkB,QAAQ,CAAC,YAAY,CAAC;EACxC,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,WAAW;EAC3B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,KAAK,EAAE,KAAK;EACtB,UAAU,QAAQ,EAAE,KAAK;EACzB,UAAU,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC;EAChC,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;EAClD,kBAAkB,QAAQ,CAAC,cAAc,CAAC;EAC1C,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE;EACzE,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3D;EACA,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,wBAAwB,CAAC,CAAC;EAC3E,IAAI,IAAI,aAAa,EAAE;EACvB,MAAM,aAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,IAAI;EACpD,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,aAAa,CAAC;EAC1C,QAAQ,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;EAC5C,QAAQ,MAAM,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;EAC3C,QAAQ,KAAK,CAAC,SAAS,CAAC;EACxB,UAAU,MAAM,EAAE;EAClB,YAAY;EACZ,cAAc,IAAI,EAAE,KAAK;EACzB,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,KAAK;EACzB,aAAa;EACb,WAAW;EACX,SAAS,CAAC,CAAC;EACX,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;EACH,CAAC;;EChJD;AACA;EACA;EACA;EACA;AACA;EACA,MAAM,sBAAsB,GAAG,MAAM;EACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,gBAAgB,EAAE;EACzD,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACzB,EAAE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;AAC/E;EACA,EAAE,IAAI,oBAAoB,EAAE;EAC5B,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;EACjE,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;AAC5D;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EAChC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,MAAM,IAAI;EAC7B,UAAU,OAAO,CAAC,QAAQ,EAAE,MAAM;AAClC,aAAa,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;AAC/B,aAAa,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EAC1D,SAAS;EACT,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;EACxB,QAAQ,IAAI,EAAE,QAAQ;EACtB,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC;EAC/B,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC;EAC/B,UAAU,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;EAC7B,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,KAAK;EACnB,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,IAAI,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAC9C,UAAU,MAAM,EAAE,MAAM;EACxB,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1C,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,QAAQ,CAAC,MAAM,CAAC;EAClC,kBAAkB,QAAQ,CAAC,YAAY,CAAC;EACxC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;EACpD,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EClED;AAEA;EACA;EACA;EACA;AACA;EACA,MAAM,WAAEA,SAAO,EAAE,GAAG,MAAM,CAAC;AAC3B;EACA,MAAM,mBAAmB,GAAG,MAAM;EAClC,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACrD,EAAE,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;AACzE;EACA,EAAE,IAAI,iBAAiB,EAAE;EACzB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;EAC9D,IAAI,MAAM,KAAK,GAAGA,SAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;AAClD;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,SAAS,EAAE,MAAM,IAAI;EAC7B,UAAU,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,UAAU,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC3E,SAAS;EACT,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,IAAI,EAAE,YAAY;EAC5B,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,QAAQ,EAAE,CAAC,GAAG;EACxB,UAAU,MAAM,EAAE,KAAK;EACvB,UAAU,OAAO,EAAE;EACnB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,OAAO,EAAE,KAAK;EAC1B,YAAY,QAAQ,EAAE,IAAI;EAC1B,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACxC,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC;EACpD,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY;EACZ,cAAc,KAAK,EAAE,EAAE;EACvB,aAAa;EACb,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;EC9ED;AACA;EACA;EACA;EACA;AACA;EACA,MAAM,4BAA4B,GAAG,MAAM;EAC3C,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,gBAAgB,EAAE;EAChE,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACzB,EAAE,MAAM,0BAA0B,GAAG,QAAQ,CAAC,aAAa;EAC3D,IAAI,+BAA+B;EACnC,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC7E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC7E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC1C,GAAG,CAAC;EACJ,EAAE,MAAM,QAAQ,GAAG;EACnB,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EAC7E,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;EACtE,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC;AACtC;AACA;AACA,0CAA0C,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC5D;AACA;AACA,IAAI,CAAC,CAAC;AACN;EACA,EAAE,IAAI,0BAA0B,EAAE;EAClC,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,0BAA0B,EAAE,SAAS,CAAC,CAAC;EACvE,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;AAClE;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE;EAC/C,UAAU,OAAO,qBAAqB,CAAC,GAA4B,CAAC,CAAC;EACrE,SAAS;EACT,QAAQ,SAAS,EAAE,gBAAgB;EACnC,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,UAAU;AACxB;EACA,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,WAAW,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC;EAC/B,QAAQ,IAAI,EAAE,QAAQ;EACtB,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,KAAK,EAAE,IAAI;EACnB,QAAQ,WAAW,EAAE,KAAK;EAC1B,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,GAAG,EAAE,GAAG;EAChB,QAAQ,GAAG,EAAE,GAAG;EAChB,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,WAAW,EAAE,CAAC;EACxB,UAAU,MAAM,EAAE,CAAC;EACnB,UAAU,IAAI;EACd,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,QAAQ,CAAC,SAAS,CAAC;EACrC,kBAAkB,QAAQ,CAAC,eAAe,CAAC;EAC3C,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,CAAC;EACd,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;AAC3D;EACA,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;AACrE;EACA,IAAI,WAAW,CAAC,MAAM;EACtB,MAAM,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;EAChD,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;EACnB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACzB,MAAM,QAAQ,CAAC,KAAK,EAAE,CAAC;EACvB,MAAM,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC;EAC9C,MAAM,cAAc,CAAC,SAAS,GAAG,OAAO,CAAC;AACzC;EACA,MAAM,KAAK,CAAC,SAAS,CAAC;EACtB,QAAQ,KAAK,EAAE;EACf,UAAU,IAAI,EAAE,QAAQ;EACxB,SAAS;EACT,QAAQ,MAAM,EAAE;EAChB,UAAU;EACV,YAAY,IAAI;EAChB,WAAW;EACX,SAAS;EACT,OAAO,CAAC,CAAC;EACT,KAAK,EAAE,IAAI,CAAC,CAAC;EACb,GAAG;EACH,CAAC;;EC5HD;AACA;EACA;EACA;EACA;AACA;EACA,MAAM,4BAA4B,GAAG,MAAM;EAC3C,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACvE,EAAE,MAAM,2BAA2B,GAAG,QAAQ,CAAC,aAAa;EAC5D,IAAI,8BAA8B;EAClC,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,UAAU,GAAG;EACrB,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACpD,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACpD,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC;EACpD,GAAG,CAAC;EACJ,EAAE,MAAM,WAAW,GAAG;EACtB,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC;EAC1D,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC;EAC1D,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC;EAC1D,GAAG,CAAC;EACJ,EAAE,MAAM,YAAY,GAAG;EACvB,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC;EAC1D,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC;EAC1D,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,EAAE,CAAC,MAAM,CAAC;EAC1D,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,2BAA2B,EAAE;EACnC,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,2BAA2B,EAAE,SAAS,CAAC,CAAC;EACxE,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;EACnE,IAAI,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EAC7E,IAAI,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EAC9E,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa;EACjD,MAAM,CAAC,CAAC,EAAE,WAAW,CAAC,WAAW,CAAC,CAAC;EACnC,KAAK,CAAC;AACN;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;EAC3D,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,CAAC;EACrD,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;EAC/C,QAAQ,IAAI,EAAE,KAAK;EACnB,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,yBAAyB,CAAC;EACpD,UAAU,SAAS,EAAE,KAAK;EAC1B,YAAY,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;EAChE,UAAU,UAAU,EAAE,aAAa;EACnC,UAAU,UAAU,EAAE,GAAG;EACzB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,0BAA0B,CAAC;EACvD,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,IAAI,EAAE;EACd,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,UAAU,SAAS;EACnB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,yBAAyB,CAAC;EACpD,UAAU,MAAM,EAAE,CAAC;EACnB,UAAU,UAAU,EAAE,aAAa;EACnC,UAAU,UAAU,EAAE,GAAG;EACzB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,0BAA0B,CAAC;EACvD,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,QAAQ,EAAE,IAAI;EACxB,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,QAAQ,CAAC,SAAS,CAAC;EACrC,kBAAkB,QAAQ,CAAC,eAAe,CAAC;EAC3C,WAAW;EACX,UAAU,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC;EAC7B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,MAAM,EAAE,MAAM;EACxB,UAAU,KAAK,EAAE,OAAO;EACxB,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,QAAQ,CAAC,SAAS,CAAC;EACrC,kBAAkB,QAAQ,CAAC,eAAe,CAAC;EAC3C,WAAW;EACX,UAAU,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;EAC9B,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE,QAAQ;EAC3B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACtC,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,QAAQ,CAAC,MAAM,CAAC;EAClC,kBAAkB,QAAQ,CAAC,YAAY,CAAC;EACxC,WAAW;EACX,UAAU,IAAI,EAAE,YAAY,CAAC,CAAC,CAAC;EAC/B,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,EAAE;EACjB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,EAAE;EACf,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,MAAM,SAAS,EAAE,KAAK;EACtB,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,iBAAiB,GAAG;EAC9B,MAAM,EAAE,EAAE;EACV,QAAQ,KAAK,EAAE;EACf,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,SAAS;EACT,QAAQ,IAAI,EAAE;EACd,UAAU,IAAI,EAAE,EAAE;EAClB,SAAS;EACT,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,KAAK,EAAE;EACf,UAAU,SAAS,EAAE;EACrB,YAAY,MAAM,EAAE,EAAE;EACtB,YAAY,IAAI,EAAE,IAAI;EACtB,WAAW;EACX,SAAS;EACT,QAAQ,IAAI,EAAE;EACd,UAAU,IAAI,EAAE,CAAC;EACjB,SAAS;EACT,OAAO;EACP,MAAM,EAAE,EAAE;EACV,QAAQ,KAAK,EAAE;EACf,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,SAAS;EACT,QAAQ,IAAI,EAAE;EACd,UAAU,IAAI,EAAE,EAAE;EAClB,SAAS;EACT,OAAO;EACP,MAAM,GAAG,EAAE;EACX,QAAQ,KAAK,EAAE;EACf,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,IAAI;EACtB,WAAW;EACX,SAAS;EACT,QAAQ,IAAI,EAAE;EACd,UAAU,IAAI,EAAE,CAAC;EACjB,SAAS;EACT,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;AAC9E;EACA,IAAI,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;EACjD,MAAM,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;EAClD,MAAM,KAAK,CAAC,cAAc,CAAC;EAC3B,QAAQ,IAAI,EAAE,oBAAoB;EAClC,QAAQ,IAAI,EAAE,QAAQ;EACtB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;AACP;EACA,IAAI,aAAa,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;EAClD,MAAM,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;EACnD,MAAM,KAAK,CAAC,cAAc,CAAC;EAC3B,QAAQ,IAAI,EAAE,oBAAoB;EAClC,QAAQ,IAAI,EAAE,SAAS;EACvB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;AACP;EACA,IAAI,cAAc,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;EACnD,MAAM,cAAc,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;EACpD,MAAM,KAAK,CAAC,cAAc,CAAC;EAC3B,QAAQ,IAAI,EAAE,oBAAoB;EAClC,QAAQ,IAAI,EAAE,UAAU;EACxB,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;EAC/E,IAAI,IAAI,cAAc,EAAE;EACxB,MAAM,cAAc,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,IAAI;EACrD,QAAQ,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC,aAAa,CAAC;EAC1C,QAAQ,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;EACxC,QAAQ,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;EACzC,QAAQ,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;EAC1C,QAAQ,KAAK,CAAC,SAAS,CAAC;EACxB,UAAU,MAAM,EAAE;EAClB,YAAY;EACZ,cAAc,IAAI,EAAE,KAAK;EACzB,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,KAAK;EACzB,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,KAAK;EACzB,aAAa;EACb,WAAW;EACX,SAAS,CAAC,CAAC;EACX,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;EACH,CAAC;;ECnQD;EACA;EACA;EACA,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;AAC3B;EACA,MAAM,eAAe,GAAG,MAAM;EAC9B,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE;EAC1D,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACzB,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,aAAa,CAAC,sBAAsB,CAAC,CAAC;AACtE;EACA,EAAE,MAAM,IAAI,GAAG;EACf,IAAI;EACJ,MAAM,IAAI,EAAE,QAAQ;EACpB,MAAM,KAAK,EAAE,EAAE;EACf,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK;EACb,UAAU,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACrD,cAAc,QAAQ,CAAC,SAAS,CAAC;EACjC,cAAc,QAAQ,CAAC,eAAe,CAAC;EACvC,OAAO;EACP,MAAM,QAAQ,EAAE;EAChB,QAAQ;EACR,UAAU,IAAI,EAAE,WAAW;EAC3B,UAAU,KAAK,EAAE,CAAC;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACrD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC;EAC3D,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC/C,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACrD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC;EAC3D,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC;EAC/D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC;EAC/D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC;EAC/D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACrD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC;EAC3D,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC;EAC/D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC;EAC/D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,IAAI,EAAE,SAAS;EACrB,MAAM,KAAK,EAAE,EAAE;EACf,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK;EACb,UAAU,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACrD,cAAc,QAAQ,CAAC,MAAM,CAAC;EAC9B,cAAc,QAAQ,CAAC,YAAY,CAAC;EACpC,OAAO;EACP,MAAM,QAAQ,EAAE;EAChB,QAAQ;EACR,UAAU,IAAI,EAAE,gBAAgB;EAChC,UAAU,KAAK,EAAE,CAAC;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;EAClD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC;EACxD,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;EAC5D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,gBAAgB;EAChC,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;EAClD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC;EACxD,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;EAC5D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;EAC5D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;EAC5D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;EAC5D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,wBAAwB;EACxC,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;EAClD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC;EACzD,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC;EAC7D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;EAC5D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,eAAe;EAC/B,UAAU,KAAK,EAAE,CAAC;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;EAClD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC;EACzD,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;EAC5D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC;EAClD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,GAAG,CAAC;EACxD,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,GAAG,CAAC;EAC5D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC;EAC5C,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,IAAI,EAAE,OAAO;EACnB,MAAM,KAAK,EAAE,EAAE;EACf,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK;EACb,UAAU,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACrD,cAAc,QAAQ,CAAC,SAAS,CAAC;EACjC,cAAc,QAAQ,CAAC,eAAe,CAAC;EACvC,OAAO;EACP,MAAM,QAAQ,EAAE;EAChB,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACrD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC;EAC3D,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC/C,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC;EAChE,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACrD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC;EAC3D,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC;EAChE,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC;EAChE,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,IAAI,CAAC;EAChE,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK;EACL,IAAI;EACJ,MAAM,IAAI,EAAE,MAAM;EAClB,MAAM,KAAK,EAAE,EAAE;EACf,MAAM,SAAS,EAAE;EACjB,QAAQ,KAAK;EACb,UAAU,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACrD,cAAc,QAAQ,CAAC,SAAS,CAAC;EACjC,cAAc,QAAQ,CAAC,eAAe,CAAC;EACvC,OAAO;EACP,MAAM,QAAQ,EAAE;EAChB,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,KAAK,EAAE,CAAC;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACrD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC;EAC3D,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC/C,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,KAAK,EAAE,EAAE;EACnB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK;EACjB,cAAc,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACzD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACrD,kBAAkB,SAAS,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,GAAG,CAAC;EAC3D,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC;EAC/D,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,IAAI,EAAE,SAAS;EAC7B,cAAc,KAAK,EAAE,CAAC;EACtB,cAAc,SAAS,EAAE;EACzB,gBAAgB,KAAK,EAAE,SAAS,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,GAAG,CAAC;EAC/D,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK;EACL,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,MAAM,GAAG;EACjB,IAAI,QAAQ,CAAC,eAAe,CAAC;EAC7B,IAAI,QAAQ,CAAC,YAAY,CAAC;EAC1B,IAAI,QAAQ,CAAC,eAAe,CAAC;EAC7B,IAAI,QAAQ,CAAC,eAAe,CAAC;EAC7B,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,YAAY,EAAE;EACpB,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;EACzD,IAAI,MAAM,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;EAC7C,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE,MAAM;EACnB,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,IAAI;EACd,UAAU,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE;EACrB,YAAY,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,EAAE;EAC/B,cAAc,OAAO,CAAC,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;EACjD,aAAa;EACb,YAAY,OAAO,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,CAAC;EAC7C,WAAW;EACX,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,MAAM,EAAE;EAClB,YAAY,EAAE;EACd,YAAY;EACZ,cAAc,EAAE,EAAE,CAAC;EACnB,cAAc,CAAC,EAAE,EAAE;EACnB,cAAc,SAAS,EAAE;EACzB,gBAAgB,WAAW,EAAE,CAAC;EAC9B,gBAAgB,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAChD,eAAe;EACf,cAAc,KAAK,EAAE;EACrB,gBAAgB,IAAI,EAAE,KAAK;EAC3B,eAAe;EACf,cAAc,IAAI,EAAE;EACpB,gBAAgB,SAAS,EAAE;EAC3B,kBAAkB,WAAW,EAAE,GAAG;EAClC,iBAAiB;EACjB,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,EAAE,EAAE,EAAE;EACpB,cAAc,CAAC,EAAE,GAAG;EACpB,cAAc,SAAS,EAAE;EACzB,gBAAgB,WAAW,EAAE,CAAC;EAC9B,gBAAgB,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAChD,eAAe;EACf,cAAc,KAAK,EAAE;EACrB,gBAAgB,IAAI,EAAE,KAAK;EAC3B,eAAe;EACf,aAAa;EACb,YAAY;EACZ,cAAc,EAAE,EAAE,GAAG;EACrB,cAAc,CAAC,EAAE,GAAG;EACpB,cAAc,SAAS,EAAE;EACzB,gBAAgB,WAAW,EAAE,CAAC;EAC9B,gBAAgB,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAChD,eAAe;EACf,cAAc,KAAK,EAAE;EACrB,gBAAgB,IAAI,EAAE,KAAK;EAC3B,eAAe;EACf,aAAa;EACb,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;EACP,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECvcD,MAAM,0BAA0B,GAAG,MAAM;EACzC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE,SAAS,EAAE;EAC1D,IAAI,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACzB,EAAE,MAAM,yBAAyB,GAAG,QAAQ,CAAC,aAAa;EAC1D,IAAI,6BAA6B;EACjC,GAAG,CAAC;EACJ,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,CAAC;EAC7B,EAAE,MAAM,OAAO,GAAG;EAClB,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC7E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;EAC3E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;EAC5E,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG;EACjE,GAAG,CAAC;EACJ,EAAE,SAAS,cAAc,CAAC,IAAI,EAAE;EAChC,IAAI,MAAM,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;EACtD,IAAI,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC;EAC1D,IAAI,MAAM,OAAO,GAAG,IAAI,GAAG,EAAE,GAAG,IAAI,CAAC;EACrC,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC;EACpB,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;EAClB,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG,EAAE,IAAI,IAAI,OAAO,EAAE;EACvD,MAAM,IAAI,CAAC,IAAI,CAAC;EAChB,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,kBAAkB,EAAE,KAAK,CAAC;EAC5D,QAAQ,OAAO,CAAC,KAAK,CAAC;EACtB,OAAO,CAAC,CAAC;EACT,MAAM,KAAK,IAAI,CAAC,CAAC;EACjB,KAAK;EACL,IAAI,OAAO,IAAI,CAAC;EAChB,GAAG;AACH;EACA,EAAE,IAAI,yBAAyB,EAAE;EACjC,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,yBAAyB,EAAE,SAAS,CAAC,CAAC;EACtE,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,yBAAyB,CAAC,CAAC;EACjE,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,WAAW,EAAE;EACrB,UAAU,IAAI,EAAE,MAAM;EACtB,SAAS;EACT,QAAQ,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;EACxB,QAAQ,eAAe,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EACtD,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,qBAAqB,CAAC,EAAE;EAC7D,QAAQ,WAAW,EAAE,CAAC;EACtB,QAAQ,kBAAkB,EAAE,CAAC;EAC7B,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,SAAS,EAAE;EACjB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,GAAG,EAAE,IAAI;EACjB,QAAQ,UAAU,EAAE,IAAI;EACxB,QAAQ,IAAI,EAAE,KAAK;EACnB,QAAQ,KAAK,EAAE;EACf,UAAU,QAAQ,CAAC,SAAS,CAAC;EAC7B;EACA,UAAU,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACrD,cAAc,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,GAAG,CAAC;EACjD,cAAc,QAAQ,CAAC,eAAe,CAAC;EACvC;EACA,UAAU,gBAAgB,CAAC,cAAc,CAAC,KAAK,MAAM;EACrD,cAAc,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC;EAClD,cAAc,QAAQ,CAAC,eAAe,CAAC;EACvC,SAAS;EACT,OAAO;EACP,MAAM,QAAQ,EAAE;EAChB,QAAQ,MAAM,EAAE,UAAU;EAC1B,QAAQ,KAAK,EAAE,SAAS;EACxB,QAAQ,KAAK,EAAE,KAAK;EACpB,QAAQ,MAAM,EAAE,OAAO;EACvB,QAAQ,IAAI,EAAE,GAAG;EACjB,QAAQ,KAAK,EAAE,MAAM;EACrB,QAAQ,GAAG,EAAE,EAAE;EACf,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,UAAU,EAAE;EACpB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;EACpE,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,KAAK,EAAE,QAAQ,CAAC,yBAAyB,CAAC;EACpD,UAAU,UAAU,EAAE,aAAa;EACnC,UAAU,UAAU,EAAE,GAAG;EACzB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,KAAK;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,oBAAoB,CAAC;EAC/C,UAAU,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC/C,SAAS;EACT,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,IAAI,EAAE,SAAS;EACvB,QAAQ,gBAAgB,EAAE,UAAU;EACpC,QAAQ,UAAU,CAAC,GAAG,EAAE;EACxB,UAAU,OAAO,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;EAC7B,SAAS;EACT,QAAQ,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;EACpC,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACpC,UAAU,OAAO,EAAE,GAAG;EACtB,SAAS;EACT,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,iBAAiB,GAAG;EAC9B,MAAM,EAAE,EAAE;EACV,QAAQ,QAAQ,EAAE;EAClB,UAAU,MAAM,EAAE,KAAK;EACvB,SAAS;EACT,OAAO;EACP,MAAM,GAAG,EAAE;EACX,QAAQ,QAAQ,EAAE;EAClB,UAAU,MAAM,EAAE,OAAO;EACzB,SAAS;EACT,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,EAAE,iBAAiB,CAAC,CAAC;EAC9E,GAAG;EACH,CAAC;;ECjJD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1C;EACA,QAAQ,CAAC,qBAAqB,CAAC,CAAC;EAChC,QAAQ,CAAC,mBAAmB,CAAC,CAAC;EAC9B,QAAQ,CAAC,sBAAsB,CAAC,CAAC;EACjC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;EACvC,QAAQ,CAAC,4BAA4B,CAAC,CAAC;EACvC,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACrC,QAAQ,CAAC,iBAAiB,CAAC,CAAC;EAC5B,QAAQ,CAAC,eAAe,CAAC;;;;;;"}