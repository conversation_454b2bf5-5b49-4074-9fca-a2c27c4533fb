{"version": 3, "file": "config.js", "sources": ["../../../src/js/config.js"], "sourcesContent": ["/* eslint-disable no-var */\r\n/* eslint-disable no-unused-vars */\r\n/* eslint-disable no-extra-boolean-cast */\r\n/* -------------------------------------------------------------------------- */\r\n/*                              Config                                        */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst configQueryMap = {\r\n  'navbar-vertical-collapsed': 'phoenixIsNavbarVerticalCollapsed',\r\n  'color-scheme': 'phoenixTheme',\r\n  'navigation-type': 'phoenixNavbarPosition',\r\n  'vertical-navbar-appearance': 'phoenixNavbarVerticalStyle',\r\n  'horizontal-navbar-shape': 'phoenixNavbarTopShape',\r\n  'horizontal-navbar-appearance': 'phoenixNavbarTopStyle'\r\n};\r\n\r\nconst initialConfig = {\r\n  phoenixIsNavbarVerticalCollapsed: false,\r\n  phoenixTheme: 'light',\r\n  phoenixNavbarTopStyle: 'default',\r\n  phoenixNavbarVerticalStyle: 'default',\r\n  phoenixNavbarPosition: 'vertical',\r\n  phoenixNavbarTopShape: 'default',\r\n  phoenixIsRTL: false,\r\n  phoenixSupportChat: true\r\n};\r\n\r\nconst CONFIG = { ...initialConfig };\r\n\r\nconst setConfig = (payload, persist = true) => {\r\n  Object.keys(payload).forEach(key => {\r\n    CONFIG[key] = payload[key];\r\n    if (persist) {\r\n      localStorage.setItem(key, payload[key]);\r\n    }\r\n  });\r\n};\r\n\r\nconst resetConfig = () => {\r\n  Object.keys(initialConfig).forEach(key => {\r\n    CONFIG[key] = initialConfig[key];\r\n    localStorage.setItem(key, initialConfig[key]);\r\n  });\r\n};\r\n\r\nconst urlSearchParams = new URLSearchParams(window.location.search);\r\nconst params = Object.fromEntries(urlSearchParams.entries());\r\n\r\nif (\r\n  Object.keys(params).length > 0 &&\r\n  Object.keys(params).includes('theme-control')\r\n) {\r\n  resetConfig();\r\n\r\n  Object.keys(params).forEach(param => {\r\n    if (configQueryMap[param]) {\r\n      localStorage.setItem(configQueryMap[param], params[param]);\r\n    }\r\n  });\r\n}\r\n\r\nObject.keys(CONFIG).forEach(key => {\r\n  if (localStorage.getItem(key) === null) {\r\n    localStorage.setItem(key, CONFIG[key]);\r\n  } else {\r\n    try {\r\n      setConfig({\r\n        [key]: JSON.parse(localStorage.getItem(key))\r\n      });\r\n    } catch {\r\n      setConfig({\r\n        [key]: localStorage.getItem(key)\r\n      });\r\n    }\r\n  }\r\n});\r\n\r\nif (!!JSON.parse(localStorage.getItem('phoenixIsNavbarVerticalCollapsed'))) {\r\n  document.documentElement.classList.add('navbar-vertical-collapsed');\r\n}\r\n\r\nif (localStorage.getItem('phoenixTheme') === 'dark') {\r\n  document.documentElement.setAttribute('data-bs-theme', 'dark');\r\n} else if (localStorage.getItem('phoenixTheme') === 'auto') {\r\n  document.documentElement.setAttribute(\r\n    'data-bs-theme',\r\n    window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'\r\n  );\r\n}\r\n\r\nif (localStorage.getItem('phoenixNavbarPosition') === 'horizontal') {\r\n  document.documentElement.setAttribute('data-navigation-type', 'horizontal');\r\n}\r\n\r\nif (localStorage.getItem('phoenixNavbarPosition') === 'combo') {\r\n  document.documentElement.setAttribute('data-navigation-type', 'combo');\r\n}\r\n\r\nexport default {\r\n  config: CONFIG,\r\n  reset: resetConfig,\r\n  set: setConfig\r\n};\r\n"], "names": [], "mappings": ";;;;;;EAAA;EACA;EACA;EACA;EACA;EACA;AACA;EACA,MAAM,cAAc,GAAG;EACvB,EAAE,2BAA2B,EAAE,kCAAkC;EACjE,EAAE,cAAc,EAAE,cAAc;EAChC,EAAE,iBAAiB,EAAE,uBAAuB;EAC5C,EAAE,4BAA4B,EAAE,4BAA4B;EAC5D,EAAE,yBAAyB,EAAE,uBAAuB;EACpD,EAAE,8BAA8B,EAAE,uBAAuB;EACzD,CAAC,CAAC;AACF;EACA,MAAM,aAAa,GAAG;EACtB,EAAE,gCAAgC,EAAE,KAAK;EACzC,EAAE,YAAY,EAAE,OAAO;EACvB,EAAE,qBAAqB,EAAE,SAAS;EAClC,EAAE,0BAA0B,EAAE,SAAS;EACvC,EAAE,qBAAqB,EAAE,UAAU;EACnC,EAAE,qBAAqB,EAAE,SAAS;EAClC,EAAE,YAAY,EAAE,KAAK;EACrB,EAAE,kBAAkB,EAAE,IAAI;EAC1B,CAAC,CAAC;AACF;EACA,MAAM,MAAM,GAAG,EAAE,GAAG,aAAa,EAAE,CAAC;AACpC;EACA,MAAM,SAAS,GAAG,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,KAAK;EAC/C,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI;EACtC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EAC/B,IAAI,IAAI,OAAO,EAAE;EACjB,MAAM,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;EAC9C,KAAK;EACL,GAAG,CAAC,CAAC;EACL,CAAC,CAAC;AACF;EACA,MAAM,WAAW,GAAG,MAAM;EAC1B,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI;EAC5C,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC;EACrC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,CAAC;EAClD,GAAG,CAAC,CAAC;EACL,CAAC,CAAC;AACF;EACA,MAAM,eAAe,GAAG,IAAI,eAAe,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;EACpE,MAAM,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC,CAAC;AAC7D;EACA;EACA,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;EAChC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC;EAC/C,EAAE;EACF,EAAE,WAAW,EAAE,CAAC;AAChB;EACA,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI;EACvC,IAAI,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;EAC/B,MAAM,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;EACjE,KAAK;EACL,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACA,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI;EACnC,EAAE,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;EAC1C,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;EAC3C,GAAG,MAAM;EACT,IAAI,IAAI;EACR,MAAM,SAAS,CAAC;EAChB,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;EACpD,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,MAAM;EACZ,MAAM,SAAS,CAAC;EAChB,QAAQ,CAAC,GAAG,GAAG,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC;EACxC,OAAO,CAAC,CAAC;EACT,KAAK;EACL,GAAG;EACH,CAAC,CAAC,CAAC;AACH;EACA,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAC,EAAE;EAC5E,EAAE,QAAQ,CAAC,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;EACtE,CAAC;AACD;EACA,IAAI,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,MAAM,EAAE;EACrD,EAAE,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;EACjE,CAAC,MAAM,IAAI,YAAY,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,MAAM,EAAE;EAC5D,EAAE,QAAQ,CAAC,eAAe,CAAC,YAAY;EACvC,IAAI,eAAe;EACnB,IAAI,MAAM,CAAC,UAAU,CAAC,8BAA8B,CAAC,CAAC,OAAO,GAAG,MAAM,GAAG,OAAO;EAChF,GAAG,CAAC;EACJ,CAAC;AACD;EACA,IAAI,YAAY,CAAC,OAAO,CAAC,uBAAuB,CAAC,KAAK,YAAY,EAAE;EACpE,EAAE,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,sBAAsB,EAAE,YAAY,CAAC,CAAC;EAC9E,CAAC;AACD;EACA,IAAI,YAAY,CAAC,OAAO,CAAC,uBAAuB,CAAC,KAAK,OAAO,EAAE;EAC/D,EAAE,QAAQ,CAAC,eAAe,CAAC,YAAY,CAAC,sBAAsB,EAAE,OAAO,CAAC,CAAC;EACzE,CAAC;AACD;AACA,eAAe;EACf,EAAE,MAAM,EAAE,MAAM;EAChB,EAAE,KAAK,EAAE,WAAW;EACpB,EAAE,GAAG,EAAE,SAAS;EAChB,CAAC;;;;;;;;"}