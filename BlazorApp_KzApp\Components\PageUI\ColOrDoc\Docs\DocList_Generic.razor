@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using eZeroCore.Web
@using Microsoft.Data.SqlClient
@code {

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public int PageSize { get; set; } = 8;


    string sAddWhere = "titlephotourls<>''";

    string GetPoster(eZeroCore.Web.Document DocObj)
    {
        #region...
        string Poster = DocObj.TitlePhotoUrls.Split(",").FirstOrDefault() ?? "";
        if (Poster is null || string.IsNullOrEmpty(Poster))
        {
            Poster = eZeroCore.Web.BlazorServerUI.WebQuest.GetBingImage();
        }        
        return Poster;
        #endregion
    }
}

<Virtualize ItemsProvider="new DocsVirtualize(){
        SetPageSize=PageSize,
        RoleEnum= eZeroCore.Web.Document.RoleEnumForDoc.WeiCityChannel,
        AddWhere=sAddWhere,
        OrderBy= DocList.OrderByEnum.Hits,
        SortOrderRule= Microsoft.Data.SqlClient.SortOrder.Descending
        }.LoadColumnsAsync" Context="DocObj" ItemSize="6">
    <ItemContent>

        <div class="col-sm-6 col-lg-3 mb-3 mb-md-0">
            <div class="card text-white dark__text-gray-1100 h-100">
                <a href="@(DocObj.GetDocUrl())" target="_blank" class="d-block ratio ratio-16x9">
                    <img class="rounded-top h-100 fit-cover" src="@GetPoster(DocObj)" alt="@DocObj.ColumnName" />
                </a>
                <div class="card-body rounded-top">
                    <div class="d-flex justify-content-between mb-2">
                        <div class="d-flex align-items-center">
                            <a class="btn-link text-decoration-none d-flex align-items-center mx-3 d-none">
                                <span class="fa-solid fa-heart text-body-quaternary me-1"></span>
                                <span class="text-body fs-10 lh-1">125</span>
                            </a>
                            <a class="btn-link text-decoration-none d-flex align-items-center d-none">
                                <span class="fa-solid fa-comment text-body-quaternary me-1"></span>
                                <span class="text-body fs-10 lh-1">125</span>
                            </a>
                            <div class="btn-link text-decoration-none d-flex align-items-center">
                                <span class="fa-solid fa-eye text-body-quaternary me-1"></span>
                                <span class="text-body fs-10 lh-1">
                                    @DocObj.Pageviews
                                </span>
                            </div>
                        </div>
                        <span class="badge badge-phoenix badge-phoenix-primary mb-2 d-none"></span>
                    </div>
                    <a href="@(DocObj.GetDocUrl())" target="_blank" >
                        <h5 class="fw-bold mb-2 lh-sm line-clamp-2">
                            @DocObj.FullTitle
                            @if (!string.IsNullOrEmpty(DocObj.SettingsEntity.RedirectUrl))
                            {
                                <small class="opacity-5 fst-italic fs-6">
                                    <i class="fa-solid fa-up-right-from-square ms-2 fa-2xs"></i>
                                </small>
                            }
                        </h5>
                    </a>
                    <h6 class="mb-2 ColumnName lh-sm line-clamp-2 @DocObj.ColumnId">
                        <a href="@(eZeroCore.Web.Column.GetColumnLink(DocObj.ColumnId))">
                            @DocObj.ColumnName
                        </a>
                    </h6>
                </div>
            </div>
        </div>

    </ItemContent>
    <Placeholder>
        Loading...
    </Placeholder>
</Virtualize>

@code {
    #region Virtualize

    public class DocsVirtualize
    {
        private readonly int PageSizeDefault = 100;
        public string ColumnId { get; set; } = "";
        public int SetPageSize { get; set; } = -1;
        public string AddWhere { get; set; } = "";
        public string DocTags { get; set; } = "";
        public DocList.OrderByEnum OrderBy { get; set; } = DocList.OrderByEnum.none;
        public SortOrder SortOrderRule { get; set; } = SortOrder.Ascending;
        public Document.RoleEnumForDoc RoleEnum { get; set; } = Document.RoleEnumForDoc.None;

        public DocsVirtualize(string columnid = "", int pagesize = 100)
        {
            ColumnId = columnid;
            SetPageSize = pagesize;
        }

        public async ValueTask<ItemsProviderResult<eZeroCore.Web.Document>> LoadColumnsAsync(ItemsProviderRequest request)
        {
            var Documents = await LoadColumnsAsync();
            int totalItemCount = Documents.Count;
            var items = Documents.Skip(request.StartIndex).Take(request.Count).ToList();
            return new ItemsProviderResult<eZeroCore.Web.Document>(items, totalItemCount);
        }

        public async Task<List<eZeroCore.Web.Document>> LoadColumnsAsync()
        {
            var List = new eZeroCore.Web.DocList()
                {
                    RoleEnum = RoleEnum,
                    OrderBy = OrderBy,
                    Tags = DocTags,
                    SortOrderRule = SortOrderRule,
                    PageSize = SetPageSize > 0 ? SetPageSize : PageSizeDefault,
                    ColumnIds = [ColumnId]
                };
            return await List.ListAsync(eZeroCore.Db.CacheHelperV2.CacheNameType.Doc_List);
        }

    }

    #endregion

}