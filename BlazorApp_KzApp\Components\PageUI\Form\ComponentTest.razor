﻿@using System.Linq.Expressions
@code {

    @inherits InputBase<string>

    [Parameter]
    public RenderFragment? ChildContent { get; set; }
    [Parameter]
    public string TextareaId { get; set; } = Guid.NewGuid().ToString();
    [Parameter]
    public string Class { get; set; } = "";
    [Parameter]
    public string Style { get; set; } = "";
    [Parameter]
    public int Row { get; set; } = 3;
    [Parameter]
    public string placeholder { get; set; } = "";
    [Parameter]
    public string Label { get; set; } = "";
    /// <summary>
    /// 0 = no limit
    /// </summary>
    [Parameter]
    public int LimitLenght { get; set; } = 150;

    private string text
    {
        get => CurrentValue ?? "";
        set
        {
            if (CurrentValue != value)
            {
                CurrentValue = value;
            }
        }
    }

    private void OnInput(ChangeEventArgs e)
    {
        if (e.Value is not null)
        {
            text = e.Value.ToString() ?? "";
            _ = HandleInput(e);
        }
    }


    private CancellationTokenSource EventCancellationTokenSource;

    int iTextLength = 0;
    double progressValue = 0;
    Color progressColor = Color.Success;

    private async Task HandleInput(ChangeEventArgs e)
    {
        if (LimitLenght == 0 || e is null || e.Value is null)
        {
            return;
        }
        // 取消之前的延迟任务（如果有的话）
        EventCancellationTokenSource?.Cancel();
        // 创建新的取消令牌和延迟任务
        EventCancellationTokenSource = new CancellationTokenSource();
        var cancellationToken = EventCancellationTokenSource.Token;

        // 等待一段时间（例如100毫秒）以延迟检测
        try
        {
            await Task.Delay(100, cancellationToken);
        }
        catch (TaskCanceledException)
        {
            // 如果任务被取消，不执行任何操作
            return;
        }

        // 如果到这里，说明用户已经停止输入，我们可以检查字符数了
        var inputText = e.Value.ToString() ?? "";
        if (inputText.Length > LimitLenght)
        {
            text = inputText.Substring(0, LimitLenght);
        }
        else
        {
            text = inputText;  // 更新文本但不需要重新渲染，因为文本没有变化
        }

        iTextLength = text.Length;

        progressValue = ((double)iTextLength / LimitLenght) * 100;
        if (progressValue < 50)
        {
            progressColor = Color.Success;
        }
        if (progressValue > 50)
        {
            progressColor = Color.Warning;
        }
        if (progressValue > 80)
        {
            progressColor = Color.Error;
        }

        StateHasChanged();  // 请求重新渲染以更新显示的文本
    }

    protected override bool TryParseValueFromString(string? value, out string result, out string? validationErrorMessage)
    {
        result = value ?? string.Empty;
        validationErrorMessage = null;  // 无验证错误
        return true;  // 解析成功
    }

}

<div class="@Class">
    <label class="form-label @(string.IsNullOrEmpty(Label)?"d-none":"")">
        @Label
    </label>
    @if (LimitLenght > 0)
    {
        <span class="ProgressCircular ms-2">
            <MudProgressCircular StrokeWidth="5" Style="height:15px;width:15px;margin-bottom: -2px;" Color="@progressColor" Value="@progressValue" />
        </span>
    }
    <textarea @bind="text"
    @oninput="OnInput"
              id="@TextareaId"
              class="form-control"
              rows="@Row"
              style="@Style"
              placeholder="@placeholder">@ChildContent</textarea>
</div>