@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; } 

    string DomId { get; set; } = Guid.NewGuid().ToString();

    [Parameter]
    public RenderFragment? ChildContent { get; set; }
}

<li class="nav-item read" role="presentation">
    <a class="nav-link d-flex align-items-center justify-content-center p-2  active" data-bs-toggle="tab" data-chat-thread="data-chat-thread" href="#tab-thread-1" role="tab" aria-selected="true">
        <div class="avatar avatar-xl status-online position-relative me-2 me-sm-0 me-xl-2">
            <img class="rounded-circle border border-2 border-light-subtle" src="../assets/img/team/20.webp" alt="" />
        </div>
        <div onclick="chatGroupMessage.removeSidebarShowClass();" class="flex-1 d-sm-none d-xl-block">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="text-body fw-normal name text-nowrap"><PERSON><PERSON><PERSON></h5>
                <p class="fs-10 text-body-tertiary text-opacity-85 mb-0 text-nowrap">
                    Just now
                </p>
            </div>
            <div class="d-flex justify-content-between">
                <p class="fs-9 mb-0 line-clamp-1 text-body-tertiary text-opacity-85 message">This is a message from you</p>
            </div>
        </div>
    </a>
</li>