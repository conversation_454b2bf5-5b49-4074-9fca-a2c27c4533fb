﻿@code {
    [Parameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public List<string> Values { get; set; } = new List<string>();

    [Parameter]
    public List<string> Titles { get; set; } = new List<string>();

    [Parameter]
    public string SetDefaultSelectedValue { get; set; } = "";
    [Parameter]
    public List<int> SetDefaultSelectedIndexValue { get; set; } = new List<int>();

    [Parameter]
    public EventCallback<List<string>> OnSelectionChanged { get; set; }

    [Parameter]
    public bool UseRadioButtons { get; set; } = false;  // 默认使用复选框

    [Parameter]
    public bool SelectFirstClearOther { get; set; }

    public async Task ClearSelections()
    {
        selectedValues.Clear();
        await OnSelectionChanged.InvokeAsync(selectedValues.ToList()); // Optional: notify about the change
    }

    string DomGuid = "";
    protected override Task OnInitializedAsync()
    {
        DomGuid = "E_CkBoxList" + Math.Abs(Titles.GetHashCode()).ToString();

        // 设置多选默认值
        if (SetDefaultSelectedIndexValue != null && SetDefaultSelectedIndexValue.Any())
        {
            selectedValues.Clear();
            foreach (var index in SetDefaultSelectedIndexValue)
            {
                if (index >= 0 && index < Values.Count)
                {
                    selectedValues.Add(Values[index]);
                }
            }
        }
        else if (!string.IsNullOrWhiteSpace(SetDefaultSelectedValue))
        {
            selectedValues.Clear();
            selectedValues.Add(SetDefaultSelectedValue);
        }
        else
        {
            selectedValues.Add(Values[0]);
        }

        return base.OnInitializedAsync();
    }

    public void SetValue(string value)
    {
        SetDefaultSelectedValue = value;
        selectedValues.Clear();
        selectedValues.Add(value);
        StateHasChanged();
    }

    public void SetValues(List<int> values)
    {
        SetDefaultSelectedIndexValue = values;
        // 设置多选默认值
        if (SetDefaultSelectedIndexValue != null && SetDefaultSelectedIndexValue.Any())
        {
            selectedValues.Clear();
            if (SelectFirstClearOther && SetDefaultSelectedIndexValue.Contains(0))
            {
                selectedValues.Add(Values[0]);
            }
            else
            {
                foreach (var index in SetDefaultSelectedIndexValue)
                {
                    if (index >= 0 && index < Values.Count)
                    {
                        selectedValues.Add(Values[index]);
                    }
                }
            }
        }
        StateHasChanged();
    }

    private HashSet<string> selectedValues = new HashSet<string>();

    private async Task HandleChange(int index)
    {
        string value = Values[index];
        if (UseRadioButtons)
        {
            selectedValues.Clear();  // 清除之前的选择
            selectedValues.Add(value);  // 添加当前的选择
        }
        else
        {
            if (selectedValues.Contains(value))
            {
                selectedValues.Remove(value);
            }
            else
            {
                selectedValues.Add(value);
            }
            if (SelectFirstClearOther)
            {
                if (index == 0)
                {
                    selectedValues.Clear();
                    selectedValues.Add(Values[0]);
                }
                else
                {
                    selectedValues.Remove(Values[0]);
                }
                StateHasChanged();
            }
        }

        await OnSelectionChanged.InvokeAsync(selectedValues.ToList());
    }

    private string GetInputType()
    {
        return UseRadioButtons ? "radio" : "checkbox";
    }

}

<div id="@DomGuid" class="d-flex flex-wrap" role="group" @key=@DomGuid>
    @for (int i = 0; i < Values.Count; i++)
    {
        var localIndex = i;

        // 确保 localIndex 不超过 Titles 的边界
        if (localIndex < Titles.Count && !string.IsNullOrWhiteSpace(Titles[localIndex]))
        {
            var keyId = DomGuid + Math.Abs(Titles[localIndex].GetHashCode());
            <div data-value="@Values[localIndex],@SetDefaultSelectedValue,[@(string.Join(',',selectedValues))],[@(string.Join(',',Values))]"
                 class="btn-group stkCkboxTheme"
                 @key="@(keyId+i)">
                <input type="@GetInputType()" id="@(keyId)"
                       checked="@(selectedValues.Contains(Values[localIndex]) || (selectedValues.Count == 0 && (SetDefaultSelectedIndexValue?.Contains(localIndex) == true || Values[localIndex].Equals(SetDefaultSelectedValue, StringComparison.OrdinalIgnoreCase))))"
                       @onchange="() => HandleChange(localIndex)"
                       class="btn-check" autocomplete="off" />
                <label class="btn btn-outline-light" for="@(keyId)">@Titles[localIndex]</label>
            </div>
        }
    }
</div>