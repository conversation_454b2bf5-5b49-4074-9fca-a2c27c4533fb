@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public int ActivateIndex { get; set; } = -1;

    eZeroCore.Web.ColumnList columnList = new();
    List<eZeroCore.Web.Column> Columns = new();
    protected override async Task OnInitializedAsync()
    {
        columnList = new();
        columnList.ParentId = "COL7A73FC8C58";
        columnList.OrderBy = eZeroCore.Web.ColumnList.OrderByEnum.Followorder;
        columnList.SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending;
        Columns = await columnList.ListAsync();
    }
}

<ul class="d-sm-block d-md-none navbar fixed-bottom mb-0 bg-body align-items-center border-top pb-5">

    @foreach (eZeroCore.Web.Column col in Columns)
    {
        <li class="nav-item text-center" style="list-style-type: none;">
            <a href="@col.Redirect" class="mb-0 @(ActivateIndex==0?"text-warning":"")">
                <i class="@col.SettingsEntity.Icon fa-xl"></i>
                <lable class="d-block mt-1 fs-10">
                    @col.ColumnName
                </lable>
            </a>
        </li>
    }
    
</ul>
