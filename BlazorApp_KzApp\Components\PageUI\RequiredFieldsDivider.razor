@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public string ClassName { get; set; } = "";

    [Parameter]
    public string Title { get; set; } = "Required Fields";

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public bool Center { get; set; }

    [Parameter]
    public bool IsOptions { get; set; }

    string TitleICONClassName { get; set; } = "text-warning-lighter";
    protected override Task OnInitializedAsync()
    {
        if (IsOptions)
        {
            TitleICONClassName = "text-success";
        }
        return base.OnInitializedAsync();
    }
}


<div class="position-relative">
    <hr class="bg-body-secondary my-5 @ClassName">
    <div class="@(Center?"divider-content-center":"position-absolute top-50 start-0 translate-middle-y px-3 bg-body rounded")">
        @if (ChildContent is null)
        {
            <i class="fa-regular fa-pen-field @TitleICONClassName me-1 fa-lg"></i>
            <span class="@(IsOptions?"":"text-info") Upper fs-10">
                @Title
            </span>
        }
        else
        {
            @ChildContent
        }
    </div>
</div>