
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web.Virtualization
@code {    

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public bool LeftMenusVisible { get; set; } = true;

    [Parameter]
    public bool TopMenusvisible { get; set; } = true;

    [Parameter]
    public bool TopRightMenus { get; set; } = true;

    [Parameter]
    public string TopBarCss { get; set; } = "";

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public bool UserLoginOrMenus { get; set; } = true;

    string ChannelId = "";
    protected override Task OnInitializedAsync()
    {
        if(!CObj.IsStk)
        {
            ChannelId = CObj.ThisChannel?.ColumnId ?? "";
        }
        return base.OnInitializedAsync();
    }
}

<ToplMenus_Framework UserLoginOrMenus=@UserLoginOrMenus LeftMenusVisible="@LeftMenusVisible" TopRightMenus="@TopRightMenus" TopBarCss="@TopBarCss">

    @if (!string.IsNullOrEmpty(ChannelId))
    {
        <Virtualize ItemsProvider="new MudUI.MenusVirtualize(CObj, ChannelId,10).LoadColumnsAsync" Context="column" ItemSize="6">
            <ItemContent>
                @if(TopMenusvisible)
                {
                    @*
            <TopMenus_NavItemGroup ExistMenu="@column.ExistSubColumn" Url="@column.GetColumnUrl()" GroupName="@column.ColumnName" ParentColumn="column" @key="@($"TopMenus_NavItemGroup{column.ColumnId}")" />
                    *@
                    @ChildContent
                }
            </ItemContent>
            <Placeholder>
                Loading...
            </Placeholder>
        </Virtualize>
    }

</ToplMenus_Framework>