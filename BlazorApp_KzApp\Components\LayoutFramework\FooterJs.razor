﻿@inject IJSRuntime JS
@code {
    protected override bool ShouldRender()
    {
        return false;
    }
}

<!-- ===============================================-->
<!--  defer  JavaScripts-->
<!-- ===============================================-->

<script src="/lib/Ph117/public/vendors/popper/popper.min.js" defer></script>
<script src="/lib/Ph117/public/vendors/bootstrap/bootstrap.min.js" defer></script>
<script src="/lib/Ph117/public/vendors/anchorjs/anchor.min.js" defer></script>
<script src="/lib/Ph117/public/vendors/is/is.min.js" defer></script>
@*
<script src="/lib/Ph117/public/vendors/fontawesome/all.min.js" defer></script>
*@
<script src="https://kit.fontawesome.com/c85fff200d.js" crossorigin="anonymous"></script>

<script src="/lib/Ph117/public/vendors/lodash/lodash.min.js" defer></script>
<script src="/lib/Ph117/public/vendors/feather-icons/feather.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/clipboard.js/2.0.11/clipboard.min.js" defer></script>
@*
    <script src="/lib/Ph117/public/assets/js/phoenix.js"></script>
*@

