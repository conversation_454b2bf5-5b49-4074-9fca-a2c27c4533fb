﻿<div class="form-floating @Class">
    @ChildContent
    <label for="@InputId">
        @Title
    </label>
</div>

@code {
        /*
        private string inputId;
            public Input()
            {
            inputId = "input_" + Guid.NewGuid().ToString("N");
    }
    */
    protected async override void OnInitialized()
    {
        if (string.IsNullOrEmpty(InputId))
        {
            InputId = General.GetBaseId(Title);
        }
        await Task.Delay(0);
    }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string InputId { get; set; } = "";

    [Parameter]
    public string Title { get; set; } = "Input Name";

    [Parameter]
    public string Class { get; set; } = "";
}
