﻿@code {
    [Parameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public EventCallback<List<string>> OnEdited { get; set; }

    [Parameter]
    public string InputClassName { get; set; } = "";

    [Parameter]
    public string LableClassName { get; set; } = "Upper";

    [Parameter]
    public string AddTagTipName { get; set; } = "Add Tag";

    [Parameter]
    public List<string> SetDefaultList { get; set; } = [];

    private List<string> tags = new List<string>();
    private bool isEditing = false;
    private string currentInput;
    private ElementReference inputElement;

    private void StartEditing()
    {
        isEditing = true;
        currentInput = string.Empty;
        StateHasChanged();
        FocusInput();
    }

    private async void FocusInput()
    {
        await Task.Delay(1); // Wait for the input to render
        await inputElement.FocusAsync();
    }

    private void StopEditing()
    {
        if (!string.IsNullOrWhiteSpace(currentInput))
        {
            // 拆分输入，假设使用逗号、分号、空格作为分隔符
            currentInput = currentInput.Replace("，",",");
            currentInput = currentInput.Replace("；", ";");
            var newTags = currentInput
                .Split(new[] { ',', ';', ' ' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var tag in newTags)
            {
                if (!tags.Contains(tag))
                {
                    tags.Add(tag);
                }
                else
                {
                    // 可以在这里处理标签已存在的情况，例如显示一个消息
                    //Console.WriteLine("Tag already exists."); // 实际项目中可能使用弹窗或是页面上的提示信息
                }
            }

            // 通知更新后的 tags
            OnEdited.InvokeAsync(tags);
        }
        currentInput = string.Empty;
        isEditing = false;
    }

    private void HandleKeyDown(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            StopEditing();
        }
    }

    async Task OnRemove(string item)
    {
        tags.Remove(item);
        await OnEdited.InvokeAsync(tags);
        await Task.Delay(0);
        //await InitDataTable();
    }

    string DomGuid = "";
    protected override Task OnInitializedAsync()
    {
        if (SetDefaultList.Count > 0)
        {
            tags = SetDefaultList;
        }
        else
        {
            Clear();
        }
        DomGuid = "E_InputTags" + Math.Abs(Guid.NewGuid().GetHashCode()).ToString();
        return base.OnInitializedAsync();
    }
    public void Clear()
    {
        SetDefaultList.Clear();
        tags.Clear();
    }
    public void SetValue(List<string> newValues)
    {
        if (newValues.Count > 0)
        {
            SetDefaultList = newValues;
            tags = SetDefaultList;
        }
        else
        {
            SetDefaultList.Clear();
            tags.Clear();
        }
        StateHasChanged();
    }
}

<div class="tag-editor" @key=@DomGuid>
    <div class="d-flex flex-wrap align-items-center">
        @foreach (var tag in tags)
        {
            <E_Tag ClassName="@LableClassName" OnRemove="()=>OnRemove(tag)" @key="@(DomGuid+tag)" CObj="@CObj">
                @tag
            </E_Tag>
        }

        @if (isEditing)
        {
            <input class="form-control d-inline-block m-1 p-1 w-70px @InputClassName" type="text" @ref="inputElement" @onkeydown="HandleKeyDown" @onblur="StopEditing" @bind="currentInput" />
        }
        else
        {
            <span class="badge text-bg-secondary border p-2" @onclick="StartEditing">+ @AddTagTipName</span>
        }
    </div>
</div>
