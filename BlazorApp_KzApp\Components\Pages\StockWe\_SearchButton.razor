@using Radzen

<RadzenButton Click="@OnClick"
              Text="@Text"
              ButtonStyle="@ButtonStyle"
              Style="@ComputedStyle"
              class="@CssClass"
              Disabled="@Disabled" />

@code {
    [Parameter] public string Text { get; set; } = "按钮";
    
    [Parameter] public string BackgroundColor { get; set; } = "#4A6FFF";
    
    [Parameter] public string TextColor { get; set; } = "white";
    
    [Parameter] public string CssClass { get; set; } = "";
    
    [Parameter] public string CustomStyle { get; set; } = "";

    [Parameter] public ButtonStyle ButtonStyle { get; set; } = ButtonStyle.Primary;
    
    [Parameter] public EventCallback OnClick { get; set; }
    
    [Parameter] public bool Disabled { get; set; } = false;
    
    [Parameter] public string BorderRadius { get; set; } = "8px";
    
    [Parameter] public string Padding { get; set; } = "0 15px";
    
    [Parameter] public string Height { get; set; } = "28px";
    
    [Parameter] public string FontSize { get; set; } = "14px";
    
    [Parameter] public string MarginLeft { get; set; } = "5px";

    [Parameter] public string MarginRight { get; set; } = "";
    
    [Parameter] public string MarginTop { get; set; } = "";
    
    [Parameter] public string MarginBottom { get; set; } = "";
    
    private string ComputedStyle
    {
        get
        {
            var styles = new List<string>
            {
                $"background-color:{BackgroundColor}",
                $"color:{TextColor}",
                $"border-radius:{BorderRadius}",
                $"padding:{Padding}",
                $"height:{Height}",
                $"font-size:{FontSize}"
            };
            
            // 只添加非空的边距
            if (!string.IsNullOrWhiteSpace(MarginLeft))
                styles.Add($"margin-left:{MarginLeft}");
            if (!string.IsNullOrWhiteSpace(MarginRight))
                styles.Add($"margin-right:{MarginRight}");
            if (!string.IsNullOrWhiteSpace(MarginTop))
                styles.Add($"margin-top:{MarginTop}");
            if (!string.IsNullOrWhiteSpace(MarginBottom))
                styles.Add($"margin-bottom:{MarginBottom}");
            
            var baseStyle = string.Join("; ", styles);
            
            // 追加自定义样式
            if (!string.IsNullOrWhiteSpace(CustomStyle))
            {
                baseStyle += "; " + CustomStyle.TrimStart(';', ' ');
            }
            
            return baseStyle;
        }
    }
} 