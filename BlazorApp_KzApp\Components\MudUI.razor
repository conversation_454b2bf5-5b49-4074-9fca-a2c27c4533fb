@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using eZeroCore.Web
@using Microsoft.Data.SqlClient
@code {

    protected override bool ShouldRender()
    {
        return false;
    }

    #region Dialog

    public static DialogOptions GetOptions(MaxWidth width = MaxWidth.ExtraSmall, bool ClickClose = false, bool Top = false, bool blurBg = true)
    {
        #region...
        return new MudBlazor.DialogOptions
        {
            FullWidth = true,
            BackgroundClass = (blurBg ? "blurBg" : null),
            MaxWidth = width,
            BackdropClick = ClickClose,
            CloseButton = ClickClose,
            Position = Top ? DialogPosition.TopCenter : MudBlazor.DialogPosition.Center,
            CloseOnEscapeKey = ClickClose
        };
        #endregion
    }

    #endregion

    #region  Snackbar
    public enum SnackbarType
    {
        Alert,
        Success,
        Info,
    }
    public static void DisplayMessage(ISnackbar snackbar, string message, SnackbarType type = SnackbarType.Alert, int? Duration = null)
    {
        #region ...
        MudBlazor.Severity severity = Severity.Success;
        MudBlazor.Variant variant = Variant.Filled;
        int iVisibleStateDuration = 2500;
        snackbar.Clear();
        snackbar.Configuration.PositionClass = Defaults.Classes.Position.TopCenter;
        switch (type)
        {
            case SnackbarType.Success:
                severity = Severity.Success;
                break;
            case SnackbarType.Alert:
                severity = Severity.Error;
                break;
            case SnackbarType.Info:
                severity = Severity.Info;
                variant = Variant.Outlined;
                iVisibleStateDuration = 10000;
                snackbar.Configuration.PositionClass = Defaults.Classes.Position.BottomLeft;
                break;
        }
        snackbar.Configuration.SnackbarVariant = variant;
        snackbar.Configuration.ShowCloseIcon = type == SnackbarType.Info;
        snackbar.Add(message, severity,
        config =>
        {
            //config.HideIcon = type != SnackbarType.Info;
            config.VisibleStateDuration = Duration is null ? iVisibleStateDuration : Duration.Value;
        });
        #endregion
    }
    #endregion

    #region Virtualize

    public class MenusVirtualize
    {
        private string ColumnId { get; set; }

        public int RootPageSize { get; set; } = -1;
        public Document.RoleEnumForDoc RoleEnum { get; set; } = Document.RoleEnumForDoc.None;
        public ColumnList.OrderByEnum OrderBy { get; set; } = ColumnList.OrderByEnum.Followorder;
        public string AddWhere { get; set; } = "";
        public bool DisplayPrivate { get; set; }
        public SortOrder SortOrderRule { get; set; } = SortOrder.Ascending;
        public bool HiddenVisible { get; set; }

        private readonly int PageSizeDefault = 100;
        public MenusVirtualize(BlzHelperObj CObj, string columnid = "", int pagesize = 100, string addWhere = "")
        {
            ColumnId = columnid;
            RootPageSize = pagesize;
            AddWhere = addWhere;
            string ChannelCreateUserId = CObj.ThisChannel?.CreateMemberId ?? "";
            HiddenVisible = eZeroCore.Settings.GetSysMode == eZeroCore.Settings.SysMode.Stk ? CObj.IsKyrison : HiddenVisible;
        }

        public async ValueTask<ItemsProviderResult<eZeroCore.Web.Column>> LoadColumnsAsync(ItemsProviderRequest request)
        {
            var columns = await LoadColumnsAsync();
            int totalItemCount = columns.Count;
            var items = columns.Skip(request.StartIndex).Take(request.Count).ToList();
            return new ItemsProviderResult<eZeroCore.Web.Column>(items, totalItemCount);
        }

        public async Task<List<eZeroCore.Web.Column>> LoadColumnsAsync()
        {
            var columnList = new eZeroCore.Web.ColumnList
            {
                RoleEnum = RoleEnum,
                ParentId = ColumnId,
                PageSize = RootPageSize > 0 ? RootPageSize : PageSizeDefault,
                SortOrderRule = SortOrderRule,
                AddWhere = AddWhere,
                OrderBy = OrderBy,
                HiddenVisible = HiddenVisible
            };

            return await columnList.ListAsync(eZeroCore.Db.CacheHelperV2.CacheNameType.Col_MenusTree);
        }

    }

    #endregion

}

