{"version": 3, "file": "projectmanagement-dashboard.js", "sources": ["../../../src/js/theme/charts/echarts/echarts-utils.js", "../../../src/js/theme/charts/echarts/issues-discovered-chart.js", "../../../src/js/theme/charts/echarts/zero-burnout-chart.js", "../../../src/js/theme/charts/echarts/zero-rodamap-chart.js", "../../../src/js/pages/projectmanagement-dashboard.js"], "sourcesContent": ["// import * as echarts from 'echarts';\r\nconst { merge } = window._;\r\n\r\n// form config.js\r\nexport const echartSetOption = (\r\n  chart,\r\n  userOptions,\r\n  getDefaultOptions,\r\n  responsiveOptions\r\n) => {\r\n  const { breakpoints, resize } = window.phoenix.utils;\r\n  const handleResize = options => {\r\n    Object.keys(options).forEach(item => {\r\n      if (window.innerWidth > breakpoints[item]) {\r\n        chart.setOption(options[item]);\r\n      }\r\n    });\r\n  };\r\n\r\n  const themeController = document.body;\r\n  // Merge user options with lodash\r\n  chart.setOption(merge(getDefaultOptions(), userOptions));\r\n\r\n  const navbarVerticalToggle = document.querySelector(\r\n    '.navbar-vertical-toggle'\r\n  );\r\n  if (navbarVerticalToggle) {\r\n    navbarVerticalToggle.addEventListener('navbar.vertical.toggle', () => {\r\n      chart.resize();\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    });\r\n  }\r\n\r\n  resize(() => {\r\n    chart.resize();\r\n    if (responsiveOptions) {\r\n      handleResize(responsiveOptions);\r\n    }\r\n  });\r\n  if (responsiveOptions) {\r\n    handleResize(responsiveOptions);\r\n  }\r\n\r\n  themeController.addEventListener(\r\n    'clickControl',\r\n    ({ detail: { control } }) => {\r\n      if (control === 'phoenixTheme') {\r\n        chart.setOption(window._.merge(getDefaultOptions(), userOptions));\r\n      }\r\n      if (responsiveOptions) {\r\n        handleResize(responsiveOptions);\r\n      }\r\n    }\r\n  );\r\n};\r\n// -------------------end config.js--------------------\r\n\r\nconst echartTabs = document.querySelectorAll('[data-tab-has-echarts]');\r\nif (echartTabs) {\r\n  echartTabs.forEach(tab => {\r\n    tab.addEventListener('shown.bs.tab', e => {\r\n      const el = e.target;\r\n      const { hash } = el;\r\n      const id = hash || el.dataset.bsTarget;\r\n      const content = document.getElementById(id.substring(1));\r\n      const chart = content?.querySelector('[data-echart-tab]');\r\n      if (chart) {\r\n        window.echarts.init(chart).resize();\r\n      }\r\n    });\r\n  });\r\n}\r\n\r\nexport const tooltipFormatter = (params, dateFormatter = 'MMM DD') => {\r\n  let tooltipItem = ``;\r\n  params.forEach(el => {\r\n    tooltipItem += `<div class='ms-1'>\r\n        <h6 class=\"text-body-tertiary\"><span class=\"fas fa-circle me-1 fs-10\" style=\"color:${\r\n          el.borderColor ? el.borderColor : el.color\r\n        }\"></span>\r\n          ${el.seriesName} : ${\r\n      typeof el.value === 'object' ? el.value[1] : el.value\r\n    }\r\n        </h6>\r\n      </div>`;\r\n  });\r\n  return `<div>\r\n            <p class='mb-2 text-body-tertiary'>\r\n              ${\r\n                window.dayjs(params[0].axisValue).isValid()\r\n                  ? window.dayjs(params[0].axisValue).format(dateFormatter)\r\n                  : params[0].axisValue\r\n              }\r\n            </p>\r\n            ${tooltipItem}\r\n          </div>`;\r\n};\r\n\r\nexport const handleTooltipPosition = ([pos, , dom, , size]) => {\r\n  // only for mobile device\r\n  if (window.innerWidth <= 540) {\r\n    const tooltipHeight = dom.offsetHeight;\r\n    const obj = { top: pos[1] - tooltipHeight - 20 };\r\n    obj[pos[0] < size.viewSize[0] / 2 ? 'left' : 'right'] = 5;\r\n    return obj;\r\n  }\r\n  return null; // else default behaviour\r\n};\r\n", "import { echartSetOption, handleTooltipPosition } from './echarts-utils';\r\n\r\n// dayjs.extend(advancedFormat);\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Total Sales                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst issuesDiscoveredChartInit = () => {\r\n  const { getColor, getData, toggleColor } = window.phoenix.utils;\r\n  const issuesDiscoveredChartEl = document.querySelector('.echart-issue-chart');\r\n\r\n  if (issuesDiscoveredChartEl) {\r\n    const userOptions = getData(issuesDiscoveredChartEl, 'echarts');\r\n    const chart = window.echarts.init(issuesDiscoveredChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      color: [\r\n        toggleColor(getColor('info-light'), getColor('info-dark')),\r\n        toggleColor(getColor('warning-light'), getColor('warning-dark')),\r\n        toggleColor(getColor('danger-light'), getColor('danger-dark')),\r\n        toggleColor(getColor('success-light'), getColor('success-dark')),\r\n        getColor('primary')\r\n      ],\r\n      tooltip: {\r\n        trigger: 'item',\r\n        position: (...params) => handleTooltipPosition(params),\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      responsive: true,\r\n      maintainAspectRatio: false,\r\n\r\n      series: [\r\n        {\r\n          name: 'Tasks assigned to me',\r\n          type: 'pie',\r\n          radius: ['48%', '90%'],\r\n          startAngle: 30,\r\n          avoidLabelOverlap: false,\r\n          // label: {\r\n          //   show: false,\r\n          //   position: 'center'\r\n          // },\r\n\r\n          label: {\r\n            show: false,\r\n            position: 'center',\r\n            formatter: '{x|{d}%} \\n {y|{b}}',\r\n            rich: {\r\n              x: {\r\n                fontSize: 31.25,\r\n                fontWeight: 800,\r\n                color: getColor('tertiary-color'),\r\n                padding: [0, 0, 5, 15]\r\n              },\r\n              y: {\r\n                fontSize: 12.8,\r\n                color: getColor('tertiary-color'),\r\n                fontWeight: 600\r\n              }\r\n            }\r\n          },\r\n          emphasis: {\r\n            label: {\r\n              show: true\r\n            }\r\n          },\r\n          labelLine: {\r\n            show: false\r\n          },\r\n          data: [\r\n            { value: 78, name: 'Product design' },\r\n            { value: 63, name: 'Development' },\r\n            { value: 56, name: 'QA & Testing' },\r\n            { value: 36, name: 'Customer queries' },\r\n            { value: 24, name: 'R & D' }\r\n          ]\r\n        }\r\n      ],\r\n      grid: {\r\n        bottom: 0,\r\n        top: 0,\r\n        left: 0,\r\n        right: 0,\r\n        containLabel: false\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default issuesDiscoveredChartInit;\r\n", "import { echartSetOption, tooltipFormatter } from './echarts-utils';\r\n\r\n/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Total Sales                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst zeroBurnOutChartInit = () => {\r\n  const { getColor, getData, getPastDates } = window.phoenix.utils;\r\n  const $zeroBurnOutChartEl = document.querySelector(\r\n    '.echart-zero-burnout-chart'\r\n  );\r\n\r\n  if ($zeroBurnOutChartEl) {\r\n    const userOptions = getData($zeroBurnOutChartEl, 'echarts');\r\n    const chart = window.echarts.init($zeroBurnOutChartEl);\r\n\r\n    const getDefaultOptions = () => ({\r\n      tooltip: {\r\n        trigger: 'axis',\r\n        backgroundColor: getColor('body-bg'),\r\n        borderColor: getColor('secondary-bg'),\r\n        formatter: params => tooltipFormatter(params, 'MMM DD, YYYY'),\r\n        axisPointer: {\r\n          shadowStyle: {\r\n            color: 'red'\r\n          }\r\n        },\r\n        extraCssText: 'z-index: 1000'\r\n      },\r\n      legend: {\r\n        bottom: '10',\r\n        data: [\r\n          {\r\n            name: 'Open',\r\n            icon: 'roundRect'\r\n          },\r\n          {\r\n            name: 'Issues found',\r\n            icon: 'roundRect'\r\n          },\r\n          {\r\n            name: 'In Progress',\r\n            icon: 'roundRect'\r\n          }\r\n        ],\r\n        itemWidth: 16,\r\n        itemHeight: 8,\r\n        itemGap: 10,\r\n        inactiveColor: getColor('quaternary-color'),\r\n        inactiveBorderWidth: 0,\r\n        textStyle: {\r\n          color: getColor('body-color'),\r\n          fontWeight: 600,\r\n          fontSize: 16,\r\n          fontFamily: 'Nunito Sans'\r\n        }\r\n      },\r\n\r\n      // grid: {\r\n      //   left: '0%',\r\n      //   right: '4%',\r\n      //   bottom: '15%',\r\n      //   top: '10%',\r\n      //   containLabel: true,\r\n      //   show: true\r\n      // },\r\n\r\n      xAxis: [\r\n        {\r\n          show: true,\r\n          interval: 2,\r\n          axisLine: {\r\n            lineStyle: {\r\n              type: 'solid',\r\n              color: getColor('border-color')\r\n            }\r\n          },\r\n          axisLabel: {\r\n            color: getColor('body-color'),\r\n            formatter: data => window.dayjs(data).format('D MMM'),\r\n            interval: 5,\r\n            align: 'left',\r\n            margin: 20,\r\n            fontSize: 12.8\r\n          },\r\n          axisTick: {\r\n            show: true,\r\n            length: 15\r\n            // alignWithLabel: true\r\n          },\r\n          splitLine: {\r\n            interval: 0,\r\n            show: true,\r\n            lineStyle: {\r\n              color: getColor('border-color'),\r\n              type: 'dashed'\r\n            }\r\n          },\r\n          type: 'category',\r\n          boundaryGap: false,\r\n          data: getPastDates(15)\r\n        },\r\n        {\r\n          show: true,\r\n          interval: 2,\r\n          axisLine: {\r\n            show: false\r\n          },\r\n          axisLabel: {\r\n            show: false\r\n          },\r\n          axisTick: {\r\n            show: false\r\n          },\r\n          splitLine: {\r\n            interval: 1,\r\n            show: true,\r\n            lineStyle: {\r\n              color: getColor('border-color'),\r\n              type: 'solid'\r\n            }\r\n          },\r\n          boundaryGap: false,\r\n          data: getPastDates(15)\r\n        }\r\n      ],\r\n      yAxis: {\r\n        show: true,\r\n        type: 'value',\r\n        axisLine: {\r\n          lineStyle: {\r\n            type: 'solid',\r\n            color: getColor('border-color')\r\n          }\r\n        },\r\n        axisLabel: {\r\n          color: getColor('body-color'),\r\n          margin: 20,\r\n          fontSize: 12.8,\r\n          interval: 0\r\n        },\r\n        splitLine: {\r\n          show: true,\r\n          lineStyle: {\r\n            color: getColor('border-color'),\r\n            type: 'solid'\r\n          }\r\n        },\r\n        axisTick: {\r\n          show: true,\r\n          length: 15,\r\n          alignWithLabel: true,\r\n          lineStyle: {\r\n            color: getColor('border-color')\r\n          }\r\n        }\r\n        // data: ['0', '10', '20']\r\n      },\r\n      series: [\r\n        {\r\n          name: 'Estimated',\r\n          type: 'line',\r\n          symbol: 'none',\r\n          data: [20, 17.5, 15, 15, 15, 12.5, 10, 7.5, 5, 2.5, 2.5, 2.5, 0],\r\n          lineStyle: {\r\n            width: 0\r\n          },\r\n          areaStyle: {\r\n            color: getColor('primary-light'),\r\n            opacity: 0.075\r\n          },\r\n          tooltip: {\r\n            show: false\r\n          }\r\n        },\r\n        {\r\n          name: 'Issues found',\r\n          type: 'line',\r\n          symbolSize: 6,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('success'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('success')\r\n          },\r\n          symbol: 'circle',\r\n          data: [3, 1, 2, 4, 3, 1]\r\n        },\r\n        {\r\n          name: 'Open',\r\n          type: 'line',\r\n          symbolSize: 6,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('info'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('info')\r\n          },\r\n          symbol: 'circle',\r\n          data: [6, 5, 4, 6, 5, 5]\r\n        },\r\n        {\r\n          name: 'In Progress',\r\n          type: 'line',\r\n          symbolSize: 6,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('warning'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('warning')\r\n          },\r\n          symbol: 'circle',\r\n          data: [11, 12, 11, 9, 11, 6]\r\n        },\r\n        {\r\n          name: 'Actual',\r\n          type: 'line',\r\n          symbolSize: 6,\r\n          itemStyle: {\r\n            color: getColor('body-highlight-bg'),\r\n            borderColor: getColor('quaternary-color'),\r\n            borderWidth: 2\r\n          },\r\n          lineStyle: {\r\n            color: getColor('quaternary-color'),\r\n            type: 'dashed'\r\n          },\r\n          symbol: 'circle',\r\n          data: [20, 19, 15, 14, 12, 8]\r\n        }\r\n      ],\r\n      grid: {\r\n        right: 5,\r\n        left: 0,\r\n        bottom: '15%',\r\n        top: 20,\r\n        containLabel: true\r\n      }\r\n    });\r\n\r\n    echartSetOption(chart, userOptions, getDefaultOptions);\r\n  }\r\n};\r\n\r\nexport default zeroBurnOutChartInit;\r\n", "/* -------------------------------------------------------------------------- */\r\n/*                             Echarts Total Sales                            */\r\n/* -------------------------------------------------------------------------- */\r\n\r\nconst zeroRoadmapChartInit = () => {\r\n  const { getItemFromStore } = window.phoenix.utils;\r\n  const zeroRoadMapEl = document.querySelector('.gantt-zero-roadmap');\r\n\r\n  if (zeroRoadMapEl) {\r\n    const chartEl = zeroRoadMapEl.querySelector('.gantt-zero-roadmap-chart');\r\n\r\n    window.gantt.plugins({\r\n      tooltip: true\r\n    });\r\n\r\n    window.gantt.config.date_format = '%Y-%m-%d %H:%i';\r\n    window.gantt.config.scale_height = 0;\r\n    window.gantt.config.row_height = 36;\r\n    window.gantt.config.bar_height = 12;\r\n    window.gantt.config.drag_move = false;\r\n    window.gantt.config.drag_progress = false;\r\n    window.gantt.config.drag_resize = false;\r\n    window.gantt.config.drag_links = false;\r\n    window.gantt.config.details_on_dblclick = false;\r\n    window.gantt.config.click_drag = false;\r\n\r\n    if (getItemFromStore('phoenixIsRTL')) {\r\n      window.gantt.config.rtl = true;\r\n    }\r\n\r\n    const zoomConfig = {\r\n      levels: [\r\n        {\r\n          name: 'month',\r\n          scales: [\r\n            { unit: 'month', format: '%F, %Y' },\r\n            { unit: 'week', format: 'Week #%W' }\r\n          ]\r\n        },\r\n\r\n        {\r\n          name: 'year',\r\n          scales: [{ unit: 'year', step: 1, format: '%Y' }]\r\n        },\r\n        {\r\n          name: 'week',\r\n          scales: [\r\n            {\r\n              unit: 'week',\r\n              step: 1,\r\n              format: date => {\r\n                const dateToStr = window.gantt.date.date_to_str('%d %M');\r\n                const endDate = window.gantt.date.add(date, -6, 'day');\r\n                const weekNum = window.gantt.date.date_to_str('%W')(date);\r\n                return (\r\n                  '#' +\r\n                  weekNum +\r\n                  ', ' +\r\n                  dateToStr(date) +\r\n                  ' - ' +\r\n                  dateToStr(endDate)\r\n                );\r\n              }\r\n            },\r\n            { unit: 'day', step: 1, format: '%j %D' }\r\n          ]\r\n        }\r\n      ]\r\n    };\r\n\r\n    gantt.ext.zoom.init(zoomConfig);\r\n    gantt.ext.zoom.setLevel('week');\r\n    gantt.ext.zoom.attachEvent('onAfterZoom', function (level, config) {\r\n      document.querySelector(\r\n        \"input[value='\" + config.name + \"']\"\r\n      ).checked = true;\r\n    });\r\n\r\n    gantt.config.columns = [{ name: 'text', width: 56, resize: true }];\r\n\r\n    gantt.templates.task_class = (start, end, task) => task.task_class;\r\n\r\n    gantt.timeline_cell_class = function (task, date) {\r\n      return 'weekend';\r\n    };\r\n\r\n    gantt.templates.task_text = () => '';\r\n\r\n    window.gantt.init(chartEl);\r\n    window.gantt.parse({\r\n      data: [\r\n        {\r\n          id: 1,\r\n          text: 'Planning',\r\n          start_date: '2019-08-01 00:00',\r\n          duration: 3,\r\n          progress: 1,\r\n          task_class: 'planning'\r\n        },\r\n        {\r\n          id: 2,\r\n          text: 'Research',\r\n          start_date: '2019-08-02 00:00',\r\n          duration: 5,\r\n          // parent: 1,\r\n          progress: 0.5,\r\n          task_class: 'research'\r\n        },\r\n        {\r\n          id: 3,\r\n          text: 'Design',\r\n          start_date: '2019-08-02 00:00',\r\n          duration: 10,\r\n          // parent: 1,\r\n          progress: 0.4,\r\n          task_class: 'design'\r\n        },\r\n        {\r\n          id: 4,\r\n          text: 'Review',\r\n          start_date: '2019-08-05 00:00',\r\n          duration: 5,\r\n          // parent: 1,\r\n          progress: 0.8,\r\n          task_class: 'review'\r\n        },\r\n        {\r\n          id: 5,\r\n          text: 'Develop',\r\n          start_date: '2019-08-06 00:00',\r\n          duration: 10,\r\n          // parent: 1,\r\n          progress: 0.3,\r\n          open: true,\r\n          task_class: 'develop'\r\n        },\r\n        {\r\n          id: 6,\r\n          text: 'Review II',\r\n          start_date: '2019-08-10 00:00',\r\n          duration: 4,\r\n          // parent: 4,\r\n          progress: 0.02,\r\n          task_class: 'review-2'\r\n        }\r\n      ],\r\n      links: [\r\n        { id: 1, source: 1, target: 2, type: '0' },\r\n        { id: 2, source: 1, target: 3, type: '0' },\r\n        { id: 3, source: 3, target: 4, type: '0' },\r\n        { id: 4, source: 6, target: 5, type: '3' }\r\n      ]\r\n    });\r\n\r\n    const scaleRadios = zeroRoadMapEl.querySelectorAll('input[name=scaleView]');\r\n\r\n    const progressCheck = zeroRoadMapEl.querySelector('[data-gantt-progress]');\r\n    const linksCheck = zeroRoadMapEl.querySelector('[data-gantt-links]');\r\n\r\n    scaleRadios.forEach(item => {\r\n      item.addEventListener('click', e => {\r\n        window.gantt.ext.zoom.setLevel(e.target.value);\r\n      });\r\n    });\r\n\r\n    linksCheck.addEventListener('change', e => {\r\n      window.gantt.config.show_links = e.target.checked;\r\n      window.gantt.init(chartEl);\r\n    });\r\n\r\n    progressCheck.addEventListener('change', e => {\r\n      window.gantt.config.show_progress = e.target.checked;\r\n      window.gantt.init(chartEl);\r\n    });\r\n  }\r\n};\r\n\r\nexport default zeroRoadmapChartInit;\r\n", "import issuesDiscoveredChartInit from '../theme/charts/echarts/issues-discovered-chart';\r\nimport zeroBurnOutChartInit from '../theme/charts/echarts/zero-burnout-chart';\r\nimport zeroRoadmapChartInit from '../theme/charts/echarts/zero-rodamap-chart';\r\n\r\nconst { docReady } = window.phoenix.utils;\r\n\r\ndocReady(zeroRoadmapChartInit);\r\ndocReady(zeroBurnOutChartInit);\r\ndocReady(issuesDiscoveredChartInit);\r\n"], "names": [], "mappings": ";;;;;EAAA;EACA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC;AAC3B;EACA;EACO,MAAM,eAAe,GAAG;EAC/B,EAAE,KAAK;EACP,EAAE,WAAW;EACb,EAAE,iBAAiB;EACnB,EAAE,iBAAiB;EACnB,KAAK;EACL,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACvD,EAAE,MAAM,YAAY,GAAG,OAAO,IAAI;EAClC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,IAAI,IAAI;EACzC,MAAM,IAAI,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE;EACjD,QAAQ,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;EACvC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC;AACJ;EACA,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;EACxC;EACA,EAAE,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;AAC3D;EACA,EAAE,MAAM,oBAAoB,GAAG,QAAQ,CAAC,aAAa;EACrD,IAAI,yBAAyB;EAC7B,GAAG,CAAC;EACJ,EAAE,IAAI,oBAAoB,EAAE;EAC5B,IAAI,oBAAoB,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,MAAM;EAC1E,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;EACrB,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG;AACH;EACA,EAAE,MAAM,CAAC,MAAM;EACf,IAAI,KAAK,CAAC,MAAM,EAAE,CAAC;EACnB,IAAI,IAAI,iBAAiB,EAAE;EAC3B,MAAM,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACtC,KAAK;EACL,GAAG,CAAC,CAAC;EACL,EAAE,IAAI,iBAAiB,EAAE;EACzB,IAAI,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACpC,GAAG;AACH;EACA,EAAE,eAAe,CAAC,gBAAgB;EAClC,IAAI,cAAc;EAClB,IAAI,CAAC,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,KAAK;EACjC,MAAM,IAAI,OAAO,KAAK,cAAc,EAAE;EACtC,QAAQ,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,WAAW,CAAC,CAAC,CAAC;EAC1E,OAAO;EACP,MAAM,IAAI,iBAAiB,EAAE;EAC7B,QAAQ,YAAY,CAAC,iBAAiB,CAAC,CAAC;EACxC,OAAO;EACP,KAAK;EACL,GAAG,CAAC;EACJ,CAAC,CAAC;EACF;AACA;EACA,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;EACvE,IAAI,UAAU,EAAE;EAChB,EAAE,UAAU,CAAC,OAAO,CAAC,GAAG,IAAI;EAC5B,IAAI,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,CAAC,IAAI;EAC9C,MAAM,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC;EAC1B,MAAM,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC;EAC1B,MAAM,MAAM,EAAE,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC;EAC7C,MAAM,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;EAC/D,MAAM,MAAM,KAAK,GAAG,OAAO,EAAE,aAAa,CAAC,mBAAmB,CAAC,CAAC;EAChE,MAAM,IAAI,KAAK,EAAE;EACjB,QAAQ,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,EAAE,CAAC;EAC5C,OAAO;EACP,KAAK,CAAC,CAAC;EACP,GAAG,CAAC,CAAC;EACL,CAAC;AACD;EACO,MAAM,gBAAgB,GAAG,CAAC,MAAM,EAAE,aAAa,GAAG,QAAQ,KAAK;EACtE,EAAE,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;EACvB,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI;EACvB,IAAI,WAAW,IAAI,CAAC;AACpB,2FAA2F;AAC3F,UAAU,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC,KAAK;AACpD,SAAS;AACT,UAAU,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG;AAC7B,MAAM,OAAO,EAAE,CAAC,KAAK,KAAK,QAAQ,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,KAAK;AAC3D,KAAK;AACL;AACA,YAAY,CAAC,CAAC;EACd,GAAG,CAAC,CAAC;EACL,EAAE,OAAO,CAAC;AACV;AACA,cAAc;AACd,gBAAgB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,OAAO,EAAE;AAC3D,oBAAoB,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,aAAa,CAAC;AAC3E,oBAAoB,MAAM,CAAC,CAAC,CAAC,CAAC,SAAS;AACvC,eAAe;AACf;AACA,YAAY,EAAE,WAAW,CAAC;AAC1B,gBAAgB,CAAC,CAAC;EAClB,CAAC,CAAC;AACF;EACO,MAAM,qBAAqB,GAAG,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK;EAC/D;EACA,EAAE,IAAI,MAAM,CAAC,UAAU,IAAI,GAAG,EAAE;EAChC,IAAI,MAAM,aAAa,GAAG,GAAG,CAAC,YAAY,CAAC;EAC3C,IAAI,MAAM,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,EAAE,EAAE,CAAC;EACrD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC;EAC9D,IAAI,OAAO,GAAG,CAAC;EACf,GAAG;EACH,EAAE,OAAO,IAAI,CAAC;EACd,CAAC;;EC3GD;AACA;EACA;EACA;EACA;AACA;EACA,MAAM,yBAAyB,GAAG,MAAM;EACxC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EAClE,EAAE,MAAM,uBAAuB,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;AAChF;EACA,EAAE,IAAI,uBAAuB,EAAE;EAC/B,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,uBAAuB,EAAE,SAAS,CAAC,CAAC;EACpE,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;AAC/D;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,KAAK,EAAE;EACb,QAAQ,WAAW,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAC;EAClE,QAAQ,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC;EACxE,QAAQ,WAAW,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,QAAQ,CAAC,aAAa,CAAC,CAAC;EACtE,QAAQ,WAAW,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC;EACxE,QAAQ,QAAQ,CAAC,SAAS,CAAC;EAC3B,OAAO;EACP,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,QAAQ,EAAE,CAAC,GAAG,MAAM,KAAK,qBAAqB,CAAC,MAAM,CAAC;EAC9D,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,UAAU,EAAE,IAAI;EACtB,MAAM,mBAAmB,EAAE,KAAK;AAChC;EACA,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,sBAAsB;EACtC,UAAU,IAAI,EAAE,KAAK;EACrB,UAAU,MAAM,EAAE,CAAC,KAAK,EAAE,KAAK,CAAC;EAChC,UAAU,UAAU,EAAE,EAAE;EACxB,UAAU,iBAAiB,EAAE,KAAK;EAClC;EACA;EACA;EACA;AACA;EACA,UAAU,KAAK,EAAE;EACjB,YAAY,IAAI,EAAE,KAAK;EACvB,YAAY,QAAQ,EAAE,QAAQ;EAC9B,YAAY,SAAS,EAAE,qBAAqB;EAC5C,YAAY,IAAI,EAAE;EAClB,cAAc,CAAC,EAAE;EACjB,gBAAgB,QAAQ,EAAE,KAAK;EAC/B,gBAAgB,UAAU,EAAE,GAAG;EAC/B,gBAAgB,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EACjD,gBAAgB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;EACtC,eAAe;EACf,cAAc,CAAC,EAAE;EACjB,gBAAgB,QAAQ,EAAE,IAAI;EAC9B,gBAAgB,KAAK,EAAE,QAAQ,CAAC,gBAAgB,CAAC;EACjD,gBAAgB,UAAU,EAAE,GAAG;EAC/B,eAAe;EACf,aAAa;EACb,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,KAAK,EAAE;EACnB,cAAc,IAAI,EAAE,IAAI;EACxB,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,IAAI,EAAE;EAChB,YAAY,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;EACjD,YAAY,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,aAAa,EAAE;EAC9C,YAAY,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;EAC/C,YAAY,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,kBAAkB,EAAE;EACnD,YAAY,EAAE,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE;EACxC,WAAW;EACX,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,MAAM,EAAE,CAAC;EACjB,QAAQ,GAAG,EAAE,CAAC;EACd,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,YAAY,EAAE,KAAK;EAC3B,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECxFD;EACA;EACA;AACA;EACA,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACnE,EAAE,MAAM,mBAAmB,GAAG,QAAQ,CAAC,aAAa;EACpD,IAAI,4BAA4B;EAChC,GAAG,CAAC;AACJ;EACA,EAAE,IAAI,mBAAmB,EAAE;EAC3B,IAAI,MAAM,WAAW,GAAG,OAAO,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;EAChE,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;AAC3D;EACA,IAAI,MAAM,iBAAiB,GAAG,OAAO;EACrC,MAAM,OAAO,EAAE;EACf,QAAQ,OAAO,EAAE,MAAM;EACvB,QAAQ,eAAe,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,QAAQ,WAAW,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,QAAQ,SAAS,EAAE,MAAM,IAAI,gBAAgB,CAAC,MAAM,EAAE,cAAc,CAAC;EACrE,QAAQ,WAAW,EAAE;EACrB,UAAU,WAAW,EAAE;EACvB,YAAY,KAAK,EAAE,KAAK;EACxB,WAAW;EACX,SAAS;EACT,QAAQ,YAAY,EAAE,eAAe;EACrC,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ,MAAM,EAAE,IAAI;EACpB,QAAQ,IAAI,EAAE;EACd,UAAU;EACV,YAAY,IAAI,EAAE,MAAM;EACxB,YAAY,IAAI,EAAE,WAAW;EAC7B,WAAW;EACX,UAAU;EACV,YAAY,IAAI,EAAE,cAAc;EAChC,YAAY,IAAI,EAAE,WAAW;EAC7B,WAAW;EACX,UAAU;EACV,YAAY,IAAI,EAAE,aAAa;EAC/B,YAAY,IAAI,EAAE,WAAW;EAC7B,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE,EAAE;EACrB,QAAQ,UAAU,EAAE,CAAC;EACrB,QAAQ,OAAO,EAAE,EAAE;EACnB,QAAQ,aAAa,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACnD,QAAQ,mBAAmB,EAAE,CAAC;EAC9B,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC;EACvC,UAAU,UAAU,EAAE,GAAG;EACzB,UAAU,QAAQ,EAAE,EAAE;EACtB,UAAU,UAAU,EAAE,aAAa;EACnC,SAAS;EACT,OAAO;AACP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA;EACA,MAAM,KAAK,EAAE;EACb,QAAQ;EACR,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,QAAQ,EAAE;EACpB,YAAY,SAAS,EAAE;EACvB,cAAc,IAAI,EAAE,OAAO;EAC3B,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,aAAa;EACb,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC;EACzC,YAAY,SAAS,EAAE,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;EACjE,YAAY,QAAQ,EAAE,CAAC;EACvB,YAAY,KAAK,EAAE,MAAM;EACzB,YAAY,MAAM,EAAE,EAAE;EACtB,YAAY,QAAQ,EAAE,IAAI;EAC1B,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,MAAM,EAAE,EAAE;EACtB;EACA,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,QAAQ,EAAE,CAAC;EACvB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,cAAc,IAAI,EAAE,QAAQ;EAC5B,aAAa;EACb,WAAW;EACX,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,IAAI,EAAE,YAAY,CAAC,EAAE,CAAC;EAChC,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,QAAQ,EAAE;EACpB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,QAAQ,EAAE,CAAC;EACvB,YAAY,IAAI,EAAE,IAAI;EACtB,YAAY,SAAS,EAAE;EACvB,cAAc,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC7C,cAAc,IAAI,EAAE,OAAO;EAC3B,aAAa;EACb,WAAW;EACX,UAAU,WAAW,EAAE,KAAK;EAC5B,UAAU,IAAI,EAAE,YAAY,CAAC,EAAE,CAAC;EAChC,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,IAAI,EAAE,IAAI;EAClB,QAAQ,IAAI,EAAE,OAAO;EACrB,QAAQ,QAAQ,EAAE;EAClB,UAAU,SAAS,EAAE;EACrB,YAAY,IAAI,EAAE,OAAO;EACzB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC;EACvC,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,QAAQ,EAAE,IAAI;EACxB,UAAU,QAAQ,EAAE,CAAC;EACrB,SAAS;EACT,QAAQ,SAAS,EAAE;EACnB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,YAAY,IAAI,EAAE,OAAO;EACzB,WAAW;EACX,SAAS;EACT,QAAQ,QAAQ,EAAE;EAClB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,MAAM,EAAE,EAAE;EACpB,UAAU,cAAc,EAAE,IAAI;EAC9B,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,cAAc,CAAC;EAC3C,WAAW;EACX,SAAS;EACT;EACA,OAAO;EACP,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,WAAW;EAC3B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,MAAM,EAAE,MAAM;EACxB,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;EAC1E,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,CAAC;EACpB,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,eAAe,CAAC;EAC5C,YAAY,OAAO,EAAE,KAAK;EAC1B,WAAW;EACX,UAAU,OAAO,EAAE;EACnB,YAAY,IAAI,EAAE,KAAK;EACvB,WAAW;EACX,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,cAAc;EAC9B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClC,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,MAAM,CAAC;EACzC,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC;EACnC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAClC,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,aAAa;EAC7B,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,SAAS,CAAC;EAC5C,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,SAAS,CAAC;EACtC,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;EACtC,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,UAAU,EAAE,CAAC;EACvB,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,mBAAmB,CAAC;EAChD,YAAY,WAAW,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EACrD,YAAY,WAAW,EAAE,CAAC;EAC1B,WAAW;EACX,UAAU,SAAS,EAAE;EACrB,YAAY,KAAK,EAAE,QAAQ,CAAC,kBAAkB,CAAC;EAC/C,YAAY,IAAI,EAAE,QAAQ;EAC1B,WAAW;EACX,UAAU,MAAM,EAAE,QAAQ;EAC1B,UAAU,IAAI,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;EACvC,SAAS;EACT,OAAO;EACP,MAAM,IAAI,EAAE;EACZ,QAAQ,KAAK,EAAE,CAAC;EAChB,QAAQ,IAAI,EAAE,CAAC;EACf,QAAQ,MAAM,EAAE,KAAK;EACrB,QAAQ,GAAG,EAAE,EAAE;EACf,QAAQ,YAAY,EAAE,IAAI;EAC1B,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,eAAe,CAAC,KAAK,EAAE,WAAW,EAAE,iBAAiB,CAAC,CAAC;EAC3D,GAAG;EACH,CAAC;;ECxPD;EACA;EACA;AACA;EACA,MAAM,oBAAoB,GAAG,MAAM;EACnC,EAAE,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;EACpD,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC,qBAAqB,CAAC,CAAC;AACtE;EACA,EAAE,IAAI,aAAa,EAAE;EACrB,IAAI,MAAM,OAAO,GAAG,aAAa,CAAC,aAAa,CAAC,2BAA2B,CAAC,CAAC;AAC7E;EACA,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC;EACzB,MAAM,OAAO,EAAE,IAAI;EACnB,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,GAAG,gBAAgB,CAAC;EACvD,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,GAAG,CAAC,CAAC;EACzC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;EACxC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC;EACxC,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;EAC1C,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,GAAG,KAAK,CAAC;EAC9C,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;EAC5C,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;EAC3C,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,mBAAmB,GAAG,KAAK,CAAC;EACpD,IAAI,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,KAAK,CAAC;AAC3C;EACA,IAAI,IAAI,gBAAgB,CAAC,cAAc,CAAC,EAAE;EAC1C,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC;EACrC,KAAK;AACL;EACA,IAAI,MAAM,UAAU,GAAG;EACvB,MAAM,MAAM,EAAE;EACd,QAAQ;EACR,UAAU,IAAI,EAAE,OAAO;EACvB,UAAU,MAAM,EAAE;EAClB,YAAY,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE;EAC/C,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE;EAChD,WAAW;EACX,SAAS;AACT;EACA,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;EAC3D,SAAS;EACT,QAAQ;EACR,UAAU,IAAI,EAAE,MAAM;EACtB,UAAU,MAAM,EAAE;EAClB,YAAY;EACZ,cAAc,IAAI,EAAE,MAAM;EAC1B,cAAc,IAAI,EAAE,CAAC;EACrB,cAAc,MAAM,EAAE,IAAI,IAAI;EAC9B,gBAAgB,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;EACzE,gBAAgB,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;EACvE,gBAAgB,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC;EAC1E,gBAAgB;EAChB,kBAAkB,GAAG;EACrB,kBAAkB,OAAO;EACzB,kBAAkB,IAAI;EACtB,kBAAkB,SAAS,CAAC,IAAI,CAAC;EACjC,kBAAkB,KAAK;EACvB,kBAAkB,SAAS,CAAC,OAAO,CAAC;EACpC,kBAAkB;EAClB,eAAe;EACf,aAAa;EACb,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;EACrD,WAAW;EACX,SAAS;EACT,OAAO;EACP,KAAK,CAAC;AACN;EACA,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;EACpC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;EACpC,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,aAAa,EAAE,UAAU,KAAK,EAAE,MAAM,EAAE;EACvE,MAAM,QAAQ,CAAC,aAAa;EAC5B,QAAQ,eAAe,GAAG,MAAM,CAAC,IAAI,GAAG,IAAI;EAC5C,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;EACvB,KAAK,CAAC,CAAC;AACP;EACA,IAAI,KAAK,CAAC,MAAM,CAAC,OAAO,GAAG,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;AACvE;EACA,IAAI,KAAK,CAAC,SAAS,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,GAAG,EAAE,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC;AACvE;EACA,IAAI,KAAK,CAAC,mBAAmB,GAAG,UAAU,IAAI,EAAE,IAAI,EAAE;EACtD,MAAM,OAAO,SAAS,CAAC;EACvB,KAAK,CAAC;AACN;EACA,IAAI,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,EAAE,CAAC;AACzC;EACA,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EAC/B,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;EACvB,MAAM,IAAI,EAAE;EACZ,QAAQ;EACR,UAAU,EAAE,EAAE,CAAC;EACf,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,UAAU,EAAE,kBAAkB;EACxC,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,QAAQ,EAAE,CAAC;EACrB,UAAU,UAAU,EAAE,UAAU;EAChC,SAAS;EACT,QAAQ;EACR,UAAU,EAAE,EAAE,CAAC;EACf,UAAU,IAAI,EAAE,UAAU;EAC1B,UAAU,UAAU,EAAE,kBAAkB;EACxC,UAAU,QAAQ,EAAE,CAAC;EACrB;EACA,UAAU,QAAQ,EAAE,GAAG;EACvB,UAAU,UAAU,EAAE,UAAU;EAChC,SAAS;EACT,QAAQ;EACR,UAAU,EAAE,EAAE,CAAC;EACf,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,UAAU,EAAE,kBAAkB;EACxC,UAAU,QAAQ,EAAE,EAAE;EACtB;EACA,UAAU,QAAQ,EAAE,GAAG;EACvB,UAAU,UAAU,EAAE,QAAQ;EAC9B,SAAS;EACT,QAAQ;EACR,UAAU,EAAE,EAAE,CAAC;EACf,UAAU,IAAI,EAAE,QAAQ;EACxB,UAAU,UAAU,EAAE,kBAAkB;EACxC,UAAU,QAAQ,EAAE,CAAC;EACrB;EACA,UAAU,QAAQ,EAAE,GAAG;EACvB,UAAU,UAAU,EAAE,QAAQ;EAC9B,SAAS;EACT,QAAQ;EACR,UAAU,EAAE,EAAE,CAAC;EACf,UAAU,IAAI,EAAE,SAAS;EACzB,UAAU,UAAU,EAAE,kBAAkB;EACxC,UAAU,QAAQ,EAAE,EAAE;EACtB;EACA,UAAU,QAAQ,EAAE,GAAG;EACvB,UAAU,IAAI,EAAE,IAAI;EACpB,UAAU,UAAU,EAAE,SAAS;EAC/B,SAAS;EACT,QAAQ;EACR,UAAU,EAAE,EAAE,CAAC;EACf,UAAU,IAAI,EAAE,WAAW;EAC3B,UAAU,UAAU,EAAE,kBAAkB;EACxC,UAAU,QAAQ,EAAE,CAAC;EACrB;EACA,UAAU,QAAQ,EAAE,IAAI;EACxB,UAAU,UAAU,EAAE,UAAU;EAChC,SAAS;EACT,OAAO;EACP,MAAM,KAAK,EAAE;EACb,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE;EAClD,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE;EAClD,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE;EAClD,QAAQ,EAAE,EAAE,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,GAAG,EAAE;EAClD,OAAO;EACP,KAAK,CAAC,CAAC;AACP;EACA,IAAI,MAAM,WAAW,GAAG,aAAa,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;AAChF;EACA,IAAI,MAAM,aAAa,GAAG,aAAa,CAAC,aAAa,CAAC,uBAAuB,CAAC,CAAC;EAC/E,IAAI,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAC;AACzE;EACA,IAAI,WAAW,CAAC,OAAO,CAAC,IAAI,IAAI;EAChC,MAAM,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,IAAI;EAC1C,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;EACvD,OAAO,CAAC,CAAC;EACT,KAAK,CAAC,CAAC;AACP;EACA,IAAI,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,IAAI;EAC/C,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;EACxD,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACjC,KAAK,CAAC,CAAC;AACP;EACA,IAAI,aAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,IAAI;EAClD,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;EAC3D,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;EACjC,KAAK,CAAC,CAAC;EACP,GAAG;EACH,CAAC;;EC3KD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1C;EACA,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC/B,QAAQ,CAAC,oBAAoB,CAAC,CAAC;EAC/B,QAAQ,CAAC,yBAAyB,CAAC;;;;;;"}