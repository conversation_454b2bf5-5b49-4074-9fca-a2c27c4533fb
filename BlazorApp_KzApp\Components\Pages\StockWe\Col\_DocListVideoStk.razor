@using <PERSON><PERSON><PERSON>
@using BlazorApp_KzApp.Components.Pages.StockWe.Col.DocItem
@inject IJSRuntime JS
@inject IDialogService DialogService
@inject NavigationManager NavManagerObj

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    public eZeroCore.Web.Document.RoleEnumForDoc RoleEnum { get; set; } = eZeroCore.Web.Document.RoleEnumForDoc.Website;

    [Parameter]
    public int PageSize { get; set; } = 36;

    [Parameter]
    public bool AllowPaging { get; set; } = true;

    /// <summary>
    /// SubVideoList Not Search
    /// </summary>
    [Parameter]
    public bool AllowSearch { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public eZeroCore.Web.Column ColObj { get; set; } = new();

    public string MainDocId { get; set; } = "";
    string ColumnId { get; set; } = "";
    string sSearchValue { get; set; } = "";
    string PageTitle = "";

    string sParentDocId = "";
    string sSubDocID = "";
    //bool videoStatus { get; set; }
    protected override async Task OnInitializedAsync()
    {
        PageTitle = ColObj.ColumnName;
        ColumnId = ColObj.ColumnId;
        if (string.IsNullOrEmpty(ColumnId))
        {
            return;
        }
        else
        {
            await Init();
            eZeroCore.Web.Column.PageViewAddOn(ColObj.ColumnId);
        }
    }
    List<eZeroCore.Web.Document> Datas = new();
    async Task Init()
    {
        eZeroCore.Web.DocList docList = new()
            {
                PageSize = PageSize,
                RoleEnum = RoleEnum,
                HiddenVisable = CObj.IsBMS,
                Search = sSearchValue,
                ColumnIds = [ColumnId],
                OrderBy = eZeroCore.Web.DocList.OrderByEnum.Followorder,
                SortOrderRule = Microsoft.Data.SqlClient.SortOrder.Ascending
                //OrderById = "docid='dciod8b03072'"
            };
        Datas = await docList.ListAsync();
    }
    public async Task Refresh(string value = "")
    {
        sSearchValue = value.ToUpper();
        //await JS.InvokeVoidAsync("console.log", $"OnSearch:【{sSearchValue}】");
        await Init();
        StateHasChanged();
        await Task.Delay(0);
    }

    async Task ClickTag(string tag)
    {
        if (AllowSearch)
        {
            await Refresh(tag);
        }
    }

    [Parameter]
    public string HeaderClass { get; set; } = "";

    async Task OnOrder()
    {
        #region ...
        var parameters = new DialogParameters<string>{
            { "Docs", Datas }
        };
        var dialog = await DialogService.ShowAsync<_OrderDocItems_DialogWinodw>(CObj.UserLang.GetLangValue("排序"), parameters, MudUI.GetOptions());
        var result = await dialog.Result;
        if (result is not null && !result.Canceled)
        {
            await Init();
            //await JS.InvokeVoidAsync("console.log", $"{result.Data}");
        }
        else
        {
            //await JS.InvokeVoidAsync("console.log", $"Cancel");
        }
        #endregion
        await Task.CompletedTask;
    }

}
@if (AllowSearch)
{
    <SearchBox OnSearch="@Refresh" InputValue="@sSearchValue" Placeholder="@($"Search {PageTitle}")" />
}

@if (ChildContent is not null)
{
    <div class="card-header pt-1 pb-2 px-2 @HeaderClass">
        @ChildContent
    </div>
}

<div class="position-relative">
    @if(CObj.UserIsAdmin)
    {
        <Ez_Btn OnBtnClick="@OnOrder" ClassName="btn btn-phoenix-primary position-absolute top-0 end-0 z-5 m-3">
            <i class="fa-solid fa-sort me-1"></i>
            @CObj.UserLang.GetLangValue("排序")
        </Ez_Btn>
    }
    @foreach (eZeroCore.Web.Document doc in Datas)
    {
        <DocCard @key=doc.DocId TagsVisible="AllowSearch" DocObj="@doc" OnClickTag="async (tag)=>{ await ClickTag(tag);}" CompactMode="true" />
    }
</div>

<div class="card shadow-none border py-2 d-none"></div>