﻿

@code {    

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public bool Visible { get; set; } = true;

    public const string DomID = "navbarVerticalCollapse";
}
 @*
     /lib/Ph117/public/assets/
     d-lg-none
 *@
<nav class="navbar navbar-vertical navbar-expand-lg PageLoading @(Visible?"":"d-none")">
    <div class="collapse navbar-collapse" id="@DomID">
        <!-- scrollbar removed-->
        <div class="navbar-vertical-content">
            @*额外添加 class：bg-body-emphasis 2024-10-01*@
            <ul class="navbar-nav flex-column bg-body-emphasis" id="navbarVerticalNav">
                @ChildContent
            </ul>

        </div>
    </div>
    <div class="navbar-vertical-footer">
        <button class="btn navbar-vertical-toggle border-0 fw-semibold w-100 white-space-nowrap d-flex align-items-center" onclick="Blz.NavbarCollapseSet()">
            <span class="uil uil-left-arrow-to-left fs-8"></span>
            <span class="uil uil-arrow-from-right fs-8"></span>
            <span class="navbar-vertical-footer-text ms-2">
                折叠侧边栏
            </span>
        </button>
    </div>
</nav>