@using BlazorApp_KzApp.Components.PageUI
@using System.Text
@using System.Globalization
@using eZeroCore.Web.Stk
@using System.Text.Json
@using System.Text.Json.Serialization
@using eZeroCore.Users
@using <PERSON><PERSON><PERSON>

@inject IJSRuntime JS
@inject IDialogService DialogService
@* @inject VisitLimitService VisitService *@

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }
    string DomId = $"Stock_Spotligh_{nameof(_BlockDarkPoolDayMinute)}";

    [Parameter]
    public EventCallback<string> OnSearchStock { get; set; }

    [CascadingParameter]
    public string DefaultStock { get; set; } = "";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await BuildChart();
        }
        //base.OnAfterRender(firstRender);
    }

    protected override Task OnInitializedAsync()
    {
        // VisitService.Limit = 3;
        return base.OnInitializedAsync();
    }


    string _SearchString = "";
    string SearchString
    {
        get
        {
            if (string.IsNullOrWhiteSpace(_SearchString))
            {
                return DefaultStock.ToUpper();
            }
            return _SearchString;
        }
        set
        {
            _SearchString = value;
            OnSearchStock.InvokeAsync(_SearchString);
        }
    }

    bool CheckUser()
    {
        if (!CObj.IsLogin)
        {
            _ = WindowTipInfo();
            return false;
        }
        // 临时移除VisitService相关代码
        // else if (!eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj.UserId)) && !VisitService.CanVisit(CObj.IpAddress))
        else if (!eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj.UserId)))
        {
            _ = WindowTipInfo();
            return false;
        }
        return true;
    }
    async Task WindowTipInfo()
    {
        var parameters = new DialogParameters<string>();
        var dialog = await DialogService.ShowAsync<_TipWindow>("", parameters, MudUI.GetOptions(blurBg: false));
        //var result = await dialog.Result;

        /*
        if (result is not null && !result.Canceled)
        {

        }
        if (VisitService.CanVisit(CObj.IpAddress))
            {
            await JS.InvokeVoidAsync("console.log", $"【Allow:{CObj.IpAddress}】");
            }
            else
            {
            await JS.InvokeVoidAsync("console.log", $"【Not Allow:{CObj.IpAddress}】");
    }
    */
    }

    string sJson = "";
    bool isDaily = true;
    async Task BuildChart()
    {
        #region...
        await Generic.LoadingDom(JS, DomId);
        sJson = await eZeroCore.Web.Stk.Data.ChartGeneric.GetBlockDarkPool_Day_Minute(isDaily, SearchString);
        await JS.InvokeVoidAsync($"BlockDarkPool_Day_Minute.init", DomId, sJson, isDaily);
        //StateHasChanged();
        await Generic.LoadingDom(JS, DomId, false);
        #endregion
    }
    async Task OnSearch(string value)
    {
        if (CheckUser())
        {
            SearchString = value.ToUpper();
        }
        else
        {
            SearchString = DefaultStock;
        }
        await BuildChart();
        await Task.Delay(0);
    }
    RzSearchInputBox inputBox;
    async Task SearchButtonClick()
    {
        await inputBox.HandleSearchKeyDown();
    }
    int SelectedChartIndex = 0;
    async Task SwitchChart(int index = 0)
    {
        if (!CObj.IsLogin)
        {
            WindowTipInfo();
            return;
        }
        SelectedChartIndex = index;
        isDaily = index == 0;
        await BuildChart();
        await Task.Delay(0);
    }

    string className = "d-xl-flex justify-content-between align-items-center mt-3";
}

<_UIFramework FullWidth="true" HeaderContentClass="@className">
    <HeaderContent>

        <div class="py-2 text-center">
            <b>
                机构主力资金买卖
            </b>
            <span class="badge badge-phoenix badge-phoenix-info py-1">
                @SearchString
            </span>
        </div>
        <div class="py-2 text-center">
            <div class="btn-group">
                <button @onclick="() => SwitchChart(0)" type=" button"
                        class="btn fs-9 py-1 mb-0 @(SelectedChartIndex == 0 ? "btn-warning" : "btn-phoenix-warning")">
                    每日
                </button>
                <button @onclick="() => SwitchChart(1)" type="button"
                        class="btn fs-9 py-1 mb-0 @(SelectedChartIndex == 1 ? "btn-warning" : "btn-phoenix-warning")">
                    每分钟
                </button>
            </div>
        </div>

        <MudHidden Breakpoint="Breakpoint.MdAndUp" Invert=true>
            <div class="py-2 text-center">
                <RzSearchInputBox @ref=inputBox OnSearch="@OnSearch" Placeholder="搜索代号" DefaultStock="@DefaultStock" />
            </div>
        </MudHidden>

        <MudHidden Breakpoint="Breakpoint.MdAndUp">
            <div class="py-2 text-center">
                <RzSearchInputBox @ref=inputBox OnSearch="@OnSearch" Placeholder="搜索代号" DefaultStock="@DefaultStock" />
                <_SearchButton Text="搜索" OnClick="@SearchButtonClick" />
            </div>
        </MudHidden>

    </HeaderContent>
    <ChildContent>

        <div id="@DomId" style="width: 100%; height:360px;" class="eChartDom"></div>

    </ChildContent>
</_UIFramework>






