@inject IJSRuntime JS
@inject NavigationManager NavManagerObj
@using System.Globalization
@using AspNetCoreWeb.Blazor.CityV3.UI.RzDataGrid
@using ZycCore.Models
@using ZycCore.Models.Dto
@using <PERSON><PERSON><PERSON>
@code {

    [CascadingParameter]
    public ComponentPassObj CObj { get; set; } = new();
    [Parameter]
    public int PageSize { get; set; } = 18;
    [Parameter]
    public bool AllowPaging { get; set; } = true;
    [Parameter]
    public EventCallback<string> OnSelectedStock { get; set; }

    Settings settings = new();

    string CssCard = "card m-1 mb-2 border d-flex flex-row align-items-center";
    string CssHeader = "flex-shrink-0 w-90px text-end me-2 py-3";
    string CssBody = "card-body flex-grow-1 bg-dark-subtle border-start p-0 px-1";

    public decimal? MaxPrice { get; set; } = null;
    public decimal? MinPrice { get; set; }

    public RapidStockMovementsDto dto { get; set; } = new();

    public DateTime? DateStart { get; set; } = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date;
    public DateTime? DateEnd { get; set; } = eZeroCore.Web.Stk.WebDefaultValue.GetLastOpenMarketDateTime().Date;

    RzDataTable<RapidStockMovements.Data> rzDataTable;
    List<RapidStockMovements.Data> Datas = [];
    bool isLoading = false;
    int TotalCount { get; set; }
    int lastPageNumber { get; set; }
    // 选中的类型
    ModelItem SelectedModel { get; set; }
    eZeroCore.Users.User CObjUser;

    protected override async Task OnInitializedAsync()
    {
        await Init();
    }

    public async Task Init(bool onSelect = false)
    {
        await Generic.Loading(JS);
        if (SelectedModel is null)
        {
            SelectedModel = types.FirstOrDefault() ?? new();
        }
        CObjUser = new(CObj.UserId);
        await LoadData();
        if (onSelect)
        {
            try
            {
                await SelectFirstStock();
            }
            catch { }
        }
        await Generic.Loading(JS, false);
        _MuteNotificationId = CObjUser.Settings.MutedGroupIds.Contains(ColumnId.ToLower());
    }

    async Task LoadData(LoadDataArgs? args = null)
    {
        isLoading = true;
        try
        {
            dto.DateStart = DateStart;
            dto.DateEnd = DateEnd;
            dto.MinPrice = MinPrice;
            dto.MaxPrice = MaxPrice;
            dto.Model = (int?)SelectedModel?.ModelType;

            //DateTime dateTimeStart = WebDefaultValue.GetLastOpenMarketDateTime(useCache: true, timeout: 300);

            RapidStockMovements.List list = new() { };

            Datas = await list.DataTableListAsync(args, PageSize, dto);

            TotalCount = list.TotalCount;

            if (lastPageNumber != list.SetPageNumber)
            {
                lastPageNumber = list.SetPageNumber;
                await SelectFirstStock();
            }
            // 确保刷新数据时总是选择第一条记录
            else if (args == null && Datas.Count > 0)
            {
                await SelectFirstStock();
            }
        }
        catch (Exception ex)
        {
            await JS.InvokeVoidAsync("console.error", $"LoadData Exception: {ex.Message}");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    async Task SelectFirstStock()
    {
        try
        {
            await ClickSelectStock(Datas.FirstOrDefault()?.Stock?.ToUpper() ?? "");
        }
        catch { }
    }

    async Task ClickSelectStock(string stock)
    {
        if (!SelectedStock.Equals(stock))
        {
            SelectedStock = stock;
            if (rzDataTable is not null)
            {
                await rzDataTable.SetSelectedItem(Datas.Find(d => d.Stock.Equals(SelectedStock, StringComparison.OrdinalIgnoreCase)));
            }
            await OnSelectedStock.InvokeAsync(SelectedStock);
        }
    }

    public string SelectedStock { get; set; } = "";

    async Task OnSelected(RapidStockMovements.Data? data)
    {
        if (data is not null)
        {
            if (AppSettings.IsTestModeAll)
            {
                await JS.InvokeVoidAsync("console.log", $"OnSelected:【{data.Stock}】");
            }
            await OnSelectedStock.InvokeAsync(data.Stock?.ToUpper());
        }
        await Task.CompletedTask;
    }

    // 行情类型枚举
    public enum ModelType
    {
        RapidRise = 20,  // 急速上涨
        RapidFall = 21   // 急速下跌
    }

    // 类型数据
    List<ModelItem> types = new()
    {
        new ModelItem { Name = "急速上涨", SubTitle = "", ModelType = ModelType.RapidRise, Color="text-warning-dark" },
        new ModelItem { Name = "急速下跌", SubTitle = "", ModelType = ModelType.RapidFall, Color="text-warning-dark" }

    };

    // 实体类型模型
    public class ModelItem
    {
        public string Name { get; set; } = "";
        public string SubTitle { get; set; } = "";
        public ModelType ModelType { get; set; }
        public string Color { get; set; } = "";

        public string GetLocalizedName(ComponentPassObj cObj)
        {
            return cObj.UserLang.GetLangValue(Name);
        }
    }

    // 选中类型事件
    async Task OnSelectedModel(ModelItem model)
    {
        SelectedModel = model;
        await Task.Delay(0);
        await Init(true);
    }

    DateRange DateRange
    {
        get => new(DateStart, DateEnd);
        set
        {
            if (value != null)
            {
                DateStart = value.Start;
                DateEnd = value.End;
                StateHasChanged(); // 通知UI更新
                _ = LoadData();    // 加这一行，异步刷新数据
            }
        }
    }

    // 新增string属性用于绑定
    public string MaxPriceStr
    {
        get => MaxPrice?.ToString() ?? "";
        set
        {
            if (string.IsNullOrWhiteSpace(value))
                MaxPrice = null;
            else if (decimal.TryParse(value, out var v))
                MaxPrice = v;
        }
    }
    public string MinPriceStr
    {
        get => MinPrice?.ToString() ?? "";
        set
        {
            if (string.IsNullOrWhiteSpace(value))
                MinPrice = null;
            else if (decimal.TryParse(value, out var v))
                MinPrice = v;
        }
    }

}

<!-- 下拉框 -->
<div class="rz-form-field-content px-2 p-1 mb-2 w-100 Selector">
    <div class="dropdown w-100">
        <div class="dropdown-toggle w-100 hand" data-bs-toggle="dropdown" aria-expanded="false">
            <span class="dropdownMenusModalName @SelectedModel?.Color">
                @SelectedModel?.GetLocalizedName(CObj)
            </span>
            <b class="golden-text ms-2">
                @SelectedModel?.SubTitle
            </b>
        </div>
        <ul class="dropdown-menu">
            @foreach (ModelItem md in types)
            {
                <li @key=md.GetHashCode() style="width:100%">
                    <button @onclick="async () => { await OnSelectedModel(md); }"
                            class="dropdown-item @md.Color" type="button">
                        <span class="dropdownMenusModalName">
                            @md.GetLocalizedName(CObj)
                        </span>
                        <b class="golden-text ms-2">
                            @md.SubTitle
                        </b>
                    </button>
                </li>
            }
        </ul>
    </div>
</div>


<!-- 日期选择器 -->
<div class="d-flex justify-content-end mb-3">
    <div style="font:bold;margin-top:10px;width:100%">
        <MudDateRangePicker Culture="@CultureInfo.GetCultureInfo("zh-Hans")"
                            PickerVariant="PickerVariant.Dialog"
                            ShowToolbar="false"
                            TitleDateFormat="yyyy,MM,dd"
                            FirstDayOfWeek="DayOfWeek.Sunday"
                            Clearable="false"
                            DateFormat="MM/dd/yy"
                            Label="@CObj.UserLang.GetLangValue("选择日期")"
                            @bind-DateRange="DateRange" />
    </div>
</div>


<div class="card">
    <div class="card-body p-1 position-relative">
        <div class="position-relative d-none">
            <hr class="bg-body-secondary mb-4">
            <div class="divider-content-center">
                <i class="fal fa-filter me-1"></i>
                @CObj.UserLang.GetLangValue("过滤器")
            </div>
        </div>
        <div class="input-group mb-3">
            <span class="input-group-text">
                @CObj.UserLang.GetLangValue("股票价格范围"):
            </span>
            <input type="text" @bind-value=@MinPriceStr class="form-control" placeholder="@CObj.UserLang.GetLangValue("最小值")" />
            <span class="input-group-text">
                ~
            </span>
            <input type="text" @bind-value=@MaxPriceStr class="form-control" placeholder="@CObj.UserLang.GetLangValue("最大值")" />
        </div>
        <div class="d-grid gap-2 mb-2 position-absolute top-0 end-0" style="margin-top: -7.8rem;">
            <Ez_Btn OnBtnClick="async () => { await LoadData(); }" ClassName="btn btn-phoenix-success btn-sm me-1 mb-1">
                @CObj.UserLang.GetLangValue("应用过滤")
            </Ez_Btn>
        </div>
    </div>
</div>

<!-- 表格 -->
<RzDataTable @ref=rzDataTable TItem="RapidStockMovements.Data"
             PageNumbersCount="3"
             OverflowXScroll="true"
             TheadVisable="true"
             AllowRowSelectOnRowClick=true
             ShowPagingSummary="false"
             PagerAlignCenter="true"
             AllowPaging="@AllowPaging"
             Slim="true"
             PageArgsEnabled="true"
             Data="@Datas" PageSize="@PageSize"
             IsLoading="@isLoading"
             OnSelected="@OnSelected"
             Count="@TotalCount" LoadData="@LoadData">

    <RadzenDataGridColumn TItem="RapidStockMovements.Data" Sortable="false" Title="@CObj.UserLang.GetLangValue("扫描时间(美东)")"
                          CssClass="hand" Property="CreateDate" Frozen="true" Width="102px">
        <Template Context="data">
            @data.CreateDate?.ToString("MM/dd HH:mm")
        </Template>
    </RadzenDataGridColumn>
    <RadzenDataGridColumn TItem="RapidStockMovements.Data" Sortable="false" Title="@CObj.UserLang.GetLangValue("代号")"
                          CssClass="hand" Property="Stock" Frozen="true" Width="102px">
        <Template Context="data">
            <div class="badge badge-phoenix fs-8 badge-phoenix-warning my-1" style="width:80px;">
                @data.Stock
            </div>
        </Template>
    </RadzenDataGridColumn>
    <RadzenDataGridColumn TItem="RapidStockMovements.Data" Sortable="false" Title="@CObj.UserLang.GetLangValue("评分")" Width="78px"
                          CssClass="hand" Property="Score">
        <Template Context="data">
            <div class="text-warning-light fs-8 fw-bold">
                @data.Score
            </div>
        </Template>
    </RadzenDataGridColumn>
    <RadzenDataGridColumn TItem="RapidStockMovements.Data" Sortable="false" Title="@CObj.UserLang.GetLangValue("股价")" Width="88px"
                          CssClass="hand" Property="Price">
        <Template Context="data">
            <div class="text-warning-info fw-bold">
                @data.Price
            </div>
        </Template>
    </RadzenDataGridColumn>
</RzDataTable>


@if (!CObj.IsLogin)
{
    <div>
        <h5 style="margin-top: 280px;" class="position-absolute top-0 start-50 translate-middle-x z-3 text-center">
            <i class="fa-solid fa-lock fa-2x text-warning-light"></i>
            <br />
            <a style="width:300px;" href="@eZeroCore.Settings.GetLoginOrRegUrl(false)" class="text-body-emphasis d-block">
                @CObj.UserLang.GetLangValue("免费注册后，立即解锁全部")
            </a>
        </h5>
        <div class="blur-overlay" style="height: 160px; top: 240px;"></div>
    </div>
}

<_FrontEnd_RowFramework AddClass="ps-0 mt-2 mb-3" TitleClass="fs-12">
    <TitleContent>        
        <i class="fas fa-bell-on me-1"></i>
        @CObj.UserLang.GetLangValue("关闭通知")
    </TitleContent>
    <ChildContent>
        <MudSwitch Class="@($"ms-2 {ColumnId}")" @bind-Value="@MuteNotificationId" Size="Size.Medium" Color="Color.Success" />
    </ChildContent>
</_FrontEnd_RowFramework>

<div class="mt-3">
    @(new MarkupString(eZeroCore.Web.Column.GetColumnContent(ColumnId)!.Replace("<br/>", "")))
</div>

@code {

    string ColumnId { get; set; } = $"{"COL8A2C9A5AF9".ToLower()}"; // 用于列ID
    bool _MuteNotificationId = false;
    bool MuteNotificationId
    {
        get { return _MuteNotificationId; }
        set
        {
            if(value)
            {
                CObjUser.Settings.MutedGroupIds.Add(ColumnId.ToLower());                
            }
            else
            {
                CObjUser.Settings.MutedGroupIds.Remove(ColumnId.ToLower());
            }
            _ = CObjUser.SaveAsync();
            _MuteNotificationId = value;
        }
    }
}