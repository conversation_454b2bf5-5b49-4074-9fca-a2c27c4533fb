@using BlazorApp_KzApp.Components.Pages.ChatGroupMessages
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.Pages.FrontEnd
@using eZeroCore.Web.Stk.Data
@using System.Globalization
@using System.Text.Json
@using System.Text.Json.Serialization
@inject IJSRuntime JS

@page "/sector/{param?}"

@code {
    [Parameter]
    public string param { get; set; } = "";

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    List<Stock_Sector> lists = [];

    string DomId = $"Chart{nameof(Stock_Sector)}";

    eZeroCore.Web.Column col = new("COLA02C5E8A5D");

    protected override async Task OnInitializedAsync()
    {
        _DataValue = eZeroCore.Web.Stk.WebDefaultValue.GetStock_QuantLastOpenMarketDateTime().Date;
        if (timestamp is not null)
        {
            await timestamp.TimerTaskEvent();
        }
    }

    int min = 0;
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await init();
        }
    }

    async Task init()
    {
        #region...
        await timestamp.TimerTaskEvent();
        if (DateValue is not null)
        {
            eZeroCore.Web.Stk.Data.Stock_Sector.DataList dataList = new()
                {
                    StartDateTime = DateValue.Value
                };
            lists = await dataList.ListAsync();
        }
        await Generic.JsConsole(JS, $"DateValue:{DateValue}");
        await JS.InvokeVoidAsync($"Stock_SectorBar.generateBarChart", JsonSerializer.Serialize(lists), DomId);
        #endregion
    }

    async Task OnSendTickSecEvent()
    {
        min = DateTime.Now.Minute;
        if (CObj.IsKyrison)
        {
            await JS.InvokeVoidAsync("console.log", $"Stock_Sector:【{min}】");
        }
        if (min == 30)
        {
            await init();
        }
        await Task.CompletedTask;
    }
    string className = "d-xl-flex justify-content-between align-items-center";
    _Timestamp timestamp;

    DateTime? _DataValue;
    public DateTime? DateValue
    {
        get => _DataValue;
        set
        {
            if (value == null)
            {
                return;
            }
            _DataValue = value;
            _ = init();
        }
    }
}

<Main_Framework ContentClass="mb-0">

    <SubscriptionV3 PaymentVisable="@(!eZeroCore.Web.Stk.UserHelper.CheckIsPayingUsers(new eZeroCore.Users.User(CObj.UserId)))">

        <_UIFramework FullWidth="true" HeaderContentClass="@className">

            <HeaderContent>
                <div class="py-2 text-center">
                    <i class="fa-solid fa-layer-group me-2"></i>
                    @CObj.UserLang.GetLangValue(col.ColumnName)
                    @CObj.UserLang.GetLangValue(col.ColumnContentSummary)
                    <_Timestamp AddString="@CObj.UserLang.GetLangValue("最后刷新时间：")" Inline="true" ClassName="fs-9 d-none d-md-block" @ref=timestamp />
                </div>

                <div>
                    <MudDatePicker Culture="@CultureInfo.GetCultureInfo("zh-Hans")"
                                   PickerVariant="PickerVariant.Dialog"
                                   Placeholder="@CObj.UserLang.GetLangValue("选择日期")"
                                   ShowToolbar="false"
                                   TitleDateFormat="yyyy,MM,dd"
                                   FirstDayOfWeek="DayOfWeek.Sunday"
                                   DateFormat="MM/dd/yy"
                                   @bind-Date="DateValue" />
                </div>
            </HeaderContent>
            <ChildContent>
                <div id="@DomId" style="width: 100%; height: 1280px;" class="eChartDom"></div>
            </ChildContent>

        </_UIFramework>

        <ChatGroupMsgOffcanvas RedirectUrl="/chat" OnSendTickMin="OnSendTickSecEvent" />

    </SubscriptionV3>

    <_FooterMenus />

    <_FooterV2 IsContentPage="true" MobileFooterMenus="true" />
</Main_Framework>
