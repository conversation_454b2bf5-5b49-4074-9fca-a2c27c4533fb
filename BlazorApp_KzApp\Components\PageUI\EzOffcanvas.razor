@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    string EzOffcanvasDomId { get; set; } = Guid.NewGuid().ToString();

    [Parameter]
    public RenderFragment? ChildContent { get; set; }

    [Parameter]
    public string Title { get; set; } = "";

    [Parameter]
    public string ButtonTitle { get; set; } = "";

    [Parameter]
    public bool HeaderVisable { get; set; } = true;

    [Parameter]
    public string CssClass { get; set; } = "";

    [Parameter]
    public bool Overflow_YHidden { get; set; }

    [Parameter]
    public bool HideWithMobile { get; set; } = true;

    [Parameter]
    public string RedirectUrl { get; set; } = "";

    [Parameter]
    public string? Icon { get; set; } 

    string _Icon = "fa-solid fa-comment-dollar fa-lg";

    protected override Task OnInitializedAsync()
    {
        if(Icon is not null)
        {
            _Icon = Icon;
        }
        return base.OnInitializedAsync();
    }

    /*
    [Parameter]
    public string UseUrl { get; set; } = "";
        */

    bool Visable { get; set; }
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JS.InvokeVoidAsync("chatGroupMessage.initializeOffcanvas", EzOffcanvasDomId);
            Visable = true;
            StateHasChanged();
        }
    }
    public async Task ShowOffcanvas(bool show = true)
    {
        if (string.IsNullOrEmpty(RedirectUrl))
        {
            await JS.InvokeVoidAsync("chatGroupMessage.showOffcanvas", EzOffcanvasDomId, show);
        }
        else
        {
            await JS.InvokeVoidAsync("eSc.openInNewTab", $"{RedirectUrl}");
        }
    }

}

@*
<button class="btn btn-primary mb-1 mt-2" type="button" data-bs-toggle="offcanvas" data-bs-target="#@EzOffcanvasDomId" aria-controls="@EzOffcanvasDomId">Right Offcanvas</button>
@ButtonTitle
*@
<button class="btn btn-@(CObj.IsStk?"danger":"info bg-primary-light") rounded-circle icon-md show fixed-bottom p-3 chatBoxOffcanvasIconBtn @(Visable?"":"d-none") @((HideWithMobile && CObj.IsMobile)?"d-none":"")" type="button" @onclick="()=>{_=ShowOffcanvas(true);}" style="height:38px;width:38px;">
    <i class="@_Icon position-absolute top-50 start-50 translate-middle"></i>
    <span class="ping-icon-wrapper position-absolute bottom-0 end-0 ts5">
        <span class="ping-icon-bg"></span>
        <span class="fa-solid fa-circle ping-icon"></span>
    </span>
</button>
<div class="offcanvas offcanvas-end @CssClass" id="@EzOffcanvasDomId" tabindex="-1">
    @if (HeaderVisable)
    {
        <div class="offcanvas-header">
            <h5>
                @Title
            </h5>
            <button class="btn-close text-reset" type="button" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
    }
    <div class="offcanvas-body @(Overflow_YHidden?"overflow-y-hidden":"")">
        <CascadingValue Value="EzOffcanvasDomId">
            @ChildContent
        </CascadingValue>
    </div>
</div>
<!-- Right Offcanvas-->
