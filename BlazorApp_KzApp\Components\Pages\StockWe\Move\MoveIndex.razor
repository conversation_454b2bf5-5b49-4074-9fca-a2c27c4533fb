@using BlazorApp_KzApp.Components.Pages.ChatGroupMessages
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using BlazorApp_KzApp.Components.Pages.ZBK
@inject IJSRuntime JS

@page "/Move/{param?}"

@code {
	[Parameter]
	public string param { get; set; } = "";

	[CascadingParameter]
	public BlzHelperObj? CObj { get; set; }

	string SelectedStock { get; set; } = "";

	_CandlestickChart _Candlestick;
	_MoversSelector _moversSelector;

	protected override async Task OnInitializedAsync()
	{
		await Task.CompletedTask;
		//return base.OnInitializedAsync();
	}

	protected override async Task OnAfterRenderAsync(bool firstRender)
	{
		if (firstRender && !string.IsNullOrEmpty(SelectedStock))
		{
			await OnSelectStock(SelectedStock);
		}
	}

	async Task OnSendTickMinEvent()
	{
		DateTime time = DateTime.Now;
		if (time.Minute == 0 || time.Minute == 10)
		{
			_ = _moversSelector.Init();
		}
		_ = _Candlestick.Init(SelectedStock, true);
		_ = _Candlestick.SecTickEvent();
		await JS.InvokeVoidAsync("console.log", $"{nameof(OnSendTickMinEvent)}:{DateTime.Now:f}");
		await Task.Delay(0);
	}

	async Task OnSelectStock(string stock)
	{
		SelectedStock = stock.ToUpper();
		if (_Candlestick is not null && !string.IsNullOrEmpty(SelectedStock))
		{
			await _Candlestick.Init(SelectedStock);
		}
		if (left_Right_Framework is not null)
		{
			await left_Right_Framework.CloseLeftContent();
		}
		await Task.CompletedTask;
	}

	_UI_Left_Right_Framework left_Right_Framework;

}

<Main_Framework ContentClass="mb-0">
	<_UI_Left_Right_Framework ChildContentClass="position-sticky" ChildContentStyle="top:30px" LeftContentBorder="false" @ref=@left_Right_Framework>
		<LeftContent>
			<_MoversSelector @ref="_moversSelector" OnSelectedStock="@OnSelectStock" />
		</LeftContent>
		<ChildContent>
			<_CandlestickChart DefaultSelectedMultiplier="eZeroCore.Web.Stk.APIData.Multiplier.Min_1" @ref=@_Candlestick />
		</ChildContent>
	</_UI_Left_Right_Framework>

	<_FooterMenus ActivateIndex="0" />

	<ChatGroupMsgOffcanvas RedirectUrl="/chat" OnSendTickMin="OnSendTickMinEvent" />
</Main_Framework>