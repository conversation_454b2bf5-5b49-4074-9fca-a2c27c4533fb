﻿@inject IJSRuntime JS
@code {
    [Parameter]
    public eZeroCore.Web.Column.ContentType SelectedColType { get; set; } = eZeroCore.Web.Column.ContentType.Article;

    [Parameter]
    public EventCallback<eZeroCore.Web.Column.ContentType> OnSelected { get; set; }

    [Parameter]
    public bool Disabled { get; set; }

    [Parameter]
    public bool InDialog { get; set; }

    [Parameter]
    public int UserGroupLv { get; set; }


    string? BtnTitleType;
    int GetTypeValue(eZeroCore.Web.Column.ContentType type)
    {
        return (int)type;
    }
    string btnTitleClorClassName = "btn-check p-1";

    async void HandleRadioChange(ChangeEventArgs e)
    {
        int iBtnTitleType = eZeroCore.ENumeric.ConvertToInt(e.Value?.ToString() ?? "0");
        await OnSelected.InvokeAsync((eZeroCore.Web.Column.ContentType)iBtnTitleType);
    }
}

<div>
    <div class="btn-group btn-group-sm" role="group">
        @{
            int typeValue = GetTypeValue(eZeroCore.Web.Column.ContentType.Article);
            bool disabledBtn = false;
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleType)"
               id="@($"{nameof(BtnTitleType)}{InDialog}{typeValue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@typeValue" checked="@((int)SelectedColType== typeValue)">
        <label class="btn btn-outline-secondary @(Disabled?"disabled":"")" for="@($"{nameof(BtnTitleType)}{InDialog}{typeValue}")">
            @(((eZeroCore.Web.Column.ContentType)typeValue).ToString())
        </label>

        @{
            typeValue = GetTypeValue(eZeroCore.Web.Column.ContentType.Photos);
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleType)"
               id="@($"{nameof(BtnTitleType)}{InDialog}{typeValue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@typeValue" checked="@((int)SelectedColType== typeValue)">
        <label class="btn btn-outline-secondary @(Disabled?"disabled":"")" for="@($"{nameof(BtnTitleType)}{InDialog}{typeValue}")">
            @(((eZeroCore.Web.Column.ContentType)typeValue).ToString())
        </label>

        @{
            typeValue = GetTypeValue(eZeroCore.Web.Column.ContentType.GoodsAndServices);
            disabledBtn = UserGroupLv < 2;
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleType)"
               id="@($"{nameof(BtnTitleType)}{InDialog}{typeValue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@typeValue" checked="@((int)SelectedColType== typeValue)">
        <label class="btn btn-outline-secondary @(Disabled || disabledBtn?"disabled":"")" for="@($"{nameof(BtnTitleType)}{InDialog}{typeValue}")">
            @(((eZeroCore.Web.Column.ContentType)typeValue).ToString())
        </label>

        @{
            typeValue = GetTypeValue(eZeroCore.Web.Column.ContentType.Video);
        }
        <input type="radio" class="@btnTitleClorClassName" name="@nameof(BtnTitleType)"
               id="@($"{nameof(BtnTitleType)}{InDialog}{typeValue}")" onchange="@HandleRadioChange"
               autocomplete="off" value="@typeValue" checked="@((int)SelectedColType== typeValue)">
        <label class="btn btn-outline-secondary @(Disabled || disabledBtn?"disabled":"")" for="@($"{nameof(BtnTitleType)}{InDialog}{typeValue}")">
            @(((eZeroCore.Web.Column.ContentType)typeValue).ToString())
        </label>

    </div>
</div>