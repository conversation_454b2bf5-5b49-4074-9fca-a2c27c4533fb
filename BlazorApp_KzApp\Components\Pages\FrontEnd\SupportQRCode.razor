﻿

@code {
    [CascadingParameter]
    private IMudDialogInstance MudDialog { get; set; }

    private void Submit() => MudDialog.Close(DialogResult.Ok(true));

    private void Cancel() => MudDialog.Cancel();
}

<MudDialog>
    <DialogContent>
        
        <img src="https://stockwewebfiles.blob.core.windows.net/web-202411-stk/543948842mceclip1.png" />

    </DialogContent>
    <DialogActions>
        <MudButton Color="Color.Primary" OnClick="Submit">
            确定
        </MudButton>
    </DialogActions>
</MudDialog>