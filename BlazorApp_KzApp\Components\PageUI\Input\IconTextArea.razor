@using Microsoft.AspNetCore.Components
@using <PERSON><PERSON><PERSON>
@inject IJSRuntime JS

<div class="position-relative w-100">
    <RadzenTextArea Value="@Value"
                    @ref="textAreaRef"
                    ValueChanged="@OnValueChanged"
                    Placeholder="@Placeholder"
                    MaxLength="@MaxLength"
                    ReadOnly="@ReadOnly"
                    Rows="@Rows"
                    Style="@Style"
                    Name="@Name"
                    class="@(InputClass + " w-100 ps-5")" />
    @if (!string.IsNullOrEmpty(IconClass))
    {
        <i class="@IconClass position-absolute top-1 start-0 translate-middle-y ps-3 mt-3 text-muted"></i>
    }
</div>

@code {

    private RadzenTextArea? textAreaRef;

    public async Task FocusAsync()
    {
        if (textAreaRef != null)
        {
            await Task.Delay(300); // Optional delay to ensure the element is ready
            // Use the injected JSRuntime instance to call the JavaScript function.
            await JS.InvokeVoidAsync("eSc.focusInput", textAreaRef.Element);
        }
    }

    [Parameter] public string? Value { get; set; }

    [Parameter] public EventCallback<string?> ValueChanged { get; set; }

    [Parameter] public string? Placeholder { get; set; }

    [Parameter] public string? IconClass { get; set; }

    [Parameter] public int MaxLength { get; set; } = 1000;

    [Parameter] public int Rows { get; set; } = 3;

    [Parameter] public bool ReadOnly { get; set; } = false;

    [Parameter] public string? Name { get; set; }

    [Parameter] public string? InputClass { get; set; }

    [Parameter] public string? Style { get; set; }

    private async Task OnValueChanged(string? newValue)
    {
        Value = newValue;
        await ValueChanged.InvokeAsync(newValue);
    }
}
