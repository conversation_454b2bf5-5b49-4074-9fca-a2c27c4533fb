﻿@using BlazorApp_KzApp.Components.Pages.ChatGroupMessages
@using BlazorApp_KzApp.Components.Pages.FrontEnd.Mobile
@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.Pages.FrontEnd
@using eZeroCore.Users
@inject IJSRuntime JS
@inject IDialogService DialogService
@* @inject VisitLimitService VisitService *@

@page "/StockData/{param?}"

@code {
    [Parameter]
    public string param { get; set; } = "";

    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    string DefaultStock = "tsla";
    
    // 添加当前选中的股票代号状态变量
    string CurrentStock = "tsla";

    async Task OnSendTickSecEvent()
    {
        await Task.CompletedTask;
    }

    async Task OnSearchStock(string stock)
    {
        if (string.IsNullOrWhiteSpace(stock))
        {
            stock = DefaultStock;
        }
        
        // 更新当前股票代号
        CurrentStock = stock.ToUpper();
        
        if (_Stock_Analyst_Rating is not null)
        {
            await _Stock_Analyst_Rating.Init(stock);
        }
        
        // 刷新 Tb_AnalystPreview 组件数据
        if (_tableComponent is not null)
        {
            await _tableComponent.RefreshData(CurrentStock);
        }
        
        // 触发页面重新渲染
        StateHasChanged();
    }

    _StockAnalystRating _Stock_Analyst_Rating;

    _TbAnalystPreview _tableComponent;
}

<Main_Framework ContentClass="mb-0">

    <CascadingValue Value="@CObj">
        <CascadingValue Value="DefaultStock">
            <_BlockDarkPoolDayMinute OnSearchStock="@OnSearchStock" />
            <_StockAnalystRating @ref=@_Stock_Analyst_Rating />
        </CascadingValue>
        
        <_TbAnalystPreview @ref="_tableComponent"
                           StockCodeString="@CurrentStock" />

        <_StockDataNews StockCodeString="@CurrentStock" />
        
        <ChatGroupMsgOffcanvas RedirectUrl="/chat" OnSendTickMin="OnSendTickSecEvent" />

        <_FooterMenus />

        <_FooterV2></_FooterV2>
    </CascadingValue>
</Main_Framework>
