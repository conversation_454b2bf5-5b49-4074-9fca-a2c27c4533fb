﻿@using BlazorApp_KzApp.Components.Pages.ZBK
@using BlazorApp_KzApp.Components.Pages.ZBK.Docs
@inject ISnackbar SnackbarObj
@inject IDialogService DialogService
@inject IJSRuntime JS

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    eZeroCore.Web.Column ColObj { get; set; } = new();

    protected override Task OnInitializedAsync()
    {
        if (CObj.ThisColumn is not null && !string.IsNullOrEmpty(CObj.ThisColumn.ColumnIdTree))
        {
            ColObj = new(CObj.ThisColumn.ColumnId);
        }
        return base.OnInitializedAsync();
    }

    async Task UploadResult(List<eZeroCore.Azure.UploadResult> uploadResults)
    {
        var resultImage = uploadResults.FirstOrDefault();
        if (resultImage is not null)
        {
            string oldImage = ColObj.TitlePhotoUrls;
            ColObj.TitlePhotoUrls = resultImage.StoredFileUrl;
            StateHasChanged();
            bool? result = await DialogService.ShowMessageBox(
              "", CObj.UserLang.GetLangValue($"使用此Banner替换."), yesText: $"{CObj.UserLang.GetLangValue("确定")}", cancelText: CObj.UserLang.GetLangValue("取消"));
            if (result is not null && result.Value)
            {
                await ColObj.SaveAsync();
            }
            else
            {
                ColObj.TitlePhotoUrls = oldImage;
                StateHasChanged();
            }
        }
    }

}

<div class="position-relative">
    <div class="card shadow-none border my-2 ratio ratio-32x9 bgImg"
         style="background-image:url(@ColObj.TitlePhotoUrls)"></div>
    @if (CObj.UserIsAdmin)
    {
        <div class="position-absolute top-0 end-0 mx-3 my-2">
            <MudTooltip Color="Color.Primary" Text="@CObj.UserLang.GetLangValue("修改Banner")" Arrow="true" Placement="Placement.Right">
                <Uploader CallBack="UploadResult"
                          FileType="eZeroCore.Azure.UploadResult.FileType.Image"
                          ShowProgressPercentToButton="false"
                          Class=""
                          PanelClass="btn text-start px-0"
                          AllowMultiple=false>
                    <div class="avatar avatar-m avatar-bordered">
                        <div class="avatar-name rounded-circle bg-warning-lighter text-white">
                            <i class="fa-solid fa-pencil fs-9 mt-2"></i>
                        </div>
                    </div>
                </Uploader>
            </MudTooltip>
        </div>
    }

</div>


<_DocListVideoStk ColObj="ColObj" />