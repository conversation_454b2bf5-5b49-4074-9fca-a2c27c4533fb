@code {
    [Parameter] public string? TimeText { get; set; }
    [Parameter] public string? TimeFormat { get; set; } = "MM/dd/yyyy HH:mm";
    [Parameter] public DateTime? TimeValue { get; set; }
    
    [Parameter] public string ContainerClass { get; set; } = "";
    [Parameter] public string ContainerStyle { get; set; } = "";
    
    [Parameter] public string HeaderInfoClass { get; set; } = "";
    [Parameter] public string HeaderInfoStyle { get; set; } = "";
    
    [Parameter] public string TimeBadgeClass { get; set; } = "badge badge-phoenix badge-phoenix-info";
    [Parameter] public string TimeBadgeStyle { get; set; } = "font-size:12px; padding:1px 4px;";
    
    [Parameter] public RenderFragment? CustomContent { get; set; }
    [Parameter] public string CustomContentClass { get; set; } = "";
    [Parameter] public string CustomContentStyle { get; set; } = "";
    
    [Parameter] public RenderFragment? RightContent { get; set; }

    private string GetDisplayTime()
    {
        if (!string.IsNullOrEmpty(TimeText))
            return TimeText;
        
        if (TimeValue.HasValue)
            return TimeValue.Value.ToString(TimeFormat);
            
        return "--";
    }
}

<div class="@ContainerClass" 
     style="@($"display:flex; justify-content:space-between; align-items:center; margin-bottom:4px; {ContainerStyle}".Trim())">
    
    <div class="@HeaderInfoClass" 
         style="@($"display:flex; align-items:center; gap:8px; {HeaderInfoStyle}".Trim())">
        
        <!-- 时间徽章 -->
        <span class="@TimeBadgeClass" style="@TimeBadgeStyle">
            @GetDisplayTime()
        </span>
        
        <!-- 自定义内容插槽 -->
        @if (CustomContent != null)
        {
            <div class="@CustomContentClass" 
                 style="@($"font-size:12px; font-weight:500; color:white; word-wrap: break-word; word-break: break-all; line-height: 1.2; {CustomContentStyle}".Trim())">
                @CustomContent
            </div>
        }
    </div>
    
    <!-- 右侧内容插槽 -->
    @if (RightContent != null)
    {
        <div>
            @RightContent
        </div>
    }
</div> 