
@code {

    eZeroCore.Web.Column colObj = new();

    List<eZeroCore.Web.Column> cols = [];
    protected override async Task OnInitializedAsync()
    {
        string columnid = "COL2FA71506EB";
        colObj = new(columnid);
        eZeroCore.Web.ColumnList list = new()
            {
                ParentId = columnid,
            };
        cols = await list.ListAsync();
    }
}


<section class="pb-0 pb-md-2 mt-0 mt-md-n10">

    <div class="container">
        <div class="text-center mb-5 position-relative">
            <h6 class="text-info mb-3">
                @colObj.ColumnContentSummary
            </h6>
            <h5 class="mb-2 lh-base">
                @colObj.ColumnName
            </h5>
        </div>
        <div class="row gx-5">

            @foreach (eZeroCore.Web.Column column in cols)
            {
                <div class="col-12 col-md-4 py-1 py-md-3">
                    <div class="card text-start py-2 py-md-5 px-5">
                        <h4 class="fw-semibold fst-italic d-none d-md-block">
                            <i class="fa-solid fa-quote-left"></i>
                        </h4>
                        <h6 class="mb-3">
                            @column.ColumnContentSummary
                        </h6>
                        <div>
                            @(new MarkupString(column.ColumnContent))
                        </div>
                        <div class="d-flex align-items-center gap-3 mt-3">
                            <div class="avatar avatar-m ms-0">
                                <img class="rounded-circle border border-2 border-primary" src="@column.TitlePhotoUrls" alt="" />
                            </div>
                            <div class="text-start">
                                <p class="mb-0">
                                    @column.ColumnName
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            }
           
           
        </div>
    </div>
    <!-- end of .container-->

</section>