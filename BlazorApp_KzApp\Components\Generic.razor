@code {

    protected override bool ShouldRender()
    {
        return false;
    }

    public static void AlertInfo(ISnackbar SnackbarObj, string info, MudUI.SnackbarType type= MudUI.SnackbarType.Alert)
    {
        MudUI.DisplayMessage(SnackbarObj, info, type);
    }

    public static async Task Loading(IJSRuntime JS, bool openLoading = true, string text = "Loading...", bool showIcon = true)
    {
        #region...
        await Js(JS, "eLoadingBody", !openLoading, text, showIcon);
        #endregion
    }

    public static async Task LoadingDom(IJSRuntime JS, string domId, bool openLoading = true, string text = "Loading...", bool showIcon = true)
    {
        #region...
        await Js(JS, "eLoadingDomId", domId, !openLoading, text, showIcon);
        #endregion
    }

    public static async Task FullLoading(IJSRuntime JS)
    {
        await Js(JS, "$(\"#eZlogo-overlay\").show");
    }

    //await Js(JS, "$(\"#eZlogo-overlay\").show", domId, !openLoading, text, showIcon);

    public static async Task Focus(IJSRuntime JS, ElementReference? FocusObj, int delay = 600)
    {
        #region...
        if (FocusObj is not null)
        {
            await Task.Delay(delay);
            await Js(JS, "eSc.focusInput", FocusObj);
        }
        #endregion
    }
    public static async Task FocusForDomId(IJSRuntime JS,string domId, int delay = 600)
    {
        #region...
        if (!string.IsNullOrWhiteSpace(domId))
        {
            await Task.Delay(delay);
            await Js(JS, "eSc.focusInputForDomId", domId);
        }
        #endregion
    }

    public static async Task Js(IJSRuntime JS, string identifier, params object?[]? args)
    {
        #region...
        try
        {
            await JS.InvokeVoidAsync(identifier, args);
        }
        catch (Exception err)
        {
            err.ToString();
        }
        #endregion
    }

    public static async Task JsConsole(IJSRuntime JS, params object?[]? args)
    {
        await Js(JS, "console.log", args);
    }

    public static async Task JsReload(IJSRuntime JS, int delay = 0)
    {
        if (delay > 0)
        {
            await Task.Delay(delay);
        }
        await Js(JS, "location.reload");
    }

    public static async Task Copy(IJSRuntime JS, string text)
    {
        await Js(JS, "Blz.copyToClipboard", text);
    }

    public static string GetPath(string? value)
    {
        string result = value ?? "";
        if (!eZeroCore.AppSettings.IsTestModeAll && !string.IsNullOrEmpty(result))
        {
            result = result.Split('.').LastOrDefault() ?? "";
        }
        return result;
    }

}

