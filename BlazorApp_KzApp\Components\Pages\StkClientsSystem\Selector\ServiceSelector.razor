@using eZeroCore.Web.Stk;

@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }


    [Parameter]
    public EventCallback<int> OnSelectedIndex { get; set; }

    [Parameter]
    public ClientManager.StockClient.ServiceEnum SelectedService { get; set; } = ClientManager.StockClient.ServiceEnum.None;

    protected override async Task OnInitializedAsync()
    {
        await Init();
    }

    async Task Init()
    {
        SelectedTitle = GetStatusItemName(SelectedService);
        StateHasChanged();
        await Task.Delay(0);
    }

    string SelectedTitle { get; set; } = "";
    int LastSelectedIndex { get; set; } = -1;

    async Task StatusSelected(int i)
    {
        if (i != LastSelectedIndex)
        {
            await OnSelectedIndex.InvokeAsync(i);
            SelectedService = (ClientManager.StockClient.ServiceEnum)i;
            await Init();
            LastSelectedIndex = i;
        }
    }
    List<(int v, string t)> StatusValueItems()
    {
        List<(int v, string t)> items = [];
        for (int i = 0; i < Enum.GetNames(typeof(ClientManager.StockClient.ServiceEnum)).Length - 1; i++)
        {
            string sItemStr = ((ClientManager.StockClient.ServiceEnum)i).ToString();
            if (!sItemStr.StartsWith('_') || sItemStr.EndsWith('_'))
            {
                items.Add((i, sItemStr.Replace("__", "-")));
            }
        }
        return items;
    }
    public static string GetStatusItemName(ClientManager.StockClient.ServiceEnum enumItem, string sNoneName = "&nbsp;")
    {
        int iValue = Convert.ToInt32(enumItem);
        if (iValue == -1)
        {
            return sNoneName;
        }
        return ((ClientManager.StockClient.ServiceEnum)iValue).ToString().Replace("__", "-");
    }
}

<div class="dropdown">
    <div class="dropdown-toggle w-100 hand" data-bs-toggle="dropdown" aria-expanded="false">
        @(new MarkupString(SelectedTitle))
    </div>
    <ul class="dropdown-menu" style="max-height:300px; overflow-y: auto;">
        @foreach ((int v, string t) item in StatusValueItems())
        {
            <li @key=item.GetHashCode()>
                <button class="dropdown-item @(item.v == Convert.ToInt32(SelectedService)?"Active":"") v@(item.v)" type="button" @onclick="()=>{_=StatusSelected(item.v);}">
                    @item.t
                    <sub class="opacity-25">
                        @(item.v)
                    </sub>
                </button>
            </li>
        }
    </ul>
</div>

