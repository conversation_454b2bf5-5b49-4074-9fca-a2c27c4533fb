﻿@using System.Text.Json
@using System.Text.Json.Serialization
@using eZeroCore.Web.Stk
@inject IJSRuntime JS
@code {
    [CascadingParameter]
    public BlzHelperObj? CObj { get; set; }

    [Parameter]
    public string NoDataInfoColumnId { get; set; } = "";

    private List<Stock_Price_Minute> stockPrices = [];

    [Parameter]
    public eZeroCore.Web.Stk.APIData.Multiplier DefaultSelectedMultiplier { get; set; } = APIData.Multiplier.Min_1;

    bool UseAPI { get; set; }

    string Stock { get; set; } = "";

    string DomId = $"mainKline_Stock_Price_Minute";

    eZeroCore.Web.Stk.APIData.Multiplier selectedMultiplier = APIData.Multiplier.Min_1;

    string NoDataInfo = "";
    protected override Task OnInitializedAsync()
    {
        selectedMultiplier = DefaultSelectedMultiplier;

        if (!string.IsNullOrEmpty(NoDataInfoColumnId))
        {
            eZeroCore.Web.Column col = new(NoDataInfoColumnId);
            NoDataInfo = col.ColumnContent;
        }

        return base.OnInitializedAsync();
    }

    public async Task Init(string stock, bool update = false, DateTime? start = null)
    {
        #region..
        Stock = stock.ToUpper();
        if (!string.IsNullOrEmpty(Stock))
        {
            if (!update)
            {
                await Generic.LoadingDom(JS, DomId);
            }
            Stock_Price_Minute.StockList stockList = new();

            DateTime? end = null;
            if (start is not null)
            {
                end = start.Value;
            }

            stockPrices = await Stock_Price_Minute.StockList.GetStocksAsync(Stock, selectedMultiplier, start: start, end: end);
            var jsonData = JsonSerializer.Serialize(stockPrices, EString.JsonDefaultOption);
            await Task.Delay(100);
            await JS.InvokeVoidAsync($"Stktb.{(update ? "updateChartK" : "renderChartK")}", jsonData, Stock, Convert.ToInt32(selectedMultiplier) <= 3);
            await Generic.LoadingDom(JS, DomId, false);
            UseAPI = stockPrices.Count > 0 && stockPrices.FirstOrDefault()!.UseAPI;
        }
        if (!update)
        {
            StateHasChanged();
        }

        #endregion
    }

    _Timestamp timestamp;
    public async Task SecTickEvent(DateTime? date = null)
    {
        if (timestamp is not null)
        {
            await timestamp.TimerTaskEvent(date);
        }
    }

    async Task SelectMulitiplier(int index)
    {
        selectedMultiplier = (APIData.Multiplier)index;
        await Init(Stock);
    }
}

<_UIFramework Class="mt-0" HeaderContentClass="d-flex justify-content-start align-items-center">
    <HeaderContent>

        @if (!string.IsNullOrEmpty(Stock))
        {
            <div class="py-2">
                <label class="badge text-bg-danger bg-warning px-2 mb-1 d-block p-2 fs-8">
                    @Stock.ToUpper()
                </label>
                @*
                    <_Timestamp @ref=timestamp />
                *@
            </div>
            <div class="px-2">

                <div class="btn-group btn-group-sm" role="group">

                    @foreach (int i in Enum.GetValues(typeof(eZeroCore.Web.Stk.APIData.Multiplier)))
                    {
                        <input @onclick="async ()=>{ await SelectMulitiplier(i); }" type="radio" class="btn-check" name="btnradio" id="@($"btnradioMuliti{i}")" autocomplete="off" checked="@(i==Convert.ToInt32(selectedMultiplier)?true:false)">
                        <label class="btn btn-phoenix-info @(i%2!=0?"d-none d-md-block":"")" for="@($"btnradioMuliti{i}")">
                            @CObj.UserLang.GetLangValue(((eZeroCore.Web.Stk.APIData.Multiplier)i).ToString())
                        </label>
                    }

                </div>

            </div>
        }

    </HeaderContent>
    <ChildContent>
        <div id="@DomId" class="eChartDom" style="width: 100%; height: calc(100vh - 150px);"></div>
        @if (stockPrices.Count < 1)
        {
            <div class="position-absolute top-0 start-50 translate-middle-x text-center p-5 m-5 mt-10">
                @if (string.IsNullOrEmpty(NoDataInfo))
                {
                    <p>
                        该代号暂时没有新数据
                    </p>
                }
                else
                {
                    @(new MarkupString(NoDataInfo))
                }
            </div>
        }
    </ChildContent>
</_UIFramework>

